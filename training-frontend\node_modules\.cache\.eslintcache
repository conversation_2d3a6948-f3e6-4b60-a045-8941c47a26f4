[{"D:\\ASL\\training-frontend\\src\\index.js": "1", "D:\\ASL\\training-frontend\\src\\App.js": "2", "D:\\ASL\\training-frontend\\src\\reportWebVitals.js": "3", "D:\\ASL\\training-frontend\\src\\components\\HomePage.js": "4", "D:\\ASL\\training-frontend\\src\\components\\TrainingPage.js": "5", "D:\\ASL\\training-frontend\\src\\components\\AboutPage.js": "6", "D:\\ASL\\training-frontend\\src\\components\\ContactPage.js": "7"}, {"size": 535, "mtime": 1751014435070, "results": "8", "hashOfConfig": "9"}, {"size": 2014, "mtime": 1751019716269, "results": "10", "hashOfConfig": "9"}, {"size": 362, "mtime": 1751014435142, "results": "11", "hashOfConfig": "9"}, {"size": 16625, "mtime": 1751019716175, "results": "12", "hashOfConfig": "9"}, {"size": 33209, "mtime": 1751108740796, "results": "13", "hashOfConfig": "9"}, {"size": 6851, "mtime": 1751019090768, "results": "14", "hashOfConfig": "9"}, {"size": 10870, "mtime": 1751019063718, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e98u2r", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ASL\\training-frontend\\src\\index.js", [], [], "D:\\ASL\\training-frontend\\src\\App.js", [], [], "D:\\ASL\\training-frontend\\src\\reportWebVitals.js", [], [], "D:\\ASL\\training-frontend\\src\\components\\HomePage.js", ["37", "38", "39", "40"], [], "D:\\ASL\\training-frontend\\src\\components\\TrainingPage.js", ["41"], [], "D:\\ASL\\training-frontend\\src\\components\\AboutPage.js", ["42", "43", "44", "45"], [], "D:\\ASL\\training-frontend\\src\\components\\ContactPage.js", [], [], {"ruleId": "46", "severity": 1, "message": "47", "line": 6, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 6, "endColumn": 8}, {"ruleId": "46", "severity": 1, "message": "50", "line": 14, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 14, "endColumn": 6}, {"ruleId": "46", "severity": 1, "message": "51", "line": 15, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 15, "endColumn": 8}, {"ruleId": "46", "severity": 1, "message": "52", "line": 16, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 16, "endColumn": 13}, {"ruleId": "46", "severity": 1, "message": "53", "line": 11, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 11, "endColumn": 6}, {"ruleId": "46", "severity": 1, "message": "50", "line": 10, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 10, "endColumn": 6}, {"ruleId": "46", "severity": 1, "message": "54", "line": 11, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 11, "endColumn": 8}, {"ruleId": "46", "severity": 1, "message": "53", "line": 12, "column": 3, "nodeType": "48", "messageId": "49", "endLine": 12, "endColumn": 6}, {"ruleId": "46", "severity": 1, "message": "55", "line": 224, "column": 7, "nodeType": "48", "messageId": "49", "endLine": 224, "endColumn": 19}, "no-unused-vars", "'Users' is defined but never used.", "Identifier", "unusedVar", "'Eye' is defined but never used.", "'Award' is defined but never used.", "'TrendingUp' is defined but never used.", "'Zap' is defined but never used.", "'Globe' is defined but never used.", "'SectionTitle' is assigned a value but never used."]