/*! For license information please see main.7993d62a.js.LICENSE.txt */
(()=>{var e={4:(e,t,n)=>{"use strict";var r=n(853),a=n(43),o=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function c(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(l(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),k=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),_=Symbol.for("react.memo"),C=Symbol.for("react.lazy");Symbol.for("react.scope");var O=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var T=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var R=Symbol.iterator;function P(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=R&&e[R]||e["@@iterator"])?e:null}var A=Symbol.for("react.client.reference");function j(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===A?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case v:return"Profiler";case y:return"StrictMode";case S:return"Suspense";case E:return"SuspenseList";case O:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case k:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case x:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case _:return null!==(t=e.displayName||null)?t:j(e.type)||"Memo";case C:t=e._payload,e=e._init;try{return j(e(t))}catch(n){}}return null}var N=Array.isArray,z=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},F=[],M=-1;function I(e){return{current:e}}function U(e){0>M||(e.current=F[M],F[M]=null,M--)}function B(e,t){M++,F[M]=e.current,e.current=t}var H=I(null),q=I(null),W=I(null),$=I(null);function V(e,t){switch(B(W,t),B(q,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=od(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}U(H),B(H,e)}function K(){U(H),U(q),U(W)}function Q(e){null!==e.memoizedState&&B($,e);var t=H.current,n=od(t,e.type);t!==n&&(B(q,e),B(H,n))}function Y(e){q.current===e&&(U(H),U(q)),$.current===e&&(U($),Qd._currentValue=D)}var G=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,J=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,oe=r.unstable_NormalPriority,ie=r.unstable_LowPriority,se=r.unstable_IdlePriority,le=r.log,ce=r.unstable_setDisableYieldValue,ue=null,de=null;function fe(e){if("function"===typeof le&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/me|0)|0},he=Math.log,me=Math.LN2;var ge=256,ye=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,o=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var s=134217727&r;return 0!==s?0!==(r=s&~o)?a=ve(r):0!==(i&=s)?a=ve(i):n||0!==(n=s&~e)&&(a=ve(n)):0!==(s=r&~o)?a=ve(s):0!==i?a=ve(i):n||0!==(n=r&~e)&&(a=ve(n)),0===a?0:0!==t&&t!==a&&0===(t&o)&&((o=a&-a)>=(n=t&-t)||32===o&&0!==(4194048&n))?t:a}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function ke(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function xe(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ye;return 0===(62914560&(ye<<=1))&&(ye=4194304),e}function Ee(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function _e(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ce(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Oe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Te(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Re(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Pe(){var e=L.p;return 0!==e?e:void 0===(e=window.event)?32:uf(e.type)}var Ae=Math.random().toString(36).slice(2),je="__reactFiber$"+Ae,Ne="__reactProps$"+Ae,ze="__reactContainer$"+Ae,Le="__reactEvents$"+Ae,De="__reactListeners$"+Ae,Fe="__reactHandles$"+Ae,Me="__reactResources$"+Ae,Ie="__reactMarker$"+Ae;function Ue(e){delete e[je],delete e[Ne],delete e[Le],delete e[De],delete e[Fe]}function Be(e){var t=e[je];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ze]||n[je]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[je])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[je]||e[ze]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function We(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Ie]=!0}var Ve=new Set,Ke={};function Qe(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for(Ke[e]=t,e=0;e<t.length;e++)Ve.add(t[e])}var Ge,Xe,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,G.call(et,a)||!G.call(Ze,a)&&(Je.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ge)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ge=t&&t[1]||"",Xe=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ge+e+Xe}var ot=!1;function it(e,t){if(!e||ot)return"";ot=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(o){r=o}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(s){if(s&&r&&"string"===typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=r.DetermineComponentFrameRoot(),i=o[0],s=o[1];if(i&&s){var l=i.split("\n"),c=s.split("\n");for(a=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===l.length||a===c.length)for(r=l.length-1,a=c.length-1;1<=r&&0<=a&&l[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(l[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||l[r]!==c[a]){var u="\n"+l[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{ot=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function st(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return at("Activity");default:return""}}function lt(e){try{var t="";do{t+=st(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ht=/[\n"\\]/g;function mt(e){return e.replace(ht,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,a,o,i,s){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?vt(e,i,ct(t)):null!=n?vt(e,i,ct(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=o&&(e.defaultChecked=!!o),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.name=""+ct(s):e.removeAttribute("name")}function yt(e,t,n,r,a,o,i,s){if(null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.type=o),null!=t||null!=n){if(!("submit"!==o&&"reset"!==o||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,s||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function kt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(N(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function xt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Et(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function _t(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&Et(e,a,r)}else for(var o in t)t.hasOwnProperty(o)&&Et(e,o,t[o])}function Ct(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ot=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Tt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Rt(e){return Tt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Pt=null;function At(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var jt=null,Nt=null;function zt(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Ne]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Ne]||null;if(!a)throw Error(i(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Lt=!1;function Dt(e,t,n){if(Lt)return e(t,n);Lt=!0;try{return e(t)}finally{if(Lt=!1,(null!==jt||null!==Nt)&&(Bc(),jt&&(t=jt,e=Nt,Nt=jt=null,zt(t),e)))for(t=0;t<e.length;t++)zt(e[t])}}function Ft(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Ne]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Mt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),It=!1;if(Mt)try{var Ut={};Object.defineProperty(Ut,"passive",{get:function(){It=!0}}),window.addEventListener("test",Ut,Ut),window.removeEventListener("test",Ut,Ut)}catch(zf){It=!1}var Bt=null,Ht=null,qt=null;function Wt(){if(qt)return qt;var e,t,n=Ht,r=n.length,a="value"in Bt?Bt.value:Bt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return qt=a.slice(e,1<t?1-t:void 0)}function $t(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Vt(){return!0}function Kt(){return!1}function Qt(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Vt:Kt,this.isPropagationStopped=Kt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vt)},persist:function(){},isPersistent:Vt}),t}var Yt,Gt,Xt,Jt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Qt(Jt),en=f({},Jt,{view:0,detail:0}),tn=Qt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xt&&(Xt&&"mousemove"===e.type?(Yt=e.screenX-Xt.screenX,Gt=e.screenY-Xt.screenY):Gt=Yt=0,Xt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Gt}}),rn=Qt(nn),an=Qt(f({},nn,{dataTransfer:0})),on=Qt(f({},en,{relatedTarget:0})),sn=Qt(f({},Jt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Qt(f({},Jt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),cn=Qt(f({},Jt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function hn(){return pn}var mn=Qt(f({},en,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=$t(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hn,charCode:function(e){return"keypress"===e.type?$t(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?$t(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Qt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Qt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hn})),vn=Qt(f({},Jt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Qt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Qt(f({},Jt,{newState:0,oldState:0})),kn=[9,13,27,32],xn=Mt&&"CompositionEvent"in window,Sn=null;Mt&&"documentMode"in document&&(Sn=document.documentMode);var En=Mt&&"TextEvent"in window&&!Sn,_n=Mt&&(!xn||Sn&&8<Sn&&11>=Sn),Cn=String.fromCharCode(32),On=!1;function Tn(e,t){switch(e){case"keyup":return-1!==kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Rn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Pn=!1;var An={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function jn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!An[e.type]:"textarea"===t}function Nn(e,t,n,r){jt?Nt?Nt.push(r):Nt=[r]:jt=r,0<(t=Wu(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zn=null,Ln=null;function Dn(e){Du(e,0)}function Fn(e){if(ft(qe(e)))return e}function Mn(e,t){if("change"===e)return t}var In=!1;if(Mt){var Un;if(Mt){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}Un=Bn}else Un=!1;In=Un&&(!document.documentMode||9<document.documentMode)}function qn(){zn&&(zn.detachEvent("onpropertychange",Wn),Ln=zn=null)}function Wn(e){if("value"===e.propertyName&&Fn(Ln)){var t=[];Nn(t,Ln,e,At(e)),Dt(Dn,t)}}function $n(e,t,n){"focusin"===e?(qn(),Ln=n,(zn=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&qn()}function Vn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Fn(Ln)}function Kn(e,t){if("click"===e)return Fn(t)}function Qn(e,t){if("input"===e||"change"===e)return Fn(t)}var Yn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Gn(e,t){if(Yn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!G.call(t,a)||!Yn(e[a],t[a]))return!1}return!0}function Xn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jn(e,t){var n,r=Xn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Mt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,or=null,ir=!1;function sr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},or&&Gn(or,r)||(or=r,0<(r=Wu(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},ur={},dr={};function fr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}Mt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var pr=fr("animationend"),hr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),yr=fr("transitionstart"),vr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xr(e,t){wr.set(e,t),Qe(t,[e])}kr.push("scrollEnd");var Sr=new WeakMap;function Er(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var _r=[],Cr=0,Or=0;function Tr(){for(var e=Cr,t=Or=Cr=0;t<e;){var n=_r[t];_r[t++]=null;var r=_r[t];_r[t++]=null;var a=_r[t];_r[t++]=null;var o=_r[t];if(_r[t++]=null,null!==r&&null!==a){var i=r.pending;null===i?a.next=a:(a.next=i.next,i.next=a),r.pending=a}0!==o&&jr(n,a,o)}}function Rr(e,t,n,r){_r[Cr++]=e,_r[Cr++]=t,_r[Cr++]=n,_r[Cr++]=r,Or|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Pr(e,t,n,r){return Rr(e,t,n,r),Nr(e)}function Ar(e,t){return Rr(e,null,null,t),Nr(e)}function jr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,o=e.return;null!==o;)o.childLanes|=n,null!==(r=o.alternate)&&(r.childLanes|=n),22===o.tag&&(null===(e=o.stateNode)||1&e._visibility||(a=!0)),e=o,o=o.return;return 3===e.tag?(o=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=o.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),o):null}function Nr(e){if(50<jc)throw jc=0,Nc=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var zr={};function Lr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,t,n,r){return new Lr(e,t,n,r)}function Fr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=Dr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ir(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ur(e,t,n,r,a,o){var s=0;if(r=e,"function"===typeof e)Fr(e)&&(s=1);else if("string"===typeof e)s=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case O:return(e=Dr(31,n,t,a)).elementType=O,e.lanes=o,e;case g:return Br(n.children,a,o,t);case y:s=8,a|=24;break;case v:return(e=Dr(12,n,t,2|a)).elementType=v,e.lanes=o,e;case S:return(e=Dr(13,n,t,a)).elementType=S,e.lanes=o,e;case E:return(e=Dr(19,n,t,a)).elementType=E,e.lanes=o,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case k:s=10;break e;case w:s=9;break e;case x:s=11;break e;case _:s=14;break e;case C:s=16,r=null;break e}s=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=Dr(s,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Br(e,t,n,r){return(e=Dr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=Dr(6,e,null,t)).lanes=n,e}function qr(e,t,n){return(t=Dr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],$r=0,Vr=null,Kr=0,Qr=[],Yr=0,Gr=null,Xr=1,Jr="";function Zr(e,t){Wr[$r++]=Kr,Wr[$r++]=Vr,Vr=e,Kr=t}function ea(e,t,n){Qr[Yr++]=Xr,Qr[Yr++]=Jr,Qr[Yr++]=Gr,Gr=e;var r=Xr;e=Jr;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var o=32-pe(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Xr=1<<32-pe(t)+a|n<<a|r,Jr=o+e}else Xr=1<<o|n<<a|r,Jr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Vr;)Vr=Wr[--$r],Wr[$r]=null,Kr=Wr[--$r],Wr[$r]=null;for(;e===Gr;)Gr=Qr[--Yr],Qr[Yr]=null,Jr=Qr[--Yr],Qr[Yr]=null,Xr=Qr[--Yr],Qr[Yr]=null}var ra=null,aa=null,oa=!1,ia=null,sa=!1,la=Error(i(519));function ca(e){throw ma(Er(Error(i(418,"")),e)),la}function ua(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[je]=e,t[Ne]=r,n){case"dialog":Fu("cancel",t),Fu("close",t);break;case"iframe":case"object":case"embed":Fu("load",t);break;case"video":case"audio":for(n=0;n<zu.length;n++)Fu(zu[n],t);break;case"source":Fu("error",t);break;case"img":case"image":case"link":Fu("error",t),Fu("load",t);break;case"details":Fu("toggle",t);break;case"input":Fu("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Fu("invalid",t);break;case"textarea":Fu("invalid",t),kt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Gu(t.textContent,n)?(null!=r.popover&&(Fu("beforetoggle",t),Fu("toggle",t)),null!=r.onScroll&&Fu("scroll",t),null!=r.onScrollEnd&&Fu("scrollend",t),null!=r.onClick&&(t.onclick=Xu),t=!0):t=!1,t||ca(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(sa=!1);case 27:case 3:return void(sa=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!oa)return da(e),oa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&aa&&ca(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,pd(e.type)?(e=vd,vd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,oa=!1}function ha(){var e=ia;return null!==e&&(null===bc?bc=e:bc.push.apply(bc,e),ia=null),e}function ma(e){null===ia?ia=[e]:ia.push(e)}var ga=I(null),ya=null,va=null;function ba(e,t,n){B(ga,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=ga.current,U(ga)}function ka(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xa(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var s=a.child;o=o.firstContext;e:for(;null!==o;){var l=o;o=a;for(var c=0;c<t.length;c++)if(l.context===t[c]){o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),ka(o.return,n,e),r||(s=null);break e}o=l.next}}else if(18===a.tag){if(null===(s=a.return))throw Error(i(341));s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),ka(s,n,e),s=null}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===e){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}}function Sa(e,t,n,r){e=null;for(var a=t,o=!1;null!==a;){if(!o)if(0!==(524288&a.flags))o=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var s=a.alternate;if(null===s)throw Error(i(387));if(null!==(s=s.memoizedProps)){var l=a.type;Yn(a.pendingProps.value,s.value)||(null!==e?e.push(l):e=[l])}}else if(a===$.current){if(null===(s=a.alternate))throw Error(i(387));s.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Qd):e=[Qd])}a=a.return}null!==e&&xa(t,e,n,r),t.flags|=262144}function Ea(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function _a(e){ya=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ca(e){return Ta(ya,e)}function Oa(e,t){return null===ya&&_a(e),Ta(e,t)}function Ta(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(i(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var Ra="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Pa=r.unstable_scheduleCallback,Aa=r.unstable_NormalPriority,ja={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Na(){return{controller:new Ra,data:new Map,refCount:0}}function za(e){e.refCount--,0===e.refCount&&Pa(Aa,function(){e.controller.abort()})}var La=null,Da=0,Fa=0,Ma=null;function Ia(){if(0===--Da&&null!==La){null!==Ma&&(Ma.status="fulfilled");var e=La;La=null,Fa=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ua=z.S;z.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===La){var n=La=[];Da=0,Fa=Ru(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}Da++,t.then(Ia,Ia)}(0,t),null!==Ua&&Ua(e,t)};var Ba=I(null);function Ha(){var e=Ba.current;return null!==e?e:rc.pooledCache}function qa(e,t){B(Ba,null===t?Ba.current:t.pool)}function Wa(){var e=Ha();return null===e?null:{parent:ja._currentValue,pool:e}}var $a=Error(i(460)),Va=Error(i(474)),Ka=Error(i(542)),Qa={then:function(){}};function Ya(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ga(){}function Xa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ga,Ga),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e;default:if("string"===typeof t.status)t.then(Ga,Ga);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e}throw Ja=t,$a}}var Ja=null;function Za(){if(null===Ja)throw Error(i(459));var e=Ja;return Ja=null,e}function eo(e){if(e===$a||e===Ka)throw Error(i(483))}var to=!1;function no(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ro(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ao(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function oo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nc)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Nr(e),jr(e,null,n),t}return Rr(e,r,t,n),Nr(e)}function io(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}function so(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var lo=!1;function co(){if(lo){if(null!==Ma)throw Ma}}function uo(e,t,n,r){lo=!1;var a=e.updateQueue;to=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,c=l.next;l.next=null,null===i?o=c:i.next=c,i=l;var u=e.alternate;null!==u&&((s=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===s?u.firstBaseUpdate=c:s.next=c,u.lastBaseUpdate=l))}if(null!==o){var d=a.baseState;for(i=0,u=c=l=null,s=o;;){var p=-536870913&s.lane,h=p!==s.lane;if(h?(oc&p)===p:(r&p)===p){0!==p&&p===Fa&&(lo=!0),null!==u&&(u=u.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});e:{var m=e,g=s;p=t;var y=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(y,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=g.payload)?m.call(y,d,p):m)||void 0===p)break e;d=f({},d,p);break e;case 2:to=!0}}null!==(p=s.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===u?(c=u=h,l=d):u=u.next=h,i|=p;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(h=s).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===u&&(l=d),a.baseState=l,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null===o&&(a.shared.lanes=0),pc|=i,e.lanes=i,e.memoizedState=d}}function fo(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function po(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fo(n[e],t)}var ho=I(null),mo=I(0);function go(e,t){B(mo,e=dc),B(ho,t),dc=e|t.baseLanes}function yo(){B(mo,dc),B(ho,ho.current)}function vo(){dc=mo.current,U(ho),U(mo)}var bo=0,wo=null,ko=null,xo=null,So=!1,Eo=!1,_o=!1,Co=0,Oo=0,To=null,Ro=0;function Po(){throw Error(i(321))}function Ao(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function jo(e,t,n,r,a,o){return bo=o,wo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=null===e||null===e.memoizedState?Vi:Ki,_o=!1,o=n(r,a),_o=!1,Eo&&(o=zo(t,n,r,a)),No(e),o}function No(e){z.H=$i;var t=null!==ko&&null!==ko.next;if(bo=0,xo=ko=wo=null,So=!1,Oo=0,To=null,t)throw Error(i(300));null===e||Os||null!==(e=e.dependencies)&&Ea(e)&&(Os=!0)}function zo(e,t,n,r){wo=e;var a=0;do{if(Eo&&(To=null),Oo=0,Eo=!1,25<=a)throw Error(i(301));if(a+=1,xo=ko=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}z.H=Qi,o=t(n,r)}while(Eo);return o}function Lo(){var e=z.H,t=e.useState()[0];return t="function"===typeof t.then?Bo(t):t,e=e.useState()[0],(null!==ko?ko.memoizedState:null)!==e&&(wo.flags|=1024),t}function Do(){var e=0!==Co;return Co=0,e}function Fo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Mo(e){if(So){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}So=!1}bo=0,xo=ko=wo=null,Eo=!1,Oo=Co=0,To=null}function Io(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===xo?wo.memoizedState=xo=e:xo=xo.next=e,xo}function Uo(){if(null===ko){var e=wo.alternate;e=null!==e?e.memoizedState:null}else e=ko.next;var t=null===xo?wo.memoizedState:xo.next;if(null!==t)xo=t,ko=e;else{if(null===e){if(null===wo.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(ko=e).memoizedState,baseState:ko.baseState,baseQueue:ko.baseQueue,queue:ko.queue,next:null},null===xo?wo.memoizedState=xo=e:xo=xo.next=e}return xo}function Bo(e){var t=Oo;return Oo+=1,null===To&&(To=[]),e=Xa(To,e,t),t=wo,null===(null===xo?t.memoizedState:xo.next)&&(t=t.alternate,z.H=null===t||null===t.memoizedState?Vi:Ki),e}function Ho(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Bo(e);if(e.$$typeof===k)return Ca(e)}throw Error(i(438,String(e)))}function qo(e){var t=null,n=wo.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wo.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=T;return t.index++,n}function Wo(e,t){return"function"===typeof t?t(e):t}function $o(e){return Vo(Uo(),ko,e)}function Vo(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,o=r.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}t.baseQueue=a=o,r.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var l=s=null,c=null,u=t=a.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(oc&f)===f:(bo&f)===f){var p=u.revertLane;if(0===p)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===Fa&&(d=!0);else{if((bo&p)===p){u=u.next,p===Fa&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=f,s=o):c=c.next=f,wo.lanes|=p,pc|=p}f=u.action,_o&&n(o,f),o=u.hasEagerState?u.eagerState:n(o,f)}else p={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=p,s=o):c=c.next=p,wo.lanes|=f,pc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?s=o:c.next=l,!Yn(o,e.memoizedState)&&(Os=!0,d&&null!==(n=Ma)))throw n;e.memoizedState=o,e.baseState=s,e.baseQueue=c,r.lastRenderedState=o}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ko(e){var t=Uo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=e(o,s.action),s=s.next}while(s!==a);Yn(o,t.memoizedState)||(Os=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Qo(e,t,n){var r=wo,a=Uo(),o=oa;if(o){if(void 0===n)throw Error(i(407));n=n()}else n=t();var s=!Yn((ko||a).memoizedState,n);if(s&&(a.memoizedState=n,Os=!0),a=a.queue,yi(2048,8,Xo.bind(null,r,a,e),[e]),a.getSnapshot!==t||s||null!==xo&&1&xo.memoizedState.tag){if(r.flags|=2048,hi(9,{destroy:void 0,resource:void 0},Go.bind(null,r,a,n,t),null),null===rc)throw Error(i(349));o||0!==(124&bo)||Yo(r,t,n)}return n}function Yo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wo.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Go(e,t,n,r){t.value=n,t.getSnapshot=r,Jo(t)&&Zo(e)}function Xo(e,t,n){return n(function(){Jo(t)&&Zo(e)})}function Jo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function Zo(e){var t=Ar(e,2);null!==t&&Dc(t,e,2)}function ei(e){var t=Io();if("function"===typeof e){var n=e;if(e=n(),_o){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:e},t}function ti(e,t,n,r){return e.baseState=n,Vo(e,ko,"function"===typeof r?r:Wo)}function ni(e,t,n,r,a){if(Hi(e))throw Error(i(485));if(null!==(e=t.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==z.T?n(!0):o.isTransition=!1,r(o),null===(n=t.pending)?(o.next=t.pending=o,ri(t,o)):(o.next=n.next,t.pending=n.next=o)}}function ri(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var o=z.T,i={};z.T=i;try{var s=n(a,r),l=z.S;null!==l&&l(i,s),ai(e,t,s)}catch(c){ii(e,t,c)}finally{z.T=o}}else try{ai(e,t,o=n(a,r))}catch(u){ii(e,t,u)}}function ai(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){oi(e,t,n)},function(n){return ii(e,t,n)}):oi(e,t,n)}function oi(e,t,n){t.status="fulfilled",t.value=n,si(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ri(e,n)))}function ii(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,si(t),t=t.next}while(t!==r)}e.action=null}function si(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function li(e,t){return t}function ci(e,t){if(oa){var n=rc.formState;if(null!==n){e:{var r=wo;if(oa){if(aa){t:{for(var a=aa,o=sa;8!==a.nodeType;){if(!o){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(o=a.data)||"F"===o?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ca(r)}r=!1}r&&(t=n[0])}}return(n=Io()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:li,lastRenderedState:t},n.queue=r,n=Ii.bind(null,wo,r),r.dispatch=n,r=ei(!1),o=Bi.bind(null,wo,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Io()).queue=a,n=ni.bind(null,wo,a,o,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ui(e){return di(Uo(),ko,e)}function di(e,t,n){if(t=Vo(e,t,li)[0],e=$o(Wo)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Bo(t)}catch(i){if(i===$a)throw Ka;throw i}else r=t;var a=(t=Uo()).queue,o=a.dispatch;return n!==t.memoizedState&&(wo.flags|=2048,hi(9,{destroy:void 0,resource:void 0},fi.bind(null,a,n),null)),[r,o,e]}function fi(e,t){e.action=t}function pi(e){var t=Uo(),n=ko;if(null!==n)return di(t,n,e);Uo(),t=t.memoizedState;var r=(n=Uo()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function hi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wo.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function mi(){return Uo().memoizedState}function gi(e,t,n,r){var a=Io();r=void 0===r?null:r,wo.flags|=e,a.memoizedState=hi(1|t,{destroy:void 0,resource:void 0},n,r)}function yi(e,t,n,r){var a=Uo();r=void 0===r?null:r;var o=a.memoizedState.inst;null!==ko&&null!==r&&Ao(r,ko.memoizedState.deps)?a.memoizedState=hi(t,o,n,r):(wo.flags|=e,a.memoizedState=hi(1|t,o,n,r))}function vi(e,t){gi(8390656,8,e,t)}function bi(e,t){yi(2048,8,e,t)}function wi(e,t){return yi(4,2,e,t)}function ki(e,t){return yi(4,4,e,t)}function xi(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function Si(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,yi(4,4,xi.bind(null,t,e),n)}function Ei(){}function _i(e,t){var n=Uo();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Ao(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ci(e,t){var n=Uo();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Ao(t,r[1]))return r[0];if(r=e(),_o){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Oi(e,t,n){return void 0===n||0!==(1073741824&bo)?e.memoizedState=t:(e.memoizedState=n,e=Lc(),wo.lanes|=e,pc|=e,n)}function Ti(e,t,n,r){return Yn(n,t)?n:null!==ho.current?(e=Oi(e,n,r),Yn(e,t)||(Os=!0),e):0===(42&bo)?(Os=!0,e.memoizedState=n):(e=Lc(),wo.lanes|=e,pc|=e,t)}function Ri(e,t,n,r,a){var o=L.p;L.p=0!==o&&8>o?o:8;var i=z.T,s={};z.T=s,Bi(e,!1,t,n);try{var l=a(),c=z.S;if(null!==c&&c(s,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Ui(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(l,r),zc());else Ui(e,t,r,zc())}catch(u){Ui(e,t,{then:function(){},status:"rejected",reason:u},zc())}finally{L.p=o,z.T=i}}function Pi(){}function Ai(e,t,n,r){if(5!==e.tag)throw Error(i(476));var a=ji(e).queue;Ri(e,a,t,D,null===n?Pi:function(){return Ni(e),n(r)})}function ji(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:D},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ni(e){Ui(e,ji(e).next.queue,{},zc())}function zi(){return Ca(Qd)}function Li(){return Uo().memoizedState}function Di(){return Uo().memoizedState}function Fi(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=zc(),r=oo(t,e=ao(n),n);return null!==r&&(Dc(r,t,n),io(r,t,n)),t={cache:Na()},void(e.payload=t)}t=t.return}}function Mi(e,t,n){var r=zc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hi(e)?qi(t,n):null!==(n=Pr(e,t,n,r))&&(Dc(n,e,r),Wi(n,t,r))}function Ii(e,t,n){Ui(e,t,n,zc())}function Ui(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hi(e))qi(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=o(i,n);if(a.hasEagerState=!0,a.eagerState=s,Yn(s,i))return Rr(e,t,a,0),null===rc&&Tr(),!1}catch(l){}if(null!==(n=Pr(e,t,a,r)))return Dc(n,e,r),Wi(n,t,r),!0}return!1}function Bi(e,t,n,r){if(r={lane:2,revertLane:Ru(),action:r,hasEagerState:!1,eagerState:null,next:null},Hi(e)){if(t)throw Error(i(479))}else null!==(t=Pr(e,n,r,2))&&Dc(t,e,2)}function Hi(e){var t=e.alternate;return e===wo||null!==t&&t===wo}function qi(e,t){Eo=So=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}var $i={readContext:Ca,use:Ho,useCallback:Po,useContext:Po,useEffect:Po,useImperativeHandle:Po,useLayoutEffect:Po,useInsertionEffect:Po,useMemo:Po,useReducer:Po,useRef:Po,useState:Po,useDebugValue:Po,useDeferredValue:Po,useTransition:Po,useSyncExternalStore:Po,useId:Po,useHostTransitionStatus:Po,useFormState:Po,useActionState:Po,useOptimistic:Po,useMemoCache:Po,useCacheRefresh:Po},Vi={readContext:Ca,use:Ho,useCallback:function(e,t){return Io().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:vi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4194308,4,xi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return gi(4194308,4,e,t)},useInsertionEffect:function(e,t){gi(4,2,e,t)},useMemo:function(e,t){var n=Io();t=void 0===t?null:t;var r=e();if(_o){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Io();if(void 0!==n){var a=n(t);if(_o){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Mi.bind(null,wo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Io().memoizedState=e},useState:function(e){var t=(e=ei(e)).queue,n=Ii.bind(null,wo,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ei,useDeferredValue:function(e,t){return Oi(Io(),e,t)},useTransition:function(){var e=ei(!1);return e=Ri.bind(null,wo,e.queue,!0,!1),Io().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=wo,a=Io();if(oa){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===rc)throw Error(i(349));0!==(124&oc)||Yo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,vi(Xo.bind(null,r,o,e),[e]),r.flags|=2048,hi(9,{destroy:void 0,resource:void 0},Go.bind(null,r,o,n,t),null),n},useId:function(){var e=Io(),t=rc.identifierPrefix;if(oa){var n=Jr;t="\xab"+t+"R"+(n=(Xr&~(1<<32-pe(Xr)-1)).toString(32)+n),0<(n=Co++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Ro++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:zi,useFormState:ci,useActionState:ci,useOptimistic:function(e){var t=Io();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bi.bind(null,wo,!0,n),n.dispatch=t,[e,t]},useMemoCache:qo,useCacheRefresh:function(){return Io().memoizedState=Fi.bind(null,wo)}},Ki={readContext:Ca,use:Ho,useCallback:_i,useContext:Ca,useEffect:bi,useImperativeHandle:Si,useInsertionEffect:wi,useLayoutEffect:ki,useMemo:Ci,useReducer:$o,useRef:mi,useState:function(){return $o(Wo)},useDebugValue:Ei,useDeferredValue:function(e,t){return Ti(Uo(),ko.memoizedState,e,t)},useTransition:function(){var e=$o(Wo)[0],t=Uo().memoizedState;return["boolean"===typeof e?e:Bo(e),t]},useSyncExternalStore:Qo,useId:Li,useHostTransitionStatus:zi,useFormState:ui,useActionState:ui,useOptimistic:function(e,t){return ti(Uo(),0,e,t)},useMemoCache:qo,useCacheRefresh:Di},Qi={readContext:Ca,use:Ho,useCallback:_i,useContext:Ca,useEffect:bi,useImperativeHandle:Si,useInsertionEffect:wi,useLayoutEffect:ki,useMemo:Ci,useReducer:Ko,useRef:mi,useState:function(){return Ko(Wo)},useDebugValue:Ei,useDeferredValue:function(e,t){var n=Uo();return null===ko?Oi(n,e,t):Ti(n,ko.memoizedState,e,t)},useTransition:function(){var e=Ko(Wo)[0],t=Uo().memoizedState;return["boolean"===typeof e?e:Bo(e),t]},useSyncExternalStore:Qo,useId:Li,useHostTransitionStatus:zi,useFormState:pi,useActionState:pi,useOptimistic:function(e,t){var n=Uo();return null!==ko?ti(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:qo,useCacheRefresh:Di},Yi=null,Gi=0;function Xi(e){var t=Gi;return Gi+=1,null===Yi&&(Yi=[]),Xa(Yi,e,t)}function Ji(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zi(e,t){if(t.$$typeof===p)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function es(e){return(0,e._init)(e._payload)}function ts(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var o=n.type;return o===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===C&&es(o)===t.type)?(Ji(t=a(t,n.props),n),t.return=e,t):(Ji(t=Ur(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Br(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case h:return Ji(n=Ur(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=qr(t,e.mode,n)).return=e,t;case C:return f(e,t=(0,t._init)(t._payload),n)}if(N(t)||P(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Xi(t),n);if(t.$$typeof===k)return f(e,Oa(e,t),n);Zi(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===a?c(e,t,n,r):null;case m:return n.key===a?u(e,t,n,r):null;case C:return p(e,t,n=(a=n._init)(n._payload),r)}if(N(n)||P(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return p(e,t,Xi(n),r);if(n.$$typeof===k)return p(e,t,Oa(e,n),r);Zi(e,n)}return null}function y(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case m:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case C:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(N(r)||P(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return y(e,t,n,Xi(r),a);if(r.$$typeof===k)return y(e,t,n,Oa(t,r),a);Zi(t,r)}return null}function v(l,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case h:e:{for(var b=u.key;null!==c;){if(c.key===b){if((b=u.type)===g){if(7===c.tag){n(l,c.sibling),(d=a(c,u.props.children)).return=l,l=d;break e}}else if(c.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===C&&es(b)===c.type){n(l,c.sibling),Ji(d=a(c,u.props),u),d.return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}u.type===g?((d=Br(u.props.children,l.mode,d,u.key)).return=l,l=d):(Ji(d=Ur(u.type,u.key,u.props,null,l.mode,d),u),d.return=l,l=d)}return s(l);case m:e:{for(b=u.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(l,c.sibling),(d=a(c,u.children||[])).return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}(d=qr(u,l.mode,d)).return=l,l=d}return s(l);case C:return v(l,c,u=(b=u._init)(u._payload),d)}if(N(u))return function(a,i,s,l){for(var c=null,u=null,d=i,h=i=0,m=null;null!==d&&h<s.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var g=p(a,d,s[h],l);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(a,d),i=o(g,i,h),null===u?c=g:u.sibling=g,u=g,d=m}if(h===s.length)return n(a,d),oa&&Zr(a,h),c;if(null===d){for(;h<s.length;h++)null!==(d=f(a,s[h],l))&&(i=o(d,i,h),null===u?c=d:u.sibling=d,u=d);return oa&&Zr(a,h),c}for(d=r(d);h<s.length;h++)null!==(m=y(d,a,h,s[h],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),i=o(m,i,h),null===u?c=m:u.sibling=m,u=m);return e&&d.forEach(function(e){return t(a,e)}),oa&&Zr(a,h),c}(l,c,u,d);if(P(u)){if("function"!==typeof(b=P(u)))throw Error(i(150));return function(a,s,l,c){if(null==l)throw Error(i(151));for(var u=null,d=null,h=s,m=s=0,g=null,v=l.next();null!==h&&!v.done;m++,v=l.next()){h.index>m?(g=h,h=null):g=h.sibling;var b=p(a,h,v.value,c);if(null===b){null===h&&(h=g);break}e&&h&&null===b.alternate&&t(a,h),s=o(b,s,m),null===d?u=b:d.sibling=b,d=b,h=g}if(v.done)return n(a,h),oa&&Zr(a,m),u;if(null===h){for(;!v.done;m++,v=l.next())null!==(v=f(a,v.value,c))&&(s=o(v,s,m),null===d?u=v:d.sibling=v,d=v);return oa&&Zr(a,m),u}for(h=r(h);!v.done;m++,v=l.next())null!==(v=y(h,a,m,v.value,c))&&(e&&null!==v.alternate&&h.delete(null===v.key?m:v.key),s=o(v,s,m),null===d?u=v:d.sibling=v,d=v);return e&&h.forEach(function(e){return t(a,e)}),oa&&Zr(a,m),u}(l,c,u=b.call(u),d)}if("function"===typeof u.then)return v(l,c,Xi(u),d);if(u.$$typeof===k)return v(l,c,Oa(l,u),d);Zi(l,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(n(l,c.sibling),(d=a(c,u)).return=l,l=d):(n(l,c),(d=Hr(u,l.mode,d)).return=l,l=d),s(l)):n(l,c)}return function(e,t,n,r){try{Gi=0;var a=v(e,t,n,r);return Yi=null,a}catch(i){if(i===$a||i===Ka)throw i;var o=Dr(29,i,null,e.mode);return o.lanes=r,o.return=e,o}}}var ns=ts(!0),rs=ts(!1),as=I(null),os=null;function is(e){var t=e.alternate;B(us,1&us.current),B(as,e),null===os&&(null===t||null!==ho.current||null!==t.memoizedState)&&(os=e)}function ss(e){if(22===e.tag){if(B(us,us.current),B(as,e),null===os){var t=e.alternate;null!==t&&null!==t.memoizedState&&(os=e)}}else ls()}function ls(){B(us,us.current),B(as,as.current)}function cs(e){U(as),os===e&&(os=null),U(us)}var us=I(0);function ds(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ps={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=zc(),a=ao(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Dc(t,e,r),io(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=zc(),a=ao(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Dc(t,e,r),io(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=zc(),r=ao(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=oo(e,r,n))&&(Dc(t,e,n),io(t,e,n))}};function hs(e,t,n,r,a,o,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!Gn(n,r)||!Gn(a,o))}function ms(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ps.enqueueReplaceState(t,t.state,null)}function gs(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var ys="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vs(e){ys(e)}function bs(e){console.error(e)}function ws(e){ys(e)}function ks(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function xs(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ss(e,t,n){return(n=ao(n)).tag=3,n.payload={element:null},n.callback=function(){ks(e,t)},n}function Es(e){return(e=ao(e)).tag=3,e}function _s(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var o=r.value;e.payload=function(){return a(o)},e.callback=function(){xs(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){xs(t,n,r),"function"!==typeof a&&(null===Ec?Ec=new Set([this]):Ec.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Cs=Error(i(461)),Os=!1;function Ts(e,t,n,r){t.child=null===e?rs(t,null,n,r):ns(t,e.child,n,r)}function Rs(e,t,n,r,a){n=n.render;var o=t.ref;if("ref"in r){var i={};for(var s in r)"ref"!==s&&(i[s]=r[s])}else i=r;return _a(t),r=jo(e,t,n,i,o,a),s=Do(),null===e||Os?(oa&&s&&ta(t),t.flags|=1,Ts(e,t,r,a),t.child):(Fo(e,t,a),Ys(e,t,a))}function Ps(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Fr(o)||void 0!==o.defaultProps||null!==n.compare?((e=Ur(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,As(e,t,o,r,a))}if(o=e.child,!Gs(e,a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:Gn)(i,r)&&e.ref===t.ref)return Ys(e,t,a)}return t.flags|=1,(e=Mr(o,r)).ref=t.ref,e.return=t,t.child=e}function As(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(Gn(o,r)&&e.ref===t.ref){if(Os=!1,t.pendingProps=r=o,!Gs(e,a))return t.lanes=e.lanes,Ys(e,t,a);0!==(131072&e.flags)&&(Os=!0)}}return Ls(e,t,n,r,a)}function js(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==o?o.baseLanes|n:n,null!==e){for(a=t.child=e.child,o=0;null!==a;)o=o|a.lanes|a.childLanes,a=a.sibling;t.childLanes=o&~r}else t.childLanes=0,t.child=null;return Ns(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ns(e,t,null!==o?o.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&qa(0,null!==o?o.cachePool:null),null!==o?go(t,o):yo(),ss(t)}else null!==o?(qa(0,o.cachePool),go(t,o),ls(),t.memoizedState=null):(null!==e&&qa(0,null),yo(),ls());return Ts(e,t,a,n),t.child}function Ns(e,t,n,r){var a=Ha();return a=null===a?null:{parent:ja._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&qa(0,null),yo(),ss(t),null!==e&&Sa(e,t,r,!0),null}function zs(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ls(e,t,n,r,a){return _a(t),n=jo(e,t,n,r,void 0,a),r=Do(),null===e||Os?(oa&&r&&ta(t),t.flags|=1,Ts(e,t,n,a),t.child):(Fo(e,t,a),Ys(e,t,a))}function Ds(e,t,n,r,a,o){return _a(t),t.updateQueue=null,n=zo(t,r,n,a),No(e),r=Do(),null===e||Os?(oa&&r&&ta(t),t.flags|=1,Ts(e,t,n,o),t.child):(Fo(e,t,o),Ys(e,t,o))}function Fs(e,t,n,r,a){if(_a(t),null===t.stateNode){var o=zr,i=n.contextType;"object"===typeof i&&null!==i&&(o=Ca(i)),o=new n(r,o),t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,o.updater=ps,t.stateNode=o,o._reactInternals=t,(o=t.stateNode).props=r,o.state=t.memoizedState,o.refs={},no(t),i=n.contextType,o.context="object"===typeof i&&null!==i?Ca(i):zr,o.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(fs(t,n,i,r),o.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(i=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),i!==o.state&&ps.enqueueReplaceState(o,o.state,null),uo(t,r,o,a),co(),o.state=t.memoizedState),"function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){o=t.stateNode;var s=t.memoizedProps,l=gs(n,s);o.props=l;var c=o.context,u=n.contextType;i=zr,"object"===typeof u&&null!==u&&(i=Ca(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof o.getSnapshotBeforeUpdate,s=t.pendingProps!==s,u||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s||c!==i)&&ms(t,o,r,i),to=!1;var f=t.memoizedState;o.state=f,uo(t,r,o,a),co(),c=t.memoizedState,s||f!==c||to?("function"===typeof d&&(fs(t,n,d,r),c=t.memoizedState),(l=to||hs(t,n,l,r,f,c,i))?(u||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=i,r=l):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ro(e,t),u=gs(n,i=t.memoizedProps),o.props=u,d=t.pendingProps,f=o.context,c=n.contextType,l=zr,"object"===typeof c&&null!==c&&(l=Ca(c)),(c="function"===typeof(s=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==l)&&ms(t,o,r,l),to=!1,f=t.memoizedState,o.state=f,uo(t,r,o,a),co();var p=t.memoizedState;i!==d||f!==p||to||null!==e&&null!==e.dependencies&&Ea(e.dependencies)?("function"===typeof s&&(fs(t,n,s,r),p=t.memoizedState),(u=to||hs(t,n,u,r,f,p,l)||null!==e&&null!==e.dependencies&&Ea(e.dependencies))?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=l,r=u):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return o=r,zs(e,t),r=0!==(128&t.flags),o||r?(o=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:o.render(),t.flags|=1,null!==e&&r?(t.child=ns(t,e.child,null,a),t.child=ns(t,null,n,a)):Ts(e,t,n,a),t.memoizedState=o.state,e=t.child):e=Ys(e,t,a),e}function Ms(e,t,n,r){return pa(),t.flags|=256,Ts(e,t,n,r),t.child}var Is={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Us(e){return{baseLanes:e,cachePool:Wa()}}function Bs(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gc),e}function Hs(e,t,n){var r,a=t.pendingProps,o=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&us.current)),r&&(o=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(oa){if(o?is(t):ls(),oa){var l,c=aa;if(l=c){e:{for(l=c,c=sa;8!==l.nodeType;){if(!c){c=null;break e}if(null===(l=yd(l.nextSibling))){c=null;break e}}c=l}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Gr?{id:Xr,overflow:Jr}:null,retryLane:536870912,hydrationErrors:null},(l=Dr(18,null,null,0)).stateNode=c,l.return=t,t.child=l,ra=t,aa=null,l=!0):l=!1}l||ca(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return gd(c)?t.lanes=32:t.lanes=536870912,null;cs(t)}return c=a.children,a=a.fallback,o?(ls(),c=Ws({mode:"hidden",children:c},o=t.mode),a=Br(a,o,n,null),c.return=t,a.return=t,c.sibling=a,t.child=c,(o=t.child).memoizedState=Us(n),o.childLanes=Bs(e,r,n),t.memoizedState=Is,a):(is(t),qs(t,c))}if(null!==(l=e.memoizedState)&&null!==(c=l.dehydrated)){if(s)256&t.flags?(is(t),t.flags&=-257,t=$s(e,t,n)):null!==t.memoizedState?(ls(),t.child=e.child,t.flags|=128,t=null):(ls(),o=a.fallback,c=t.mode,a=Ws({mode:"visible",children:a.children},c),(o=Br(o,c,n,null)).flags|=2,a.return=t,o.return=t,a.sibling=o,t.child=a,ns(t,e.child,null,n),(a=t.child).memoizedState=Us(n),a.childLanes=Bs(e,r,n),t.memoizedState=Is,t=o);else if(is(t),gd(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(a=Error(i(419))).stack="",a.digest=r,ma({value:a,source:null,stack:null}),t=$s(e,t,n)}else if(Os||Sa(e,t,n,!1),r=0!==(n&e.childLanes),Os||r){if(null!==(r=rc)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Te(a))&(r.suspendedLanes|n))?0:a)&&a!==l.retryLane))throw l.retryLane=a,Ar(e,a),Dc(r,e,a),Cs;"$?"===c.data||Kc(),t=$s(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,aa=yd(c.nextSibling),ra=t,oa=!0,ia=null,sa=!1,null!==e&&(Qr[Yr++]=Xr,Qr[Yr++]=Jr,Qr[Yr++]=Gr,Xr=e.id,Jr=e.overflow,Gr=t),(t=qs(t,a.children)).flags|=4096);return t}return o?(ls(),o=a.fallback,c=t.mode,u=(l=e.child).sibling,(a=Mr(l,{mode:"hidden",children:a.children})).subtreeFlags=65011712&l.subtreeFlags,null!==u?o=Mr(u,o):(o=Br(o,c,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,null===(c=e.child.memoizedState)?c=Us(n):(null!==(l=c.cachePool)?(u=ja._currentValue,l=l.parent!==u?{parent:u,pool:u}:l):l=Wa(),c={baseLanes:c.baseLanes|n,cachePool:l}),o.memoizedState=c,o.childLanes=Bs(e,r,n),t.memoizedState=Is,a):(is(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function qs(e,t){return(t=Ws({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Ws(e,t){return(e=Dr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $s(e,t,n){return ns(t,e.child,null,n),(e=qs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ka(e.return,t,n)}function Ks(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Qs(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(Ts(e,t,r.children,n),0!==(2&(r=us.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vs(e,n,t);else if(19===e.tag)Vs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(us,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ds(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Ks(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ds(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Ks(t,!0,n,null,o);break;case"together":Ks(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ys(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Gs(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ea(e))}function Xs(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Os=!0;else{if(!Gs(e,n)&&0===(128&t.flags))return Os=!1,function(e,t,n){switch(t.tag){case 3:V(t,t.stateNode.containerInfo),ba(0,ja,e.memoizedState.cache),pa();break;case 27:case 5:Q(t);break;case 4:V(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(is(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Hs(e,t,n):(is(t),null!==(e=Ys(e,t,n))?e.sibling:null);is(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Qs(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),B(us,us.current),r)break;return null;case 22:case 23:return t.lanes=0,js(e,t,n);case 24:ba(0,ja,e.memoizedState.cache)}return Ys(e,t,n)}(e,t,n);Os=0!==(131072&e.flags)}else Os=!1,oa&&0!==(1048576&t.flags)&&ea(t,Kr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===x){t.tag=11,t=Rs(null,t,r,e,n);break e}if(a===_){t.tag=14,t=Ps(null,t,r,e,n);break e}}throw t=j(r)||r,Error(i(306,t,""))}Fr(r)?(e=gs(r,e),t.tag=1,t=Fs(null,t,r,e,n)):(t.tag=0,t=Ls(null,t,r,e,n))}return t;case 0:return Ls(e,t,t.type,t.pendingProps,n);case 1:return Fs(e,t,r=t.type,a=gs(r,t.pendingProps),n);case 3:e:{if(V(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var o=t.memoizedState;a=o.element,ro(e,t),uo(t,r,null,n);var s=t.memoizedState;if(r=s.cache,ba(0,ja,r),r!==o.cache&&xa(t,[ja],n,!0),co(),r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ms(e,t,r,n);break e}if(r!==a){ma(a=Er(Error(i(424)),t)),t=Ms(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=yd(e.firstChild),ra=t,oa=!0,ia=null,sa=!0,n=rs(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),r===a){t=Ys(e,t,n);break e}Ts(e,t,r,n)}t=t.child}return t;case 26:return zs(e,t),null===e?(n=Td(t.type,null,t.pendingProps,null))?t.memoizedState=n:oa||(n=t.type,e=t.pendingProps,(r=rd(W.current).createElement(n))[je]=t,r[Ne]=e,ed(r,n,e),$e(r),t.stateNode=r):t.memoizedState=Td(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Q(t),null===e&&oa&&(r=t.stateNode=wd(t.type,t.pendingProps,W.current),ra=t,sa=!0,a=aa,pd(t.type)?(vd=a,aa=yd(r.firstChild)):aa=a),Ts(e,t,t.pendingProps.children,n),zs(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&oa&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ie])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(o=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(o!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((o=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var o=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===o)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,sa))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),sa=!1,a=!0):a=!1),a||ca(t)),Q(t),a=t.type,o=t.pendingProps,s=null!==e?e.memoizedProps:null,r=o.children,id(a,o)?r=null:null!==s&&id(a,s)&&(t.flags|=32),null!==t.memoizedState&&(a=jo(e,t,Lo,null,null,n),Qd._currentValue=a),zs(e,t),Ts(e,t,r,n),t.child;case 6:return null===e&&oa&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,sa))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ca(t)),null;case 13:return Hs(e,t,n);case 4:return V(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ns(t,null,r,n):Ts(e,t,r,n),t.child;case 11:return Rs(e,t,t.type,t.pendingProps,n);case 7:return Ts(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ts(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Ts(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,_a(t),r=r(a=Ca(a)),t.flags|=1,Ts(e,t,r,n),t.child;case 14:return Ps(e,t,t.type,t.pendingProps,n);case 15:return As(e,t,t.type,t.pendingProps,n);case 19:return Qs(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Ws(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return js(e,t,n);case 24:return _a(t),r=Ca(ja),null===e?(null===(a=Ha())&&(a=rc,o=Na(),a.pooledCache=o,o.refCount++,null!==o&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:r,cache:a},no(t),ba(0,ja,a)):(0!==(e.lanes&n)&&(ro(e,t),uo(t,null,null,n),co()),a=e.memoizedState,o=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,ja,r)):(r=o.cache,ba(0,ja,r),r!==a.cache&&xa(t,[ja],n,!0))),Ts(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Js(e){e.flags|=4}function Zs(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=as.current)&&((4194048&oc)===oc?null!==os:(62914560&oc)!==oc&&0===(536870912&oc)||t!==os))throw Ja=Qa,Va;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,yc|=t)}function tl(e,t){if(!oa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(ja),K(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Js(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,ha())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Js(t),null!==n?(nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Js(t),nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Js(t),nl(t),t.flags&=-16777217),null;case 27:Y(t),n=W.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Js(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}e=H.current,fa(t)?ua(t):(e=wd(a,r,n),t.stateNode=e,Js(t))}return nl(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Js(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}if(e=H.current,fa(t))ua(t);else{switch(a=rd(W.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[je]=t,e[Ne]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Js(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Js(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=W.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[je]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Gu(e.nodeValue,n)))||ca(t)}else(e=rd(e).createTextNode(r))[je]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[je]=t}else pa(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),a=!1}else a=ha(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(cs(t),t):(cs(t),null)}if(cs(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return K(),null===e&&Uu(t.stateNode.containerInfo),nl(t),null;case 10:return wa(t.type),nl(t),null;case 19:if(U(us),null===(a=t.memoizedState))return nl(t),null;if(r=0!==(128&t.flags),null===(o=a.rendering))if(r)tl(a,!1);else{if(0!==fc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(o=ds(e))){for(t.flags|=128,tl(a,!1),e=o.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ir(n,e),n=n.sibling;return B(us,1&us.current|2),t.child}e=e.sibling}null!==a.tail&&te()>xc&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ds(o))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!oa)return nl(t),null}else 2*te()-a.renderingStartTime>xc&&536870912!==n&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=a.last)?e.sibling=o:t.child=o,a.last=o)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=us.current,B(us,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return cs(t),vo(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&U(Ba),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(ja),nl(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function al(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(ja),K(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(cs(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return U(us),null;case 4:return K(),null;case 10:return wa(t.type),null;case 22:case 23:return cs(t),vo(),null!==e&&U(Ba),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(ja),null;default:return null}}function ol(e,t){switch(na(t),t.tag){case 3:wa(ja),K();break;case 26:case 27:case 5:Y(t);break;case 4:K();break;case 13:cs(t);break;case 19:U(us);break;case 10:wa(t.type);break;case 22:case 23:cs(t),vo(),null!==e&&U(Ba);break;case 24:wa(ja)}}function il(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var o=n.create,i=n.inst;r=o(),i.destroy=r}n=n.next}while(n!==a)}}catch(s){uu(t,t.return,s)}}function sl(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var o=a.next;r=o;do{if((r.tag&e)===e){var i=r.inst,s=i.destroy;if(void 0!==s){i.destroy=void 0,a=t;var l=n,c=s;try{c()}catch(u){uu(a,l,u)}}}r=r.next}while(r!==o)}}catch(u){uu(t,t.return,u)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{po(t,n)}catch(r){uu(e,e.return,r)}}}function cl(e,t,n){n.props=gs(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){uu(e,t,r)}}function ul(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){uu(e,t,a)}}function dl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){uu(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(o){uu(e,t,o)}else n.current=null}function fl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){uu(e,e.return,a)}}function pl(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,o=null,s=null,l=null,c=null,u=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(h)||Ju(e,t,h,null,r,f)}}for(var p in r){var h=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":o=h;break;case"name":a=h;break;case"checked":u=h;break;case"defaultChecked":d=h;break;case"value":s=h;break;case"defaultValue":l=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(i(137,t));break;default:h!==f&&Ju(e,t,p,h,r,f)}}return void gt(e,s,l,c,u,d,o,a);case"select":for(o in h=s=l=p=null,n)if(c=n[o],n.hasOwnProperty(o)&&null!=c)switch(o){case"value":break;case"multiple":h=c;default:r.hasOwnProperty(o)||Ju(e,t,o,null,r,c)}for(a in r)if(o=r[a],c=n[a],r.hasOwnProperty(a)&&(null!=o||null!=c))switch(a){case"value":p=o;break;case"defaultValue":l=o;break;case"multiple":s=o;default:o!==c&&Ju(e,t,a,o,r,c)}return t=l,n=s,r=h,void(null!=p?bt(e,!!n,p,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in h=p=null,n)if(a=n[l],n.hasOwnProperty(l)&&null!=a&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Ju(e,t,l,null,r,a)}for(s in r)if(a=r[s],o=n[s],r.hasOwnProperty(s)&&(null!=a||null!=o))switch(s){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(i(91));break;default:a!==o&&Ju(e,t,s,a,r,o)}return void wt(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Ju(e,t,m,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))if("selected"===c)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Ju(e,t,c,p,r,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Ju(e,t,g,null,r,p);for(u in r)if(p=r[u],h=n[u],r.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:Ju(e,t,u,p,r,h)}return;default:if(Ct(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zu(e,t,y,void 0,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Zu(e,t,d,p,r,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Ju(e,t,v,null,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Ju(e,t,f,p,r,h)}(r,e.type,n,t),r[Ne]=t}catch(a){uu(e,e.return,a)}}function hl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function ml(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Xu));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gl(e,t,n),e=e.sibling;null!==e;)gl(e,t,n),e=e.sibling}function yl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(yl(e,t,n),e=e.sibling;null!==e;)yl(e,t,n),e=e.sibling}function vl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[je]=e,t[Ne]=n}catch(o){uu(e,e.return,o)}}var bl=!1,wl=!1,kl=!1,xl="function"===typeof WeakSet?WeakSet:Set,Sl=null;function El(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Fl(e,n),4&r&&il(5,n);break;case 1:if(Fl(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){uu(n,n.return,i)}else{var a=gs(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){uu(n,n.return,s)}}64&r&&ll(n),512&r&&ul(n,n.return);break;case 3:if(Fl(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{po(e,t)}catch(i){uu(n,n.return,i)}}break;case 27:null===t&&4&r&&vl(n);case 26:case 5:Fl(e,n),null===t&&4&r&&fl(n),512&r&&ul(n,n.return);break;case 12:Fl(e,n);break;case 13:Fl(e,n),4&r&&Pl(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=hu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||wl,a=bl;var o=wl;bl=r,(wl=t)&&!o?Il(e,n,0!==(8772&n.subtreeFlags)):Fl(e,n),bl=a,wl=o}break;case 30:break;default:Fl(e,n)}}function _l(e){var t=e.alternate;null!==t&&(e.alternate=null,_l(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ue(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Cl=null,Ol=!1;function Tl(e,t,n){for(n=n.child;null!==n;)Rl(e,t,n),n=n.sibling}function Rl(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,n)}catch(o){}switch(n.tag){case 26:wl||dl(n,t),Tl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||dl(n,t);var r=Cl,a=Ol;pd(n.type)&&(Cl=n.stateNode,Ol=!1),Tl(e,t,n),kd(n.stateNode),Cl=r,Ol=a;break;case 5:wl||dl(n,t);case 6:if(r=Cl,a=Ol,Cl=null,Tl(e,t,n),Ol=a,null!==(Cl=r))if(Ol)try{(9===Cl.nodeType?Cl.body:"HTML"===Cl.nodeName?Cl.ownerDocument.body:Cl).removeChild(n.stateNode)}catch(i){uu(n,t,i)}else try{Cl.removeChild(n.stateNode)}catch(i){uu(n,t,i)}break;case 18:null!==Cl&&(Ol?(hd(9===(e=Cl).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Tf(e)):hd(Cl,n.stateNode));break;case 4:r=Cl,a=Ol,Cl=n.stateNode.containerInfo,Ol=!0,Tl(e,t,n),Cl=r,Ol=a;break;case 0:case 11:case 14:case 15:wl||sl(2,n,t),wl||sl(4,n,t),Tl(e,t,n);break;case 1:wl||(dl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&cl(n,t,r)),Tl(e,t,n);break;case 21:Tl(e,t,n);break;case 22:wl=(r=wl)||null!==n.memoizedState,Tl(e,t,n),wl=r;break;default:Tl(e,t,n)}}function Pl(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Tf(e)}catch(n){uu(t,t.return,n)}}function Al(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new xl),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new xl),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var r=mu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function jl(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 27:if(pd(l.type)){Cl=l.stateNode,Ol=!1;break e}break;case 5:Cl=l.stateNode,Ol=!1;break e;case 3:case 4:Cl=l.stateNode.containerInfo,Ol=!0;break e}l=l.return}if(null===Cl)throw Error(i(160));Rl(o,s,a),Cl=null,Ol=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)zl(t,e),t=t.sibling}var Nl=null;function zl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:jl(t,e),Ll(e),4&r&&(sl(3,e,e.return),il(3,e),sl(5,e,e.return));break;case 1:jl(t,e),Ll(e),512&r&&(wl||null===n||dl(n,n.return)),64&r&&bl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Nl;if(jl(t,e),Ll(e),512&r&&(wl||null===n||dl(n,n.return)),4&r){var o=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(o=a.getElementsByTagName("title")[0])||o[Ie]||o[je]||"http://www.w3.org/2000/svg"===o.namespaceURI||o.hasAttribute("itemprop"))&&(o=a.createElement(r),a.head.insertBefore(o,a.querySelector("head > title"))),ed(o,r,n),o[je]=e,$e(o),r=o;break e;case"link":var s=Id("link","href",a).get(r+(n.href||""));if(s)for(var l=0;l<s.length;l++)if((o=s[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&o.getAttribute("rel")===(null==n.rel?null:n.rel)&&o.getAttribute("title")===(null==n.title?null:n.title)&&o.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){s.splice(l,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;case"meta":if(s=Id("meta","content",a).get(r+(n.content||"")))for(l=0;l<s.length;l++)if((o=s[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&o.getAttribute("name")===(null==n.name?null:n.name)&&o.getAttribute("property")===(null==n.property?null:n.property)&&o.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&o.getAttribute("charset")===(null==n.charSet?null:n.charSet)){s.splice(l,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;default:throw Error(i(468,r))}o[je]=e,$e(o),r=o}e.stateNode=r}else Ud(a,e.type,e.stateNode);else e.stateNode=zd(a,r,e.memoizedProps);else o!==r?(null===o?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):o.count--,null===r?Ud(a,e.type,e.stateNode):zd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&pl(e,e.memoizedProps,n.memoizedProps)}break;case 27:jl(t,e),Ll(e),512&r&&(wl||null===n||dl(n,n.return)),null!==n&&4&r&&pl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(jl(t,e),Ll(e),512&r&&(wl||null===n||dl(n,n.return)),32&e.flags){a=e.stateNode;try{xt(a,"")}catch(h){uu(e,e.return,h)}}4&r&&null!=e.stateNode&&pl(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(kl=!0);break;case 6:if(jl(t,e),Ll(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){uu(e,e.return,h)}}break;case 3:if(Md=null,a=Nl,Nl=Ed(t.containerInfo),jl(t,e),Nl=a,Ll(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Tf(t.containerInfo)}catch(h){uu(e,e.return,h)}kl&&(kl=!1,Dl(e));break;case 4:r=Nl,Nl=Ed(e.stateNode.containerInfo),jl(t,e),Ll(e),Nl=r;break;case 12:default:jl(t,e),Ll(e);break;case 13:jl(t,e),Ll(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(kc=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Al(e,r)));break;case 22:a=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=bl,d=wl;if(bl=u||a,wl=d||c,jl(t,e),wl=d,bl=u,Ll(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||c||bl||wl||Ml(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(o=c.stateNode,a)"function"===typeof(s=o.style).setProperty?s.setProperty("display","none","important"):s.display="none";else{l=c.stateNode;var f=c.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;l.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(h){uu(c,c.return,h)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=a?"":c.memoizedProps}catch(h){uu(c,c.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Al(e,n))));break;case 19:jl(t,e),Ll(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Al(e,r)));case 30:case 21:}}function Ll(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(hl(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode;yl(e,ml(e),a);break;case 5:var o=n.stateNode;32&n.flags&&(xt(o,""),n.flags&=-33),yl(e,ml(e),o);break;case 3:case 4:var s=n.stateNode.containerInfo;gl(e,ml(e),s);break;default:throw Error(i(161))}}catch(l){uu(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Dl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Dl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Fl(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)El(e,t.alternate,t),t=t.sibling}function Ml(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:sl(4,t,t.return),Ml(t);break;case 1:dl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&cl(t,t.return,n),Ml(t);break;case 27:kd(t.stateNode);case 26:case 5:dl(t,t.return),Ml(t);break;case 22:null===t.memoizedState&&Ml(t);break;default:Ml(t)}e=e.sibling}}function Il(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,o=t,i=o.flags;switch(o.tag){case 0:case 11:case 15:Il(a,o,n),il(4,o);break;case 1:if(Il(a,o,n),"function"===typeof(a=(r=o).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){uu(r,r.return,c)}if(null!==(a=(r=o).updateQueue)){var s=r.stateNode;try{var l=a.shared.hiddenCallbacks;if(null!==l)for(a.shared.hiddenCallbacks=null,a=0;a<l.length;a++)fo(l[a],s)}catch(c){uu(r,r.return,c)}}n&&64&i&&ll(o),ul(o,o.return);break;case 27:vl(o);case 26:case 5:Il(a,o,n),n&&null===r&&4&i&&fl(o),ul(o,o.return);break;case 12:Il(a,o,n);break;case 13:Il(a,o,n),n&&4&i&&Pl(a,o);break;case 22:null===o.memoizedState&&Il(a,o,n),ul(o,o.return);break;case 30:break;default:Il(a,o,n)}t=t.sibling}}function Ul(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&za(n))}function Bl(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&za(e))}function Hl(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)ql(e,t,n,r),t=t.sibling}function ql(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Hl(e,t,n,r),2048&a&&il(9,t);break;case 1:case 13:default:Hl(e,t,n,r);break;case 3:Hl(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&za(e)));break;case 12:if(2048&a){Hl(e,t,n,r),e=t.stateNode;try{var o=t.memoizedProps,i=o.id,s=o.onPostCommit;"function"===typeof s&&s(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){uu(t,t.return,l)}}else Hl(e,t,n,r);break;case 23:break;case 22:o=t.stateNode,i=t.alternate,null!==t.memoizedState?2&o._visibility?Hl(e,t,n,r):$l(e,t):2&o._visibility?Hl(e,t,n,r):(o._visibility|=2,Wl(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Ul(i,t);break;case 24:Hl(e,t,n,r),2048&a&&Bl(t.alternate,t)}}function Wl(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var o=e,i=t,s=n,l=r,c=i.flags;switch(i.tag){case 0:case 11:case 15:Wl(o,i,s,l,a),il(8,i);break;case 23:break;case 22:var u=i.stateNode;null!==i.memoizedState?2&u._visibility?Wl(o,i,s,l,a):$l(o,i):(u._visibility|=2,Wl(o,i,s,l,a)),a&&2048&c&&Ul(i.alternate,i);break;case 24:Wl(o,i,s,l,a),a&&2048&c&&Bl(i.alternate,i);break;default:Wl(o,i,s,l,a)}t=t.sibling}}function $l(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:$l(n,r),2048&a&&Ul(r.alternate,r);break;case 24:$l(n,r),2048&a&&Bl(r.alternate,r);break;default:$l(n,r)}t=t.sibling}}var Vl=8192;function Kl(e){if(e.subtreeFlags&Vl)for(e=e.child;null!==e;)Ql(e),e=e.sibling}function Ql(e){switch(e.tag){case 26:Kl(e),e.flags&Vl&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(i(475));var r=Hd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Rd(n.href),o=e.querySelector(Pd(a));if(o)return null!==(e=o._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Wd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=o,void $e(o);o=e.ownerDocument||e,n=Ad(n),(a=xd.get(a))&&Dd(n,a),$e(o=o.createElement("link"));var s=o;s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(o,"link",n),t.instance=o}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Wd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Nl,e.memoizedState,e.memoizedProps);break;case 5:default:Kl(e);break;case 3:case 4:var t=Nl;Nl=Ed(e.stateNode.containerInfo),Kl(e),Nl=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Vl,Vl=16777216,Kl(e),Vl=t):Kl(e))}}function Yl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Gl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Sl=r,Zl(r,e)}Yl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Xl(e),e=e.sibling}function Xl(e){switch(e.tag){case 0:case 11:case 15:Gl(e),2048&e.flags&&sl(9,e,e.return);break;case 3:case 12:default:Gl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Jl(e)):Gl(e)}}function Jl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Sl=r,Zl(r,e)}Yl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:sl(8,t,t.return),Jl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Jl(t));break;default:Jl(t)}e=e.sibling}}function Zl(e,t){for(;null!==Sl;){var n=Sl;switch(n.tag){case 0:case 11:case 15:sl(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:za(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Sl=r;else e:for(n=e;null!==Sl;){var a=(r=Sl).sibling,o=r.return;if(_l(r),r===n){Sl=null;break e}if(null!==a){a.return=o,Sl=a;break e}Sl=o}}}var ec={getCacheForType:function(e){var t=Ca(ja),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tc="function"===typeof WeakMap?WeakMap:Map,nc=0,rc=null,ac=null,oc=0,ic=0,sc=null,lc=!1,cc=!1,uc=!1,dc=0,fc=0,pc=0,hc=0,mc=0,gc=0,yc=0,vc=null,bc=null,wc=!1,kc=0,xc=1/0,Sc=null,Ec=null,_c=0,Cc=null,Oc=null,Tc=0,Rc=0,Pc=null,Ac=null,jc=0,Nc=null;function zc(){if(0!==(2&nc)&&0!==oc)return oc&-oc;if(null!==z.T){return 0!==Fa?Fa:Ru()}return Pe()}function Lc(){0===gc&&(gc=0===(536870912&oc)||oa?xe():536870912);var e=as.current;return null!==e&&(e.flags|=32),gc}function Dc(e,t,n){(e!==rc||2!==ic&&9!==ic)&&null===e.cancelPendingCommit||(qc(e,0),Uc(e,oc,gc,!1)),_e(e,n),0!==(2&nc)&&e===rc||(e===rc&&(0===(2&nc)&&(hc|=n),4===fc&&Uc(e,oc,gc,!1)),xu(e))}function Fc(e,t,n){if(0!==(6&nc))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=nc;nc|=2;var r=$c(),a=Vc();rc!==e||oc!==t?(Sc=null,xc=te()+500,qc(e,t)):cc=we(e,t);e:for(;;)try{if(0!==ic&&null!==ac){t=ac;var o=sc;t:switch(ic){case 1:ic=0,sc=null,Zc(e,t,o,1);break;case 2:case 9:if(Ya(o)){ic=0,sc=null,Jc(t);break}t=function(){2!==ic&&9!==ic||rc!==e||(ic=7),xu(e)},o.then(t,t);break e;case 3:ic=7;break e;case 4:ic=5;break e;case 7:Ya(o)?(ic=0,sc=null,Jc(t)):(ic=0,sc=null,Zc(e,t,o,7));break;case 5:var s=null;switch(ac.tag){case 26:s=ac.memoizedState;case 5:case 27:var l=ac;if(!s||Bd(s)){ic=0,sc=null;var c=l.sibling;if(null!==c)ac=c;else{var u=l.return;null!==u?(ac=u,eu(u)):ac=null}break t}}ic=0,sc=null,Zc(e,t,o,5);break;case 6:ic=0,sc=null,Zc(e,t,o,6);break;case 8:Hc(),fc=6;break e;default:throw Error(i(462))}}Gc();break}catch(d){Wc(e,d)}return va=ya=null,z.H=r,z.A=a,nc=n,null!==ac?0:(rc=null,oc=0,Tr(),fc)}(e,t):Qc(e,t,!0),o=r;;){if(0===a){cc&&!r&&Uc(e,t,0,!1);break}if(n=e.current.alternate,!o||Ic(n)){if(2===a){if(o=t,e.errorRecoveryDisabledLanes&o)var s=0;else s=0!==(s=-536870913&e.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){t=s;e:{var l=e;a=vc;var c=l.current.memoizedState.isDehydrated;if(c&&(qc(l,s).flags|=256),2!==(s=Qc(l,s,!1))){if(uc&&!c){l.errorRecoveryDisabledLanes|=o,hc|=o,a=4;break e}o=bc,bc=a,null!==o&&(null===bc?bc=o:bc.push.apply(bc,o))}a=s}if(o=!1,2!==a)continue}}if(1===a){qc(e,0),Uc(e,t,0,!0);break}e:{switch(r=e,o=a){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Uc(r,t,gc,!lc);break e;case 2:bc=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(a=kc+300-te())){if(Uc(r,t,gc,!lc),0!==be(r,0,!0))break e;r.timeoutHandle=ld(Mc.bind(null,r,n,bc,Sc,wc,t,gc,hc,yc,lc,o,2,-0,0),a)}else Mc(r,n,bc,Sc,wc,t,gc,hc,yc,lc,o,0,-0,0)}break}a=Qc(e,t,!1),o=!1}xu(e)}function Mc(e,t,n,r,a,o,s,l,c,u,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Hd={stylesheets:null,count:0,unsuspend:qd},Ql(t),null!==(f=function(){if(null===Hd)throw Error(i(475));var e=Hd;return e.stylesheets&&0===e.count&&Vd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Vd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nu.bind(null,e,t,o,n,r,a,s,l,c,d,1,p,h)),void Uc(e,o,s,!u);nu(e,t,o,n,r,a,s,l,c)}function Ic(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!Yn(o(),a))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Uc(e,t,n,r){t&=~mc,t&=~hc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var o=31-pe(a),i=1<<o;r[o]=-1,a&=~i}0!==n&&Ce(e,n,t)}function Bc(){return 0!==(6&nc)||(Su(0,!1),!1)}function Hc(){if(null!==ac){if(0===ic)var e=ac.return;else va=ya=null,Mo(e=ac),Yi=null,Gi=0,e=ac;for(;null!==e;)ol(e.alternate,e),e=e.return;ac=null}}function qc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,cd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hc(),rc=e,ac=n=Mr(e.current,null),oc=t,ic=0,sc=null,lc=!1,cc=we(e,t),uc=!1,yc=gc=mc=hc=pc=fc=0,bc=vc=null,wc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),o=1<<a;t|=e[a],r&=~o}return dc=t,Tr(),n}function Wc(e,t){wo=null,z.H=$i,t===$a||t===Ka?(t=Za(),ic=3):t===Va?(t=Za(),ic=4):ic=t===Cs?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,sc=t,null===ac&&(fc=1,ks(e,Er(t,e.current)))}function $c(){var e=z.H;return z.H=$i,null===e?$i:e}function Vc(){var e=z.A;return z.A=ec,e}function Kc(){fc=4,lc||(4194048&oc)!==oc&&null!==as.current||(cc=!0),0===(134217727&pc)&&0===(134217727&hc)||null===rc||Uc(rc,oc,gc,!1)}function Qc(e,t,n){var r=nc;nc|=2;var a=$c(),o=Vc();rc===e&&oc===t||(Sc=null,qc(e,t)),t=!1;var i=fc;e:for(;;)try{if(0!==ic&&null!==ac){var s=ac,l=sc;switch(ic){case 8:Hc(),i=6;break e;case 3:case 2:case 9:case 6:null===as.current&&(t=!0);var c=ic;if(ic=0,sc=null,Zc(e,s,l,c),n&&cc){i=0;break e}break;default:c=ic,ic=0,sc=null,Zc(e,s,l,c)}}Yc(),i=fc;break}catch(u){Wc(e,u)}return t&&e.shellSuspendCounter++,va=ya=null,nc=r,z.H=a,z.A=o,null===ac&&(rc=null,oc=0,Tr()),i}function Yc(){for(;null!==ac;)Xc(ac)}function Gc(){for(;null!==ac&&!Z();)Xc(ac)}function Xc(e){var t=Xs(e.alternate,e,dc);e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Jc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ds(n,t,t.pendingProps,t.type,void 0,oc);break;case 11:t=Ds(n,t,t.pendingProps,t.type.render,t.ref,oc);break;case 5:Mo(t);default:ol(n,t),t=Xs(n,t=ac=Ir(t,dc),dc)}e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Zc(e,t,n,r){va=ya=null,Mo(t),Yi=null,Gi=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=as.current)){switch(n.tag){case 13:return null===os?Kc():null===n.alternate&&0===fc&&(fc=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Qa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),du(e,r,a)),!1;case 22:return n.flags|=65536,r===Qa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),du(e,r,a)),!1}throw Error(i(435,n.tag))}return du(e,r,a),Kc(),!1}if(oa)return null!==(t=as.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==la&&ma(Er(e=Error(i(422),{cause:r}),n))):(r!==la&&ma(Er(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Er(r,n),so(e,a=Ss(e.stateNode,r,a)),4!==fc&&(fc=2)),!1;var o=Error(i(520),{cause:r});if(o=Er(o,n),null===vc?vc=[o]:vc.push(o),4!==fc&&(fc=2),null===t)return!0;r=Er(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,so(n,e=Ss(n.stateNode,r,e)),!1;case 1:if(t=n.type,o=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==o&&"function"===typeof o.componentDidCatch&&(null===Ec||!Ec.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,_s(a=Es(a),e,n,r),so(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,oc))return fc=1,ks(e,Er(n,e.current)),void(ac=null)}catch(o){if(null!==a)throw ac=a,o;return fc=1,ks(e,Er(n,e.current)),void(ac=null)}32768&t.flags?(oa||1===r?e=!0:cc||0!==(536870912&oc)?e=!1:(lc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=as.current)&&13===r.tag&&(r.flags|=16384))),tu(t,e)):eu(t)}function eu(e){var t=e;do{if(0!==(32768&t.flags))return void tu(t,lc);e=t.return;var n=rl(t.alternate,t,dc);if(null!==n)return void(ac=n);if(null!==(t=t.sibling))return void(ac=t);ac=t=e}while(null!==t);0===fc&&(fc=5)}function tu(e,t){do{var n=al(e.alternate,e);if(null!==n)return n.flags&=32767,void(ac=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ac=e);ac=e=n}while(null!==e);fc=6,ac=null}function nu(e,t,n,r,a,o,s,l,c){e.cancelPendingCommit=null;do{su()}while(0!==_c);if(0!==(6&nc))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(o=t.lanes|t.childLanes,function(e,t,n,r,a,o){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var s=e.entanglements,l=e.expirationTimes,c=e.hiddenUpdates;for(n=i&~n;0<n;){var u=31-pe(n),d=1<<u;s[u]=0,l[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var p=f[u];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ce(e,r,0),0!==o&&0===a&&0!==e.tag&&(e.suspendedLanes|=o&~(i&~t))}(e,n,o|=Or,s,l,c),e===rc&&(ac=rc=null,oc=0),Oc=t,Cc=e,Tc=n,Rc=o,Pc=a,Ac=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,X(oe,function(){return lu(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=z.T,z.T=null,a=L.p,L.p=2,s=nc,nc|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(g){n=null;break e}var s=0,l=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==o||0!==r&&3!==f.nodeType||(c=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++u===a&&(l=s),p===o&&++d===r&&(c=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Sl=t;null!==Sl;)if(e=(t=Sl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Sl=e;else for(;null!==Sl;){switch(o=(t=Sl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==o){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,r=n.stateNode;try{var m=gs(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,o),r.__reactInternalSnapshotBeforeUpdate=e}catch(y){uu(n,n.return,y)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,Sl=e;break}Sl=t.return}}(e,t)}finally{nc=s,L.p=a,z.T=r}}_c=1,ru(),au(),ou()}}function ru(){if(1===_c){_c=0;var e=Cc,t=Oc,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=z.T,z.T=null;var r=L.p;L.p=2;var a=nc;nc|=4;try{zl(t,e);var o=nd,i=er(e.containerInfo),s=o.focusedElem,l=o.selectionRange;if(i!==s&&s&&s.ownerDocument&&Zn(s.ownerDocument.documentElement,s)){if(null!==l&&tr(s)){var c=l.start,u=l.end;if(void 0===u&&(u=c),"selectionStart"in s)s.selectionStart=c,s.selectionEnd=Math.min(u,s.value.length);else{var d=s.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=s.textContent.length,m=Math.min(l.start,h),g=void 0===l.end?m:Math.min(l.end,h);!p.extend&&m>g&&(i=g,g=m,m=i);var y=Jn(s,m),v=Jn(s,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(d=[],p=s;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof s.focus&&s.focus(),s=0;s<d.length;s++){var w=d[s];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nf=!!td,nd=td=null}finally{nc=a,L.p=r,z.T=n}}e.current=t,_c=2}}function au(){if(2===_c){_c=0;var e=Cc,t=Oc,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=z.T,z.T=null;var r=L.p;L.p=2;var a=nc;nc|=4;try{El(e,t.alternate,t)}finally{nc=a,L.p=r,z.T=n}}_c=3}}function ou(){if(4===_c||3===_c){_c=0,ee();var e=Cc,t=Oc,n=Tc,r=Ac;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?_c=5:(_c=0,Oc=Cc=null,iu(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Ec=null),Re(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==r){t=z.T,a=L.p,L.p=2,z.T=null;try{for(var o=e.onRecoverableError,i=0;i<r.length;i++){var s=r[i];o(s.value,{componentStack:s.stack})}}finally{z.T=t,L.p=a}}0!==(3&Tc)&&su(),xu(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Nc?jc++:(jc=0,Nc=e):jc=0,Su(0,!1)}}function iu(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,za(t)))}function su(e){return ru(),au(),ou(),lu()}function lu(){if(5!==_c)return!1;var e=Cc,t=Rc;Rc=0;var n=Re(Tc),r=z.T,a=L.p;try{L.p=32>n?32:n,z.T=null,n=Pc,Pc=null;var o=Cc,s=Tc;if(_c=0,Oc=Cc=null,Tc=0,0!==(6&nc))throw Error(i(331));var l=nc;if(nc|=4,Xl(o.current),ql(o,o.current,s,n),nc=l,Su(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,o)}catch(c){}return!0}finally{L.p=a,z.T=r,iu(e,t)}}function cu(e,t,n){t=Er(n,t),null!==(e=oo(e,t=Ss(e.stateNode,t,2),2))&&(_e(e,2),xu(e))}function uu(e,t,n){if(3===e.tag)cu(e,e,n);else for(;null!==t;){if(3===t.tag){cu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Ec||!Ec.has(r))){e=Er(n,e),null!==(r=oo(t,n=Es(2),2))&&(_s(n,r,t,e),_e(r,2),xu(r));break}}t=t.return}}function du(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tc;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(uc=!0,a.add(n),e=fu.bind(null,e,t,n),t.then(e,e))}function fu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rc===e&&(oc&n)===n&&(4===fc||3===fc&&(62914560&oc)===oc&&300>te()-kc?0===(2&nc)&&qc(e,0):mc|=n,yc===oc&&(yc=0)),xu(e)}function pu(e,t){0===t&&(t=Se()),null!==(e=Ar(e,t))&&(_e(e,t),xu(e))}function hu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pu(e,n)}function mu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),pu(e,n)}var gu=null,yu=null,vu=!1,bu=!1,wu=!1,ku=0;function xu(e){e!==yu&&null===e.next&&(null===yu?gu=yu=e:yu=yu.next=e),bu=!0,vu||(vu=!0,dd(function(){0!==(6&nc)?X(re,Eu):_u()}))}function Su(e,t){if(!wu&&bu){wu=!0;do{for(var n=!1,r=gu;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var o=0;else{var i=r.suspendedLanes,s=r.pingedLanes;o=(1<<31-pe(42|e)+1)-1,o=201326741&(o&=a&~(i&~s))?201326741&o|1:o?2|o:0}0!==o&&(n=!0,Tu(r,o))}else o=oc,0===(3&(o=be(r,r===rc?o:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,o)||(n=!0,Tu(r,o));r=r.next}}while(n);wu=!1}}function Eu(){_u()}function _u(){bu=vu=!1;var e=0;0!==ku&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==sd&&(sd=e,!0);return sd=null,!1}()&&(e=ku),ku=0);for(var t=te(),n=null,r=gu;null!==r;){var a=r.next,o=Cu(r,t);0===o?(r.next=null,null===n?gu=a:n.next=a,null===a&&(yu=n)):(n=r,(0!==e||0!==(3&o))&&(bu=!0)),r=a}Su(e,!1)}function Cu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=-62914561&e.pendingLanes;0<o;){var i=31-pe(o),s=1<<i,l=a[i];-1===l?0!==(s&n)&&0===(s&r)||(a[i]=ke(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}if(n=oc,n=be(e,e===(t=rc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ic||9===ic)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&J(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&J(r),Re(n)){case 2:case 8:n=ae;break;case 32:default:n=oe;break;case 268435456:n=se}return r=Ou.bind(null,e),n=X(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&J(r),e.callbackPriority=2,e.callbackNode=null,2}function Ou(e,t){if(0!==_c&&5!==_c)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(su()&&e.callbackNode!==n)return null;var r=oc;return 0===(r=be(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Fc(e,r,t),Cu(e,te()),null!=e.callbackNode&&e.callbackNode===n?Ou.bind(null,e):null)}function Tu(e,t){if(su())return null;Fc(e,t,!0)}function Ru(){return 0===ku&&(ku=xe()),ku}function Pu(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Rt(""+e)}function Au(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var ju=0;ju<kr.length;ju++){var Nu=kr[ju];xr(Nu.toLowerCase(),"on"+(Nu[0].toUpperCase()+Nu.slice(1)))}xr(pr,"onAnimationEnd"),xr(hr,"onAnimationIteration"),xr(mr,"onAnimationStart"),xr("dblclick","onDoubleClick"),xr("focusin","onFocus"),xr("focusout","onBlur"),xr(gr,"onTransitionRun"),xr(yr,"onTransitionStart"),xr(vr,"onTransitionCancel"),xr(br,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lu=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zu));function Du(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,c=s.currentTarget;if(s=s.listener,l!==o&&a.isPropagationStopped())break e;o=s,a.currentTarget=c;try{o(a)}catch(u){ys(u)}a.currentTarget=null,o=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,c=s.currentTarget,s=s.listener,l!==o&&a.isPropagationStopped())break e;o=s,a.currentTarget=c;try{o(a)}catch(u){ys(u)}a.currentTarget=null,o=l}}}}function Fu(e,t){var n=t[Le];void 0===n&&(n=t[Le]=new Set);var r=e+"__bubble";n.has(r)||(Bu(t,e,2,!1),n.add(r))}function Mu(e,t,n){var r=0;t&&(r|=4),Bu(n,e,r,t)}var Iu="_reactListening"+Math.random().toString(36).slice(2);function Uu(e){if(!e[Iu]){e[Iu]=!0,Ve.forEach(function(t){"selectionchange"!==t&&(Lu.has(t)||Mu(t,!1,e),Mu(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Iu]||(t[Iu]=!0,Mu("selectionchange",!1,t))}}function Bu(e,t,n,r){switch(uf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=of}n=a.bind(null,t,n,e),a=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hu(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===a)break;if(4===i)for(i=r.return;null!==i;){var c=i.tag;if((3===c||4===c)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==s;){if(null===(i=Be(s)))return;if(5===(c=i.tag)||6===c||26===c||27===c){r=o=i;continue e}s=s.parentNode}}r=r.return}Dt(function(){var r=o,a=At(n),i=[];e:{var s=wr.get(e);if(void 0!==s){var c=Zt,u=e;switch(e){case"keypress":if(0===$t(n))break e;case"keydown":case"keyup":c=mn;break;case"focusin":u="focus",c=on;break;case"focusout":u="blur",c=on;break;case"beforeblur":case"afterblur":c=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=yn;break;case pr:case hr:case mr:c=sn;break;case br:c=vn;break;case"scroll":case"scrollend":c=tn;break;case"wheel":c=bn;break;case"copy":case"cut":case"paste":c=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gn;break;case"toggle":case"beforetoggle":c=wn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==s?s+"Capture":null:s;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Ft(m,p))&&d.push(qu(m,g,h)),f)break;m=m.return}0<d.length&&(s=new c(s,u,null,n,a),i.push({event:s,listeners:d}))}}if(0===(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Pt||!(u=n.relatedTarget||n.fromElement)||!Be(u)&&!u[ze])&&(c||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?Be(u):null)&&(f=l(u),d=u.tag,u!==f||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=rn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==c?s:qe(c),h=null==u?s:qe(u),(s=new d(g,m+"leave",c,n,a)).target=f,s.relatedTarget=h,g=null,Be(a)===r&&((d=new d(p,m+"enter",u,n,a)).target=h,d.relatedTarget=f,g=d),f=g,c&&u)e:{for(p=u,m=0,h=d=c;h;h=$u(h))m++;for(h=0,g=p;g;g=$u(g))h++;for(;0<m-h;)d=$u(d),m--;for(;0<h-m;)p=$u(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=$u(d),p=$u(p)}d=null}else d=null;null!==c&&Vu(i,s,c,d,!1),null!==u&&null!==f&&Vu(i,f,u,d,!0)}if("select"===(c=(s=r?qe(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===c&&"file"===s.type)var y=Mn;else if(jn(s))if(In)y=Qn;else{y=Vn;var v=$n}else!(c=s.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Ct(r.elementType)&&(y=Mn):y=Kn;switch(y&&(y=y(e,r))?Nn(i,y,n,a):(v&&v(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&vt(s,"number",s.value)),v=r?qe(r):window,e){case"focusin":(jn(v)||"true"===v.contentEditable)&&(rr=v,ar=r,or=null);break;case"focusout":or=ar=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,sr(i,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":sr(i,n,a)}var b;if(xn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Pn?Tn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(_n&&"ko"!==n.locale&&(Pn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Pn&&(b=Wt()):(Ht="value"in(Bt=a)?Bt.value:Bt.textContent,Pn=!0)),0<(v=Wu(r,w)).length&&(w=new cn(w,e,null,n,a),i.push({event:w,listeners:v}),b?w.data=b:null!==(b=Rn(n))&&(w.data=b))),(b=En?function(e,t){switch(e){case"compositionend":return Rn(t);case"keypress":return 32!==t.which?null:(On=!0,Cn);case"textInput":return(e=t.data)===Cn&&On?null:e;default:return null}}(e,n):function(e,t){if(Pn)return"compositionend"===e||!xn&&Tn(e,t)?(e=Wt(),qt=Ht=Bt=null,Pn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _n&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Wu(r,"onBeforeInput")).length&&(v=new cn("onBeforeInput","beforeinput",null,n,a),i.push({event:v,listeners:w}),v.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var o=Pu((a[Ne]||null).action),i=r.submitter;i&&null!==(t=(t=i[Ne]||null)?Pu(t.formAction):i.getAttribute("formAction"))&&(o=t,i=null);var s=new Zt("action","action",null,r,a);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==ku){var e=i?Au(a,i):new FormData(a);Ai(n,{pending:!0,data:e,method:a.method,action:o},null,e)}}else"function"===typeof o&&(s.preventDefault(),e=i?Au(a,i):new FormData(a),Ai(n,{pending:!0,data:e,method:a.method,action:o},o,e))},currentTarget:a}]})}}(i,e,r,n,a)}Du(i,t)})}function qu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wu(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===o||(null!=(a=Ft(e,n))&&r.unshift(qu(e,a,o)),null!=(a=Ft(e,t))&&r.push(qu(e,a,o))),3===e.tag)return r;e=e.return}return[]}function $u(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Vu(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,c=s.stateNode;if(s=s.tag,null!==l&&l===r)break;5!==s&&26!==s&&27!==s||null===c||(l=c,a?null!=(c=Ft(n,o))&&i.unshift(qu(n,c,l)):a||null!=(c=Ft(n,o))&&i.push(qu(n,c,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Ku=/\r\n?/g,Qu=/\u0000|\uFFFD/g;function Yu(e){return("string"===typeof e?e:""+e).replace(Ku,"\n").replace(Qu,"")}function Gu(e,t){return t=Yu(t),Yu(e)===t}function Xu(){}function Ju(e,t,n,r,a,o){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||xt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&xt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":_t(e,r,o);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Rt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof o&&("formAction"===n?("input"!==t&&Ju(e,t,"name",a.name,a,null),Ju(e,t,"formEncType",a.formEncType,a,null),Ju(e,t,"formMethod",a.formMethod,a,null),Ju(e,t,"formTarget",a.formTarget,a,null)):(Ju(e,t,"encType",a.encType,a,null),Ju(e,t,"method",a.method,a,null),Ju(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Rt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Xu);break;case"onScroll":null!=r&&Fu("scroll",e);break;case"onScrollEnd":null!=r&&Fu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Rt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Fu("beforetoggle",e),Fu("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Ot.get(n)||n,r)}}function Zu(e,t,n,r,a,o){switch(n){case"style":_t(e,r,o);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?xt(e,r):("number"===typeof r||"bigint"===typeof r)&&xt(e,""+r);break;case"onScroll":null!=r&&Fu("scroll",e);break;case"onScrollEnd":null!=r&&Fu("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Xu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(o=null!=(o=e[Ne]||null)?o[n]:null)&&e.removeEventListener(t,o,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof o&&null!==o&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Fu("error",e),Fu("load",e);var r,a=!1,o=!1;for(r in n)if(n.hasOwnProperty(r)){var s=n[r];if(null!=s)switch(r){case"src":a=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Ju(e,t,r,s,n,null)}}return o&&Ju(e,t,"srcSet",n.srcSet,n,null),void(a&&Ju(e,t,"src",n.src,n,null));case"input":Fu("invalid",e);var l=r=s=o=null,c=null,u=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":o=d;break;case"type":s=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Ju(e,t,a,d,n,null)}}return yt(e,r,l,c,u,s,o,!1),void dt(e);case"select":for(o in Fu("invalid",e),a=s=r=null,n)if(n.hasOwnProperty(o)&&null!=(l=n[o]))switch(o){case"value":r=l;break;case"defaultValue":s=l;break;case"multiple":a=l;default:Ju(e,t,o,l,n,null)}return t=r,n=s,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(s in Fu("invalid",e),r=o=a=null,n)if(n.hasOwnProperty(s)&&null!=(l=n[s]))switch(s){case"value":a=l;break;case"defaultValue":o=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(i(91));break;default:Ju(e,t,s,l,n,null)}return kt(e,a,o,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))if("selected"===c)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Ju(e,t,c,a,n,null);return;case"dialog":Fu("beforetoggle",e),Fu("toggle",e),Fu("cancel",e),Fu("close",e);break;case"iframe":case"object":Fu("load",e);break;case"video":case"audio":for(a=0;a<zu.length;a++)Fu(zu[a],e);break;case"image":Fu("error",e),Fu("load",e);break;case"details":Fu("toggle",e);break;case"embed":case"source":case"link":Fu("error",e),Fu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Ju(e,t,u,a,n,null)}return;default:if(Ct(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zu(e,t,d,a,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(a=n[l])&&Ju(e,t,l,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var sd=null;var ld="function"===typeof setTimeout?setTimeout:void 0,cd="function"===typeof clearTimeout?clearTimeout:void 0,ud="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ud?function(e){return ud.resolve(null).then(e).catch(fd)}:ld;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function hd(e,t){var n=t,r=0,a=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&kd(i.documentElement),2&n&&kd(i.body),4&n)for(kd(n=i.head),i=n.firstChild;i;){var s=i.nextSibling,l=i.nodeName;i[Ie]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=s}}if(0===a)return e.removeChild(o),void Tf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=o}while(n);Tf(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Ue(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ue(e)}var xd=new Map,Sd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var _d=L.d;L.d={f:function(){var e=_d.f(),t=Bc();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Ni(t):_d.r(e)},D:function(e){_d.D(e),Od("dns-prefetch",e,null)},C:function(e,t){_d.C(e,t),Od("preconnect",e,t)},L:function(e,t,n){_d.L(e,t,n);var r=Cd;if(r&&e&&t){var a='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+mt(n.imageSizes)+'"]')):a+='[href="'+mt(e)+'"]';var o=a;switch(t){case"style":o=Rd(e);break;case"script":o=jd(e)}xd.has(o)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),xd.set(o,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Pd(o))||"script"===t&&r.querySelector(Nd(o))||(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}},m:function(e,t){_d.m(e,t);var n=Cd;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',o=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=jd(e)}if(!xd.has(o)&&(e=f({rel:"modulepreload",href:e},t),xd.set(o,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Nd(o)))return}ed(r=n.createElement("link"),"link",e),$e(r),n.head.appendChild(r)}}},X:function(e,t){_d.X(e,t);var n=Cd;if(n&&e){var r=We(n).hoistableScripts,a=jd(e),o=r.get(a);o||((o=n.querySelector(Nd(a)))||(e=f({src:e,async:!0},t),(t=xd.get(a))&&Fd(e,t),$e(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}},S:function(e,t,n){_d.S(e,t,n);var r=Cd;if(r&&e){var a=We(r).hoistableStyles,o=Rd(e);t=t||"default";var i=a.get(o);if(!i){var s={loading:0,preload:null};if(i=r.querySelector(Pd(o)))s.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=xd.get(o))&&Dd(e,n);var l=i=r.createElement("link");$e(l),ed(l,"link",e),l._p=new Promise(function(e,t){l.onload=e,l.onerror=t}),l.addEventListener("load",function(){s.loading|=1}),l.addEventListener("error",function(){s.loading|=2}),s.loading|=4,Ld(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:s},a.set(o,i)}}},M:function(e,t){_d.M(e,t);var n=Cd;if(n&&e){var r=We(n).hoistableScripts,a=jd(e),o=r.get(a);o||((o=n.querySelector(Nd(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=xd.get(a))&&Fd(e,t),$e(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}}};var Cd="undefined"===typeof document?null:document;function Od(e,t,n){var r=Cd;if(r&&"string"===typeof t&&t){var a=mt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}}function Td(e,t,n,r){var a,o,s,l,c=(c=W.current)?Ed(c):null;if(!c)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Rd(n.href),(r=(n=We(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Rd(n.href);var u=We(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(Pd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),xd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},xd.set(e,n),u||(a=c,o=e,s=n,l=d.state,a.querySelector('link[rel="preload"][as="style"]['+o+"]")?l.loading=1:(o=a.createElement("link"),l.preload=o,o.addEventListener("load",function(){return l.loading|=1}),o.addEventListener("error",function(){return l.loading|=2}),ed(o,"link",s),$e(o),a.head.appendChild(o))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=jd(n),(r=(n=We(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Rd(e){return'href="'+mt(e)+'"'}function Pd(e){return'link[rel="stylesheet"]['+e+"]"}function Ad(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function jd(e){return'[src="'+mt(e)+'"]'}function Nd(e){return"script[async]"+e}function zd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,$e(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return $e(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Ld(r,n.precedence,e),t.instance=r;case"stylesheet":a=Rd(n.href);var o=e.querySelector(Pd(a));if(o)return t.state.loading|=4,t.instance=o,$e(o),o;r=Ad(n),(a=xd.get(a))&&Dd(r,a),$e(o=(e.ownerDocument||e).createElement("link"));var s=o;return s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(o,"link",r),t.state.loading|=4,Ld(o,n.precedence,e),t.instance=o;case"script":return o=jd(n.src),(a=e.querySelector(Nd(o)))?(t.instance=a,$e(a),a):(r=n,(a=xd.get(o))&&Fd(r=f({},n),a),$e(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Ld(r,n.precedence,e));return t.instance}function Ld(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,o=a,i=0;i<r.length;i++){var s=r[i];if(s.dataset.precedence===t)o=s;else if(o!==a)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Md=null;function Id(e,t,n){if(null===Md){var r=new Map,a=Md=new Map;a.set(n,r)}else(r=(a=Md).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var o=n[a];if(!(o[Ie]||o[je]||"link"===e&&"stylesheet"===o.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==o.namespaceURI){var i=o.getAttribute(t)||"";i=e+i;var s=r.get(i);s?s.push(o):r.set(i,[o])}}return r}function Ud(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Hd=null;function qd(){}function Wd(){if(this.count--,0===this.count)if(this.stylesheets)Vd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var $d=null;function Vd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,$d=new Map,t.forEach(Kd,e),$d=null,Wd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=$d.get(e);if(n)var r=n.get(null);else{n=new Map,$d.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<a.length;o++){var i=a[o];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(a=t.instance).getAttribute("data-precedence"),(o=n.get(i)||r)===r&&n.set(null,a),n.set(i,a),this.count++,r=Wd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),o?o.parentNode.insertBefore(a,o.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Qd={$$typeof:k,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Yd(e,t,n,r,a,o,i,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=o,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function Gd(e,t,n,r,a,o,i,s,l,c,u,d){return e=new Yd(e,t,n,i,s,l,c,d),t=1,!0===o&&(t|=24),o=Dr(3,null,null,t),e.current=o,o.stateNode=e,(t=Na()).refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:r,isDehydrated:n,cache:t},no(o),e}function Xd(e){return e?e=zr:zr}function Jd(e,t,n,r,a,o){a=Xd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ao(t)).payload={element:n},null!==(o=void 0===o?null:o)&&(r.callback=o),null!==(n=oo(e,r,t))&&(Dc(n,0,t),io(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=Ar(e,67108864);null!==t&&Dc(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=z.T;z.T=null;var o=L.p;try{L.p=2,of(e,t,n,r)}finally{L.p=o,z.T=a}}function af(e,t,n,r){var a=z.T;z.T=null;var o=L.p;try{L.p=8,of(e,t,n,r)}finally{L.p=o,z.T=a}}function of(e,t,n,r){if(nf){var a=sf(r);if(null===a)Hu(e,t,r,lf,n),bf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=wf(ff,e,t,n,r,a),!0;case"dragenter":return pf=wf(pf,e,t,n,r,a),!0;case"mouseover":return hf=wf(hf,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return mf.set(o,wf(mf.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,gf.set(o,wf(gf.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==a;){var o=He(a);if(null!==o)switch(o.tag){case 3:if((o=o.stateNode).current.memoizedState.isDehydrated){var i=ve(o.pendingLanes);if(0!==i){var s=o;for(s.pendingLanes|=2,s.entangledLanes|=2;i;){var l=1<<31-pe(i);s.entanglements[1]|=l,i&=~l}xu(o),0===(6&nc)&&(xc=te()+500,Su(0,!1))}}break;case 13:null!==(s=Ar(o,2))&&Dc(s,0,2),Bc(),ef(o,2)}if(null===(o=sf(r))&&Hu(e,t,r,lf,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Hu(e,t,r,null,n)}}function sf(e){return cf(e=At(e))}var lf=null;function cf(e){if(lf=null,null!==(e=Be(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=c(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lf=e,null}function uf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case oe:case ie:return 32;case se:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,hf=null,mf=new Map,gf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":hf=null;break;case"pointerover":case"pointerout":mf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function wf(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function kf(e){var t=Be(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=c(n)))return e.blockedOn=t,void function(e,t){var n=L.p;try{return L.p=e,t()}finally{L.p=n}}(e.priority,function(){if(13===n.tag){var e=zc();e=Te(e);var t=Ar(n,e);null!==t&&Dc(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function xf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=sf(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Pt=r,n.target.dispatchEvent(r),Pt=null,t.shift()}return!0}function Sf(e,t,n){xf(e)&&n.delete(t)}function Ef(){df=!1,null!==ff&&xf(ff)&&(ff=null),null!==pf&&xf(pf)&&(pf=null),null!==hf&&xf(hf)&&(hf=null),mf.forEach(Sf),gf.forEach(Sf)}function _f(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var Cf=null;function Of(e){Cf!==e&&(Cf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Cf===e&&(Cf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===cf(r||n))continue;break}var o=He(n);null!==o&&(e.splice(t,3),t-=3,Ai(o,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Tf(e){function t(t){return _f(t,e)}null!==ff&&_f(ff,e),null!==pf&&_f(pf,e),null!==hf&&_f(hf,e),mf.forEach(t),gf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)kf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],o=n[r+1],i=a[Ne]||null;if("function"===typeof o)i||Of(n);else if(i){var s=null;if(o&&o.hasAttribute("formAction")){if(a=o,i=o[Ne]||null)s=i.formAction;else if(null!==cf(a))continue}else s=i.action;"function"===typeof s?n[r+1]=s:(n.splice(r,3),r-=3),Of(n)}}}function Rf(e){this._internalRoot=e}function Pf(e){this._internalRoot=e}Pf.prototype.render=Rf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Jd(t.current,zc(),e,t,null,null)},Pf.prototype.unmount=Rf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Jd(e.current,2,null,e,null,null),Bc(),t[ze]=null}},Pf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&kf(e)}};var Af=a.version;if("19.1.0"!==Af)throw Error(i(527,Af,"19.1.0"));L.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return u(a),e;if(o===r)return u(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,c=a.child;c;){if(c===n){s=!0,n=a,r=o;break}if(c===r){s=!0,r=a,n=o;break}c=c.sibling}if(!s){for(c=o.child;c;){if(c===n){s=!0,n=o,r=a;break}if(c===r){s=!0,r=o,n=a;break}c=c.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var jf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Nf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Nf.isDisabled&&Nf.supportsFiber)try{ue=Nf.inject(jf),de=Nf}catch(Lf){}}t.createRoot=function(e,t){if(!s(e))throw Error(i(299));var n=!1,r="",a=vs,o=bs,l=ws;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(o=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Gd(e,1,!1,null,0,n,r,a,o,l,0,null),e[ze]=t.current,Uu(e),new Rf(t)},t.hydrateRoot=function(e,t,n){if(!s(e))throw Error(i(299));var r=!1,a="",o=vs,l=bs,c=ws,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(o=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Gd(e,1,!0,t,0,r,a,o,l,c,0,u)).context=Xd(null),n=t.current,(a=ao(r=Te(r=zc()))).callback=null,oo(n,a,r),n=r,t.current.lanes=n,_e(t,n),xu(t),e[ze]=t.current,Uu(e),new Pf(t)},t.version="19.1.0"},29:function(e,t,n){var r;r=function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="./src/react-webcam.tsx")}({"./src/react-webcam.tsx":function(e,t,n){"use strict";n.r(t);var r=n("react"),a=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},o.apply(this,arguments)},i=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};function s(){return!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)}"undefined"!==typeof window&&(void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e){var t=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return t?new Promise(function(n,r){t.call(navigator,e,n,r)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}));var l=function(e){function t(t){var n=e.call(this,t)||this;return n.canvas=null,n.ctx=null,n.requestUserMediaId=0,n.unmounted=!1,n.state={hasUserMedia:!1},n}return a(t,e),t.prototype.componentDidMount=function(){var e=this.state,t=this.props;this.unmounted=!1,s()?(e.hasUserMedia||this.requestUserMedia(),t.children&&"function"!=typeof t.children&&console.warn("children must be a function")):t.onUserMediaError("getUserMedia not supported")},t.prototype.componentDidUpdate=function(e){var t=this.props;if(s()){var n=JSON.stringify(e.audioConstraints)!==JSON.stringify(t.audioConstraints),r=JSON.stringify(e.videoConstraints)!==JSON.stringify(t.videoConstraints),a=e.minScreenshotWidth!==t.minScreenshotWidth,o=e.minScreenshotHeight!==t.minScreenshotHeight;(r||a||o)&&(this.canvas=null,this.ctx=null),(n||r)&&(this.stopAndCleanup(),this.requestUserMedia())}else t.onUserMediaError("getUserMedia not supported")},t.prototype.componentWillUnmount=function(){this.unmounted=!0,this.stopAndCleanup()},t.stopMediaStream=function(e){e&&(e.getVideoTracks&&e.getAudioTracks?(e.getVideoTracks().map(function(t){e.removeTrack(t),t.stop()}),e.getAudioTracks().map(function(t){e.removeTrack(t),t.stop()})):e.stop())},t.prototype.stopAndCleanup=function(){var e=this.state;e.hasUserMedia&&(t.stopMediaStream(this.stream),e.src&&window.URL.revokeObjectURL(e.src))},t.prototype.getScreenshot=function(e){var t=this.state,n=this.props;if(!t.hasUserMedia)return null;var r=this.getCanvas(e);return r&&r.toDataURL(n.screenshotFormat,n.screenshotQuality)},t.prototype.getCanvas=function(e){var t=this.state,n=this.props;if(!this.video)return null;if(!t.hasUserMedia||!this.video.videoHeight)return null;if(!this.ctx){var r=this.video.videoWidth,a=this.video.videoHeight;if(!this.props.forceScreenshotSourceSize){var o=r/a;a=(r=n.minScreenshotWidth||this.video.clientWidth)/o,n.minScreenshotHeight&&a<n.minScreenshotHeight&&(r=(a=n.minScreenshotHeight)*o)}this.canvas=document.createElement("canvas"),this.canvas.width=(null===e||void 0===e?void 0:e.width)||r,this.canvas.height=(null===e||void 0===e?void 0:e.height)||a,this.ctx=this.canvas.getContext("2d")}var i=this.ctx,s=this.canvas;return i&&s&&(s.width=(null===e||void 0===e?void 0:e.width)||s.width,s.height=(null===e||void 0===e?void 0:e.height)||s.height,n.mirrored&&(i.translate(s.width,0),i.scale(-1,1)),i.imageSmoothingEnabled=n.imageSmoothing,i.drawImage(this.video,0,0,(null===e||void 0===e?void 0:e.width)||s.width,(null===e||void 0===e?void 0:e.height)||s.height),n.mirrored&&(i.scale(-1,1),i.translate(-s.width,0))),s},t.prototype.requestUserMedia=function(){var e=this,n=this.props,r=function(r,a){var o={video:"undefined"===typeof a||a};n.audio&&(o.audio="undefined"===typeof r||r),e.requestUserMediaId++;var i=e.requestUserMediaId;navigator.mediaDevices.getUserMedia(o).then(function(n){e.unmounted||i!==e.requestUserMediaId?t.stopMediaStream(n):e.handleUserMedia(null,n)}).catch(function(t){e.handleUserMedia(t)})};if("mediaDevices"in navigator)r(n.audioConstraints,n.videoConstraints);else{var a=function(e){return{optional:[{sourceId:e}]}},o=function(e){var t=e.deviceId;return"string"===typeof t?t:Array.isArray(t)&&t.length>0?t[0]:"object"===typeof t&&t.ideal?t.ideal:null};MediaStreamTrack.getSources(function(e){var t=null,i=null;e.forEach(function(e){"audio"===e.kind?t=e.id:"video"===e.kind&&(i=e.id)});var s=o(n.audioConstraints);s&&(t=s);var l=o(n.videoConstraints);l&&(i=l),r(a(t),a(i))})}},t.prototype.handleUserMedia=function(e,t){var n=this.props;if(e||!t)return this.setState({hasUserMedia:!1}),void n.onUserMediaError(e);this.stream=t;try{this.video&&(this.video.srcObject=t),this.setState({hasUserMedia:!0})}catch(r){this.setState({hasUserMedia:!0,src:window.URL.createObjectURL(t)})}n.onUserMedia(t)},t.prototype.render=function(){var e=this,t=this.state,n=this.props,a=n.audio,s=(n.forceScreenshotSourceSize,n.disablePictureInPicture),l=(n.onUserMedia,n.onUserMediaError,n.screenshotFormat,n.screenshotQuality,n.minScreenshotWidth,n.minScreenshotHeight,n.audioConstraints,n.videoConstraints,n.imageSmoothing,n.mirrored),c=n.style,u=void 0===c?{}:c,d=n.children,f=i(n,["audio","forceScreenshotSourceSize","disablePictureInPicture","onUserMedia","onUserMediaError","screenshotFormat","screenshotQuality","minScreenshotWidth","minScreenshotHeight","audioConstraints","videoConstraints","imageSmoothing","mirrored","style","children"]),p=l?o(o({},u),{transform:(u.transform||"")+" scaleX(-1)"}):u,h={getScreenshot:this.getScreenshot.bind(this)};return r.createElement(r.Fragment,null,r.createElement("video",o({autoPlay:!0,disablePictureInPicture:s,src:t.src,muted:!a,playsInline:!0,ref:function(t){e.video=t},style:p},f)),d&&d(h))},t.defaultProps={audio:!1,disablePictureInPicture:!1,forceScreenshotSourceSize:!1,imageSmoothing:!0,mirrored:!1,onUserMedia:function(){},onUserMediaError:function(){},screenshotFormat:"image/webp",screenshotQuality:.92},t}(r.Component);t.default=l},react:function(t,n){t.exports=e}}).default},e.exports=r(n(43))},43:(e,t,n)=>{"use strict";e.exports=n(288)},288:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var k=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function E(e,t,r,a,o,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function _(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(){}function R(e,t,a,o,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,c,u=!1;if(null===e)u=!0;else switch(s){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case f:return R((u=e._init)(e._payload),t,a,o,i)}}if(u)return i=i(e),u=""===o?"."+O(e,0):o,k(i)?(a="",null!=u&&(a=u.replace(C,"$&/")+"/"),R(i,t,a,"",function(e){return e})):null!=i&&(_(i)&&(l=i,c=a+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(C,"$&/")+"/")+u,i=E(l.type,c,void 0,0,0,l.props)),t.push(i)),1;u=0;var d,h=""===o?".":o+":";if(k(e))for(var m=0;m<e.length;m++)u+=R(o=e[m],t,a,s=h+O(o,m),i);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(o=e.next()).done;)u+=R(o=o.value,t,a,s=h+O(o,m++),i);else if("object"===s){if("function"===typeof e.then)return R(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(T,T):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,o,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function P(e,t,n){if(null==e)return e;var r=[],a=0;return R(e,r,"","",function(e){return t.call(n,e,a++)}),r}function A(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var j="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function N(){}t.Children={map:P,forEach:function(e,t,n){P(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=o,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return x.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=t)for(o in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,o)||"key"===o||"__self"===o||"__source"===o||"ref"===o&&void 0===t.ref||(r[o]=t[o]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var i=Array(o),s=0;s<o;s++)i[s]=arguments[s+2];r.children=i}return E(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,a={},o=null;if(null!=t)for(r in void 0!==t.key&&(o=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var i=arguments.length-2;if(1===i)a.children=n;else if(1<i){for(var s=Array(i),l=0;l<i;l++)s[l]=arguments[l+2];a.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===a[r]&&(a[r]=i[r]);return E(e,o,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:A}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=x.T,n={};x.T=n;try{var r=e(),a=x.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(N,j)}catch(o){j(o)}finally{x.T=t}},t.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},t.use=function(e){return x.H.use(e)},t.useActionState=function(e,t,n){return x.H.useActionState(e,t,n)},t.useCallback=function(e,t){return x.H.useCallback(e,t)},t.useContext=function(e){return x.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return x.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=x.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return x.H.useId()},t.useImperativeHandle=function(e,t,n){return x.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return x.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return x.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return x.H.useMemo(e,t)},t.useOptimistic=function(e,t){return x.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return x.H.useReducer(e,t,n)},t.useRef=function(e){return x.H.useRef(e)},t.useState=function(e){return x.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return x.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return x.H.useTransition()},t.version="19.1.0"},324:e=>{e.exports=function(e,t,n,r){var a=n?n.call(r,e,t):void 0;if(void 0!==a)return!!a;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;var o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),l=0;l<o.length;l++){var c=o[l];if(!s(c))return!1;var u=e[c],d=t[c];if(!1===(a=n?n.call(r,u,d,c):void 0)||void 0===a&&u!==d)return!1}return!0}},391:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{"use strict";e.exports=n(799)},672:(e,t,n)=>{"use strict";var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(a(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:s,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=i.p;try{if(l.T=null,i.p=2,e)return e()}finally{l.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,o="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:o}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:o,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var o in r={},t)"key"!==o&&(r[o]=t[o]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},853:(e,t,n)=>{"use strict";e.exports=n(896)},896:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var s=2*(r+1)-1,l=e[s],c=s+1,u=e[c];if(0>o(l,n))c<a&&0>o(u,l)?(e[r]=u,e[c]=n,r=c):(e[r]=l,e[s]=n,r=s);else{if(!(c<a&&0>o(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var c=[],u=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function x(e){if(g=!1,k(e),!m)if(null!==r(c))m=!0,E||(E=!0,S());else{var t=r(u);null!==t&&j(x,t.startTime-e)}}var S,E=!1,_=-1,C=5,O=-1;function T(){return!!y||!(t.unstable_now()-O<C)}function R(){if(y=!1,E){var e=t.unstable_now();O=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(_),_=-1),h=!0;var o=p;try{t:{for(k(e),f=r(c);null!==f&&!(f.expirationTime>e&&T());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var s=i(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof s){f.callback=s,k(e),n=!0;break t}f===r(c)&&a(c),k(e)}else a(c);f=r(c)}if(null!==f)n=!0;else{var l=r(u);null!==l&&j(x,l.startTime-e),n=!1}}break e}finally{f=null,p=o,h=!1}n=void 0}}finally{n?S():E=!1}}}if("function"===typeof w)S=function(){w(R)};else if("undefined"!==typeof MessageChannel){var P=new MessageChannel,A=P.port2;P.port1.onmessage=R,S=function(){A.postMessage(null)}}else S=function(){v(R,0)};function j(e,n){_=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(e.sortIndex=o,n(u,e),null===r(c)&&e===r(u)&&(g?(b(_),_=-1):g=!0,j(x,o-i))):(e.sortIndex=s,n(c,e),m||h||(m=!0,E||(E=!0,S()))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".a21126ec.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="training-frontend:";n.l=(r,a,o,i)=>{if(e[r])e[r].push(a);else{var s,l;if(void 0!==o)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+o),s.src=r),e[r]=[a];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=o);var i=n.p+n.u(t),s=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",s.name="ChunkLoadError",s.type=o,s.request=i,a[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,o,i=r[0],s=r[1],l=r[2],c=0;if(i.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)l(n)}for(t&&t(r);c<i.length;c++)o=i[c],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunktraining_frontend=self.webpackChunktraining_frontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0,(()=>{"use strict";var e={};n.r(e),n.d(e,{Decoder:()=>Ya,Encoder:()=>Ka,PacketType:()=>Va,protocol:()=>$a});var t={};n.r(t),n.d(t,{hasBrowserEnv:()=>ui,hasStandardBrowserEnv:()=>fi,hasStandardBrowserWebWorkerEnv:()=>pi,navigator:()=>di,origin:()=>hi});var r=n(43),a=n(391);function o(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}var i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};Object.create;function s(e,t,n){if(n||2===arguments.length)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"===typeof SuppressedError&&SuppressedError;var l=n(324),c=n.n(l),u="-ms-",d="-moz-",f="-webkit-",p="comm",h="rule",m="decl",g="@keyframes",y=Math.abs,v=String.fromCharCode,b=Object.assign;function w(e){return e.trim()}function k(e,t){return(e=t.exec(e))?e[0]:e}function x(e,t,n){return e.replace(t,n)}function S(e,t,n){return e.indexOf(t,n)}function E(e,t){return 0|e.charCodeAt(t)}function _(e,t,n){return e.slice(t,n)}function C(e){return e.length}function O(e){return e.length}function T(e,t){return t.push(e),e}function R(e,t){return e.filter(function(e){return!k(e,t)})}var P=1,A=1,j=0,N=0,z=0,L="";function D(e,t,n,r,a,o,i,s){return{value:e,root:t,parent:n,type:r,props:a,children:o,line:P,column:A,length:i,return:"",siblings:s}}function F(e,t){return b(D("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function M(e){for(;e.root;)e=F(e.root,{children:[e]});T(e,e.siblings)}function I(){return z=N>0?E(L,--N):0,A--,10===z&&(A=1,P--),z}function U(){return z=N<j?E(L,N++):0,A++,10===z&&(A=1,P++),z}function B(){return E(L,N)}function H(){return N}function q(e,t){return _(L,e,t)}function W(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function $(e){return P=A=1,j=C(L=e),N=0,[]}function V(e){return L="",e}function K(e){return w(q(N-1,G(91===e?e+2:40===e?e+1:e)))}function Q(e){for(;(z=B())&&z<33;)U();return W(e)>2||W(z)>3?"":" "}function Y(e,t){for(;--t&&U()&&!(z<48||z>102||z>57&&z<65||z>70&&z<97););return q(e,H()+(t<6&&32==B()&&32==U()))}function G(e){for(;U();)switch(z){case e:return N;case 34:case 39:34!==e&&39!==e&&G(z);break;case 40:41===e&&G(e);break;case 92:U()}return N}function X(e,t){for(;U()&&e+z!==57&&(e+z!==84||47!==B()););return"/*"+q(t,N-1)+"*"+v(47===e?e:U())}function J(e){for(;!W(B());)U();return q(e,N)}function Z(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function ee(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case m:return e.return=e.return||e.value;case p:return"";case g:return e.return=e.value+"{"+Z(e.children,r)+"}";case h:if(!C(e.value=e.props.join(",")))return""}return C(n=Z(e.children,r))?e.return=e.value+"{"+n+"}":""}function te(e,t,n){switch(function(e,t){return 45^E(e,0)?(((t<<2^E(e,0))<<2^E(e,1))<<2^E(e,2))<<2^E(e,3):0}(e,t)){case 5103:return f+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return f+e+e;case 4789:return d+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return f+e+d+e+u+e+e;case 5936:switch(E(e,t+11)){case 114:return f+e+u+x(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return f+e+u+x(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return f+e+u+x(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return f+e+u+e+e;case 6165:return f+e+u+"flex-"+e+e;case 5187:return f+e+x(e,/(\w+).+(:[^]+)/,f+"box-$1$2"+u+"flex-$1$2")+e;case 5443:return f+e+u+"flex-item-"+x(e,/flex-|-self/g,"")+(k(e,/flex-|baseline/)?"":u+"grid-row-"+x(e,/flex-|-self/g,""))+e;case 4675:return f+e+u+"flex-line-pack"+x(e,/align-content|flex-|-self/g,"")+e;case 5548:return f+e+u+x(e,"shrink","negative")+e;case 5292:return f+e+u+x(e,"basis","preferred-size")+e;case 6060:return f+"box-"+x(e,"-grow","")+f+e+u+x(e,"grow","positive")+e;case 4554:return f+x(e,/([^-])(transform)/g,"$1"+f+"$2")+e;case 6187:return x(x(x(e,/(zoom-|grab)/,f+"$1"),/(image-set)/,f+"$1"),e,"")+e;case 5495:case 3959:return x(e,/(image-set\([^]*)/,f+"$1$`$1");case 4968:return x(x(e,/(.+:)(flex-)?(.*)/,f+"box-pack:$3"+u+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+f+e+e;case 4200:if(!k(e,/flex-|baseline/))return u+"grid-column-align"+_(e,t)+e;break;case 2592:case 3360:return u+x(e,"template-","")+e;case 4384:case 3616:return n&&n.some(function(e,n){return t=n,k(e.props,/grid-\w+-end/)})?~S(e+(n=n[t].value),"span",0)?e:u+x(e,"-start","")+e+u+"grid-row-span:"+(~S(n,"span",0)?k(n,/\d+/):+k(n,/\d+/)-+k(e,/\d+/))+";":u+x(e,"-start","")+e;case 4896:case 4128:return n&&n.some(function(e){return k(e.props,/grid-\w+-start/)})?e:u+x(x(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return x(e,/(.+)-inline(.+)/,f+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(C(e)-1-t>6)switch(E(e,t+1)){case 109:if(45!==E(e,t+4))break;case 102:return x(e,/(.+:)(.+)-([^]+)/,"$1"+f+"$2-$3$1"+d+(108==E(e,t+3)?"$3":"$2-$3"))+e;case 115:return~S(e,"stretch",0)?te(x(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return x(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(t,n,r,a,o,i,s){return u+n+":"+r+s+(a?u+n+"-span:"+(o?i:+i-+r)+s:"")+e});case 4949:if(121===E(e,t+6))return x(e,":",":"+f)+e;break;case 6444:switch(E(e,45===E(e,14)?18:11)){case 120:return x(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+f+(45===E(e,14)?"inline-":"")+"box$3$1"+f+"$2$3$1"+u+"$2box$3")+e;case 100:return x(e,":",":"+u)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return x(e,"scroll-","scroll-snap-")+e}return e}function ne(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case m:return void(e.return=te(e.value,e.length,n));case g:return Z([F(e,{value:x(e.value,"@","@"+f)})],r);case h:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,function(t){switch(k(t,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":M(F(e,{props:[x(t,/:(read-\w+)/,":-moz-$1")]})),M(F(e,{props:[t]})),b(e,{props:R(n,r)});break;case"::placeholder":M(F(e,{props:[x(t,/:(plac\w+)/,":"+f+"input-$1")]})),M(F(e,{props:[x(t,/:(plac\w+)/,":-moz-$1")]})),M(F(e,{props:[x(t,/:(plac\w+)/,u+"input-$1")]})),M(F(e,{props:[t]})),b(e,{props:R(n,r)})}return""})}}function re(e){return V(ae("",null,null,null,[""],e=$(e),0,[0],e))}function ae(e,t,n,r,a,o,i,s,l){for(var c=0,u=0,d=i,f=0,p=0,h=0,m=1,g=1,b=1,w=0,k="",_=a,O=o,R=r,P=k;g;)switch(h=w,w=U()){case 40:if(108!=h&&58==E(P,d-1)){-1!=S(P+=x(K(w),"&","&\f"),"&\f",y(c?s[c-1]:0))&&(b=-1);break}case 34:case 39:case 91:P+=K(w);break;case 9:case 10:case 13:case 32:P+=Q(h);break;case 92:P+=Y(H()-1,7);continue;case 47:switch(B()){case 42:case 47:T(ie(X(U(),H()),t,n,l),l);break;default:P+="/"}break;case 123*m:s[c++]=C(P)*b;case 125*m:case 59:case 0:switch(w){case 0:case 125:g=0;case 59+u:-1==b&&(P=x(P,/\f/g,"")),p>0&&C(P)-d&&T(p>32?se(P+";",r,n,d-1,l):se(x(P," ","")+";",r,n,d-2,l),l);break;case 59:P+=";";default:if(T(R=oe(P,t,n,c,u,a,s,k,_=[],O=[],d,o),o),123===w)if(0===u)ae(P,t,R,R,_,o,d,s,O);else switch(99===f&&110===E(P,3)?100:f){case 100:case 108:case 109:case 115:ae(e,R,R,r&&T(oe(e,R,R,0,0,a,s,k,a,_=[],d,O),O),a,O,d,s,r?_:O);break;default:ae(P,R,R,R,[""],O,0,s,O)}}c=u=p=0,m=b=1,k=P="",d=i;break;case 58:d=1+C(P),p=h;default:if(m<1)if(123==w)--m;else if(125==w&&0==m++&&125==I())continue;switch(P+=v(w),w*m){case 38:b=u>0?1:(P+="\f",-1);break;case 44:s[c++]=(C(P)-1)*b,b=1;break;case 64:45===B()&&(P+=K(U())),f=B(),u=d=C(k=P+=J(H())),w++;break;case 45:45===h&&2==C(P)&&(m=0)}}return o}function oe(e,t,n,r,a,o,i,s,l,c,u,d){for(var f=a-1,p=0===a?o:[""],m=O(p),g=0,v=0,b=0;g<r;++g)for(var k=0,S=_(e,f+1,f=y(v=i[g])),E=e;k<m;++k)(E=w(v>0?p[k]+" "+S:x(S,/&\f/g,p[k])))&&(l[b++]=E);return D(e,t,n,0===a?h:s,l,c,u,d)}function ie(e,t,n,r){return D(e,t,n,p,v(z),_(e,2,-2),0,r)}function se(e,t,n,r,a){return D(e,t,n,m,_(e,0,r),_(e,r+1,-1),r,a)}var le={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ce="undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_ATTR)||"data-styled",ue="active",de="data-styled-version",fe="6.1.19",pe="/*!sc*/\n",he="undefined"!=typeof window&&"undefined"!=typeof document,me=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY)),ge=(new Set,Object.freeze([])),ye=Object.freeze({});function ve(e,t,n){return void 0===n&&(n=ye),e.theme!==n.theme&&e.theme||t||n.theme}var be=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),we=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,ke=/(^-|-$)/g;function xe(e){return e.replace(we,"-").replace(ke,"")}var Se=/(a)(d)/gi,Ee=function(e){return String.fromCharCode(e+(e>25?39:97))};function _e(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=Ee(t%52)+n;return(Ee(t%52)+n).replace(Se,"$1-$2")}var Ce,Oe=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},Te=function(e){return Oe(5381,e)};function Re(e){return _e(Te(e)>>>0)}function Pe(e){return e.displayName||e.name||"Component"}function Ae(e){return"string"==typeof e&&!0}var je="function"==typeof Symbol&&Symbol.for,Ne=je?Symbol.for("react.memo"):60115,ze=je?Symbol.for("react.forward_ref"):60112,Le={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},De={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Fe={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Me=((Ce={})[ze]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Ce[Ne]=Fe,Ce);function Ie(e){return("type"in(t=e)&&t.type.$$typeof)===Ne?Fe:"$$typeof"in e?Me[e.$$typeof]:Le;var t}var Ue=Object.defineProperty,Be=Object.getOwnPropertyNames,He=Object.getOwnPropertySymbols,qe=Object.getOwnPropertyDescriptor,We=Object.getPrototypeOf,$e=Object.prototype;function Ve(e,t,n){if("string"!=typeof t){if($e){var r=We(t);r&&r!==$e&&Ve(e,r,n)}var a=Be(t);He&&(a=a.concat(He(t)));for(var o=Ie(e),i=Ie(t),s=0;s<a.length;++s){var l=a[s];if(!(l in De||n&&n[l]||i&&l in i||o&&l in o)){var c=qe(t,l);try{Ue(e,l,c)}catch(e){}}}}return e}function Ke(e){return"function"==typeof e}function Qe(e){return"object"==typeof e&&"styledComponentId"in e}function Ye(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Ge(e,t){if(0===e.length)return"";for(var n=e[0],r=1;r<e.length;r++)n+=t?t+e[r]:e[r];return n}function Xe(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Je(e,t,n){if(void 0===n&&(n=!1),!n&&!Xe(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=Je(e[r],t[r]);else if(Xe(t))for(var r in t)e[r]=Je(e[r],t[r]);return e}function Ze(e,t){Object.defineProperty(e,"toString",{value:t})}function et(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var tt=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,a=r;e>=a;)if((a<<=1)<0)throw et(16,"".concat(e));this.groupSizes=new Uint32Array(a),this.groupSizes.set(n),this.length=a;for(var o=r;o<a;o++)this.groupSizes[o]=0}for(var i=this.indexOfGroup(e+1),s=(o=0,t.length);o<s;o++)this.tag.insertRule(i,t[o])&&(this.groupSizes[e]++,i++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var a=n;a<r;a++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),a=r+n,o=r;o<a;o++)t+="".concat(this.tag.getRule(o)).concat(pe);return t},e}(),nt=new Map,rt=new Map,at=1,ot=function(e){if(nt.has(e))return nt.get(e);for(;rt.has(at);)at++;var t=at++;return nt.set(e,t),rt.set(t,e),t},it=function(e,t){at=t+1,nt.set(e,t),rt.set(t,e)},st="style[".concat(ce,"][").concat(de,'="').concat(fe,'"]'),lt=new RegExp("^".concat(ce,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),ct=function(e,t,n){for(var r,a=n.split(","),o=0,i=a.length;o<i;o++)(r=a[o])&&e.registerName(t,r)},ut=function(e,t){for(var n,r=(null!==(n=t.textContent)&&void 0!==n?n:"").split(pe),a=[],o=0,i=r.length;o<i;o++){var s=r[o].trim();if(s){var l=s.match(lt);if(l){var c=0|parseInt(l[1],10),u=l[2];0!==c&&(it(u,c),ct(e,u,l[3]),e.getTag().insertRules(c,a)),a.length=0}else a.push(s)}}},dt=function(e){for(var t=document.querySelectorAll(st),n=0,r=t.length;n<r;n++){var a=t[n];a&&a.getAttribute(ce)!==ue&&(ut(e,a),a.parentNode&&a.parentNode.removeChild(a))}};function ft(){return n.nc}var pt=function(e){var t=document.head,n=e||t,r=document.createElement("style"),a=function(e){var t=Array.from(e.querySelectorAll("style[".concat(ce,"]")));return t[t.length-1]}(n),o=void 0!==a?a.nextSibling:null;r.setAttribute(ce,ue),r.setAttribute(de,fe);var i=ft();return i&&r.setAttribute("nonce",i),n.insertBefore(r,o),r},ht=function(){function e(e){this.element=pt(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var a=t[n];if(a.ownerNode===e)return a}throw et(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),mt=function(){function e(e){this.element=pt(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),gt=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),yt=he,vt={isServer:!he,useCSSOMInjection:!me},bt=function(){function e(e,t,n){void 0===e&&(e=ye),void 0===t&&(t={});var r=this;this.options=i(i({},vt),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&he&&yt&&(yt=!1,dt(this)),Ze(this,function(){return function(e){for(var t=e.getTag(),n=t.length,r="",a=function(n){var a=function(e){return rt.get(e)}(n);if(void 0===a)return"continue";var o=e.names.get(a),i=t.getGroup(n);if(void 0===o||!o.size||0===i.length)return"continue";var s="".concat(ce,".g").concat(n,'[id="').concat(a,'"]'),l="";void 0!==o&&o.forEach(function(e){e.length>0&&(l+="".concat(e,","))}),r+="".concat(i).concat(s,'{content:"').concat(l,'"}').concat(pe)},o=0;o<n;o++)a(o);return r}(r)})}return e.registerId=function(e){return ot(e)},e.prototype.rehydrate=function(){!this.server&&he&&dt(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(i(i({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new gt(n):t?new ht(n):new mt(n)}(this.options),new tt(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(ot(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(ot(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(ot(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),wt=/&/g,kt=/^\s*\/\/.*$/gm;function xt(e,t){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map(function(e){return"".concat(t," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=xt(e.children,t)),e})}function St(e){var t,n,r,a=void 0===e?ye:e,o=a.options,i=void 0===o?ye:o,s=a.plugins,l=void 0===s?ge:s,c=function(e,r,a){return a.startsWith(n)&&a.endsWith(n)&&a.replaceAll(n,"").length>0?".".concat(t):e},u=l.slice();u.push(function(e){e.type===h&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(wt,n).replace(r,c))}),i.prefix&&u.push(ne),u.push(ee);var d=function(e,a,o,s){void 0===a&&(a=""),void 0===o&&(o=""),void 0===s&&(s="&"),t=s,n=a,r=new RegExp("\\".concat(n,"\\b"),"g");var l=e.replace(kt,""),c=re(o||a?"".concat(o," ").concat(a," { ").concat(l," }"):l);i.namespace&&(c=xt(c,i.namespace));var d,f=[];return Z(c,function(e){var t=O(e);return function(n,r,a,o){for(var i="",s=0;s<t;s++)i+=e[s](n,r,a,o)||"";return i}}(u.concat((d=function(e){return f.push(e)},function(e){e.root||(e=e.return)&&d(e)})))),f};return d.hash=l.length?l.reduce(function(e,t){return t.name||et(15),Oe(e,t.name)},5381).toString():"",d}var Et=new bt,_t=St(),Ct=r.createContext({shouldForwardProp:void 0,styleSheet:Et,stylis:_t}),Ot=(Ct.Consumer,r.createContext(void 0));function Tt(){return(0,r.useContext)(Ct)}function Rt(e){var t=(0,r.useState)(e.stylisPlugins),n=t[0],a=t[1],o=Tt().styleSheet,i=(0,r.useMemo)(function(){var t=o;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t},[e.disableCSSOMInjection,e.sheet,e.target,o]),s=(0,r.useMemo)(function(){return St({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:n})},[e.enableVendorPrefixes,e.namespace,n]);(0,r.useEffect)(function(){c()(n,e.stylisPlugins)||a(e.stylisPlugins)},[e.stylisPlugins]);var l=(0,r.useMemo)(function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:i,stylis:s}},[e.shouldForwardProp,i,s]);return r.createElement(Ct.Provider,{value:l},r.createElement(Ot.Provider,{value:s},e.children))}var Pt=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=_t);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Ze(this,function(){throw et(12,String(n.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=_t),this.name+e.hash},e}(),At=function(e){return e>="A"&&e<="Z"};function jt(e){for(var t="",n=0;n<e.length;n++){var r=e[n];if(1===n&&"-"===r&&"-"===e[0])return e;At(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}var Nt=function(e){return null==e||!1===e||""===e},zt=function(e){var t,n,r=[];for(var a in e){var o=e[a];e.hasOwnProperty(a)&&!Nt(o)&&(Array.isArray(o)&&o.isCss||Ke(o)?r.push("".concat(jt(a),":"),o,";"):Xe(o)?r.push.apply(r,s(s(["".concat(a," {")],zt(o),!1),["}"],!1)):r.push("".concat(jt(a),": ").concat((t=a,null==(n=o)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in le||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return r};function Lt(e,t,n,r){return Nt(e)?[]:Qe(e)?[".".concat(e.styledComponentId)]:Ke(e)?!Ke(a=e)||a.prototype&&a.prototype.isReactComponent||!t?[e]:Lt(e(t),t,n,r):e instanceof Pt?n?(e.inject(n,r),[e.getName(r)]):[e]:Xe(e)?zt(e):Array.isArray(e)?Array.prototype.concat.apply(ge,e.map(function(e){return Lt(e,t,n,r)})):[e.toString()];var a}function Dt(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Ke(n)&&!Qe(n))return!1}return!0}var Ft=Te(fe),Mt=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&Dt(e),this.componentId=t,this.baseHash=Oe(Ft,t),this.baseStyle=n,bt.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))r=Ye(r,this.staticRulesId);else{var a=Ge(Lt(this.rules,e,t,n)),o=_e(Oe(this.baseHash,a)>>>0);if(!t.hasNameForId(this.componentId,o)){var i=n(a,".".concat(o),void 0,this.componentId);t.insertRules(this.componentId,o,i)}r=Ye(r,o),this.staticRulesId=o}else{for(var s=Oe(this.baseHash,n.hash),l="",c=0;c<this.rules.length;c++){var u=this.rules[c];if("string"==typeof u)l+=u;else if(u){var d=Ge(Lt(u,e,t,n));s=Oe(s,d+c),l+=d}}if(l){var f=_e(s>>>0);t.hasNameForId(this.componentId,f)||t.insertRules(this.componentId,f,n(l,".".concat(f),void 0,this.componentId)),r=Ye(r,f)}}return r},e}(),It=r.createContext(void 0);It.Consumer;var Ut={};new Set;function Bt(e,t,n){var a=Qe(e),o=e,s=!Ae(e),l=t.attrs,c=void 0===l?ge:l,u=t.componentId,d=void 0===u?function(e,t){var n="string"!=typeof e?"sc":xe(e);Ut[n]=(Ut[n]||0)+1;var r="".concat(n,"-").concat(Re(fe+n+Ut[n]));return t?"".concat(t,"-").concat(r):r}(t.displayName,t.parentComponentId):u,f=t.displayName,p=void 0===f?function(e){return Ae(e)?"styled.".concat(e):"Styled(".concat(Pe(e),")")}(e):f,h=t.displayName&&t.componentId?"".concat(xe(t.displayName),"-").concat(t.componentId):t.componentId||d,m=a&&o.attrs?o.attrs.concat(c).filter(Boolean):c,g=t.shouldForwardProp;if(a&&o.shouldForwardProp){var y=o.shouldForwardProp;if(t.shouldForwardProp){var v=t.shouldForwardProp;g=function(e,t){return y(e,t)&&v(e,t)}}else g=y}var b=new Mt(n,h,a?o.componentStyle:void 0);function w(e,t){return function(e,t,n){var a=e.attrs,o=e.componentStyle,s=e.defaultProps,l=e.foldedComponentIds,c=e.styledComponentId,u=e.target,d=r.useContext(It),f=Tt(),p=e.shouldForwardProp||f.shouldForwardProp,h=ve(t,d,s)||ye,m=function(e,t,n){for(var r,a=i(i({},t),{className:void 0,theme:n}),o=0;o<e.length;o+=1){var s=Ke(r=e[o])?r(a):r;for(var l in s)a[l]="className"===l?Ye(a[l],s[l]):"style"===l?i(i({},a[l]),s[l]):s[l]}return t.className&&(a.className=Ye(a.className,t.className)),a}(a,t,h),g=m.as||u,y={};for(var v in m)void 0===m[v]||"$"===v[0]||"as"===v||"theme"===v&&m.theme===h||("forwardedAs"===v?y.as=m.forwardedAs:p&&!p(v,g)||(y[v]=m[v]));var b=function(e,t){var n=Tt();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(o,m),w=Ye(l,c);return b&&(w+=" "+b),m.className&&(w+=" "+m.className),y[Ae(g)&&!be.has(g)?"class":"className"]=w,n&&(y.ref=n),(0,r.createElement)(g,y)}(k,e,t)}w.displayName=p;var k=r.forwardRef(w);return k.attrs=m,k.componentStyle=b,k.displayName=p,k.shouldForwardProp=g,k.foldedComponentIds=a?Ye(o.foldedComponentIds,o.styledComponentId):"",k.styledComponentId=h,k.target=a?o.target:e,Object.defineProperty(k,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=a?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,a=t;r<a.length;r++)Je(e,a[r],!0);return e}({},o.defaultProps,e):e}}),Ze(k,function(){return".".concat(k.styledComponentId)}),s&&Ve(k,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),k}function Ht(e,t){for(var n=[e[0]],r=0,a=t.length;r<a;r+=1)n.push(t[r],e[r+1]);return n}var qt=function(e){return Object.assign(e,{isCss:!0})};function Wt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Ke(e)||Xe(e))return qt(Lt(Ht(ge,s([e],t,!0))));var r=e;return 0===t.length&&1===r.length&&"string"==typeof r[0]?Lt(r):qt(Lt(Ht(r,t)))}function $t(e,t,n){if(void 0===n&&(n=ye),!t)throw et(1,t);var r=function(r){for(var a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];return e(t,n,Wt.apply(void 0,s([r],a,!1)))};return r.attrs=function(r){return $t(e,t,i(i({},n),{attrs:Array.prototype.concat(n.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return $t(e,t,i(i({},n),r))},r}var Vt=function(e){return $t(Bt,e)},Kt=Vt;be.forEach(function(e){Kt[e]=Vt(e)});!function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=Dt(e),bt.registerId(this.componentId+1)}e.prototype.createStyles=function(e,t,n,r){var a=r(Ge(Lt(this.rules,t,n,r)),""),o=this.componentId+e;n.insertRules(o,o,a)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,n,r){e>2&&bt.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}();(function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=ft(),r=Ge([n&&'nonce="'.concat(n,'"'),"".concat(ce,'="true"'),"".concat(de,'="').concat(fe,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(t,"</style>")},this.getStyleTags=function(){if(e.sealed)throw et(2);return e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)throw et(2);var n=e.instance.toString();if(!n)return[];var a=((t={})[ce]="",t[de]=fe,t.dangerouslySetInnerHTML={__html:n},t),o=ft();return o&&(a.nonce=o),[r.createElement("style",i({},a,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new bt({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw et(2);return r.createElement(Rt,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw et(3)}})(),"__sc-".concat(ce,"__");function Qt(e){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qt(e)}function Yt(e){var t=function(e,t){if("object"!=Qt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Qt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Qt(t)?t:t+""}function Gt(e,t,n){return(t=Yt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Jt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach(function(t){Gt(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Zt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const en=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},tn=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var nn={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const rn=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],an=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:s="",children:l,iconNode:c}=e,u=Zt(e,rn);return(0,r.createElement)("svg",Jt(Jt(Jt({ref:t},nn),{},{width:a,height:a,stroke:n,strokeWidth:i?24*Number(o)/Number(a):o,className:tn("lucide",s)},!l&&!(e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"}),u),[...c.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(l)?l:[l]])}),on=["className"],sn=(e,t)=>{const n=(0,r.forwardRef)((n,a)=>{let{className:o}=n,i=Zt(n,on);return(0,r.createElement)(an,Jt({ref:a,iconNode:t,className:tn("lucide-".concat((s=en(e),s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(e),o)},i));var s});return n.displayName=en(e),n},ln=sn("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),cn=sn("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),un=sn("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),dn=sn("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),fn=sn("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),pn=sn("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),hn=sn("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),mn=sn("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),gn=sn("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),yn=sn("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var vn,bn,wn,kn,xn,Sn,En,_n,Cn,On,Tn,Rn,Pn,An,jn,Nn,zn,Ln,Dn,Fn,Mn,In,Un,Bn,Hn,qn,Wn,$n,Vn,Kn=n(579);const Qn=Kt.div(vn||(vn=o(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),Yn=Kt.nav(bn||(bn=o(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n"]))),Gn=Kt.div(wn||(wn=o(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),Xn=Kt.div(kn||(kn=o(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),Jn=Kt.div(xn||(xn=o(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),Zn=Kt.div(Sn||(Sn=o(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n"]))),er=Kt.a(En||(En=o(["\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: var(--transition-fast);\n  position: relative;\n\n  &:hover {\n    color: var(--text-accent);\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -4px;\n    left: 0;\n    width: 0;\n    height: 2px;\n    background: var(--bg-neural);\n    transition: var(--transition-normal);\n  }\n\n  &:hover::after {\n    width: 100%;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.85rem;\n  }\n"]))),tr=Kt.section(_n||(_n=o(["\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  text-align: center;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n    min-height: calc(100vh - 80px);\n  }\n"]))),nr=Kt.div(Cn||(Cn=o(["\n  max-width: 900px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n"]))),rr=Kt.div(On||(On=o(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-6);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"]))),ar=Kt.h1(Tn||(Tn=o(["\n  font-family: var(--font-primary);\n  font-size: 3.5rem;\n  font-weight: 800;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n"]))),or=Kt.p(Rn||(Rn=o(["\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n  }\n"]))),ir=Kt.p(Pn||(Pn=o(["\n  font-size: 1rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 0.95rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n"]))),sr=Kt.div(An||(An=o(["\n  display: flex;\n  gap: var(--space-5);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n    margin-bottom: var(--space-12);\n    flex-direction: column;\n  }\n"]))),lr=Kt.button(jn||(jn=o(["\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-10);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  box-shadow: var(--shadow-neural);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: var(--transition-slow);\n  }\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n\n    &::before {\n      left: 100%;\n    }\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-8);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n"]))),cr=Kt.button(Nn||(Nn=o(["\n  background: var(--bg-glass);\n  color: var(--text-primary);\n  border: 2px solid var(--border-neural);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-6);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n"]))),ur=Kt.section(zn||(zn=o(["\n  padding: var(--space-24) var(--space-6);\n  background: var(--bg-tertiary);\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4);\n  }\n"]))),dr=Kt.div(Ln||(Ln=o(["\n  max-width: 1400px;\n  margin: 0 auto;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n"]))),fr=Kt.h2(Dn||(Dn=o(["\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n"]))),pr=Kt.p(Fn||(Fn=o(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),hr=Kt.div(Mn||(Mn=o(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n"]))),mr=Kt.div(In||(In=o(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  transition: var(--transition-normal);\n  text-align: left;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    transform: translateY(-6px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n    text-align: center;\n  }\n"]))),gr=Kt.div(Un||(Un=o(["\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    margin: 0 auto var(--space-6);\n  }\n"]))),yr=Kt.h3(Bn||(Bn=o(["\n  font-size: 1.5rem;\n  margin-bottom: var(--space-4);\n  color: var(--text-primary);\n  font-weight: 700;\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n"]))),vr=Kt.p(Hn||(Hn=o(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  line-height: 1.7;\n  font-weight: 400;\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n"]))),br=Kt.div(qn||(qn=o(["\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n"]))),wr=Kt.div(Wn||(Wn=o(["\n  text-align: center;\n"]))),kr=Kt.div($n||($n=o(["\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n"]))),xr=Kt.div(Vn||(Vn=o(["\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n"]))),Sr=e=>{let{onStartTraining:t,onNavigateToAbout:n,onNavigateToContact:r}=e;return(0,Kn.jsxs)(Qn,{children:[(0,Kn.jsx)(Yn,{children:(0,Kn.jsxs)(Gn,{children:[(0,Kn.jsxs)(Xn,{children:[(0,Kn.jsx)(Jn,{children:(0,Kn.jsx)(ln,{size:24})}),"ASL Neural"]}),(0,Kn.jsxs)(Zn,{children:[(0,Kn.jsx)(er,{href:"#features",children:"Features"}),(0,Kn.jsx)(er,{onClick:n,style:{cursor:"pointer"},children:"About"}),(0,Kn.jsx)(er,{onClick:r,style:{cursor:"pointer"},children:"Contact"})]})]})}),(0,Kn.jsx)(tr,{children:(0,Kn.jsxs)(nr,{children:[(0,Kn.jsxs)(rr,{children:[(0,Kn.jsx)(cn,{size:16}),"AI-Powered Learning Platform"]}),(0,Kn.jsx)(ar,{children:"Master Sign Language with Neural Intelligence"}),(0,Kn.jsx)(or,{children:"Revolutionary AI platform that transforms sign language learning through real-time computer vision and adaptive neural networks"}),(0,Kn.jsx)(ir,{children:"Experience the future of accessibility education. Our advanced machine learning algorithms provide instant feedback while contributing to breakthrough AI research for the deaf and hard-of-hearing community."}),(0,Kn.jsxs)(sr,{children:[(0,Kn.jsxs)(lr,{onClick:t,children:[(0,Kn.jsx)(un,{size:20}),"Start Neural Training"]}),(0,Kn.jsxs)(cr,{onClick:n,children:[(0,Kn.jsx)(dn,{size:20}),"Explore Technology"]})]}),(0,Kn.jsxs)(br,{children:[(0,Kn.jsxs)(wr,{children:[(0,Kn.jsx)(kr,{children:"25K+"}),(0,Kn.jsx)(xr,{children:"Neural Sessions"})]}),(0,Kn.jsxs)(wr,{children:[(0,Kn.jsx)(kr,{children:"150+"}),(0,Kn.jsx)(xr,{children:"Sign Patterns"})]}),(0,Kn.jsxs)(wr,{children:[(0,Kn.jsx)(kr,{children:"98.7%"}),(0,Kn.jsx)(xr,{children:"AI Accuracy"})]})]})]})}),(0,Kn.jsx)(ur,{id:"features",children:(0,Kn.jsxs)(dr,{children:[(0,Kn.jsx)(fr,{children:"Neural Network Capabilities"}),(0,Kn.jsx)(pr,{children:"Discover how our advanced AI technology revolutionizes sign language learning through cutting-edge computer vision and machine learning"}),(0,Kn.jsxs)(hr,{children:[(0,Kn.jsxs)(mr,{children:[(0,Kn.jsx)(gr,{children:(0,Kn.jsx)(fn,{size:28,color:"white"})}),(0,Kn.jsx)(yr,{children:"Real-time Computer Vision"}),(0,Kn.jsx)(vr,{children:"Advanced neural networks analyze your hand movements in real-time, providing instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms"})]}),(0,Kn.jsxs)(mr,{children:[(0,Kn.jsx)(gr,{children:(0,Kn.jsx)(pn,{size:28,color:"white"})}),(0,Kn.jsx)(yr,{children:"Adaptive AI Learning"}),(0,Kn.jsx)(vr,{children:"Our deep learning models continuously adapt to your learning style, creating personalized training paths that optimize skill acquisition and retention rates"})]}),(0,Kn.jsxs)(mr,{children:[(0,Kn.jsx)(gr,{children:(0,Kn.jsx)(hn,{size:28,color:"white"})}),(0,Kn.jsx)(yr,{children:"Global Impact Network"}),(0,Kn.jsx)(vr,{children:"Join a worldwide community contributing to breakthrough AI research that advances accessibility technology for millions in the deaf and hard-of-hearing community"})]}),(0,Kn.jsxs)(mr,{children:[(0,Kn.jsx)(gr,{children:(0,Kn.jsx)(mn,{size:28,color:"white"})}),(0,Kn.jsx)(yr,{children:"Cross-Platform Intelligence"}),(0,Kn.jsx)(vr,{children:"Seamless AI-powered experience across all devices with cloud-synchronized progress and edge computing for lightning-fast response times"})]}),(0,Kn.jsxs)(mr,{children:[(0,Kn.jsx)(gr,{children:(0,Kn.jsx)(gn,{size:28,color:"white"})}),(0,Kn.jsx)(yr,{children:"Precision Learning Analytics"}),(0,Kn.jsx)(vr,{children:"Advanced analytics track micro-movements and learning patterns, providing data-driven insights to accelerate your mastery of sign language"})]}),(0,Kn.jsxs)(mr,{children:[(0,Kn.jsx)(gr,{children:(0,Kn.jsx)(yn,{size:28,color:"white"})}),(0,Kn.jsx)(yr,{children:"Privacy-First Architecture"}),(0,Kn.jsx)(vr,{children:"Enterprise-grade security with local processing ensures your data remains private while contributing anonymized insights to advance AI research"})]})]})]})})]})};var Er=n(29),_r=n.n(Er);const Cr=sn("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),Or=sn("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),Tr=sn("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),Rr=sn("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Pr=sn("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Ar=sn("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),jr=sn("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]]),Nr=sn("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),zr=sn("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),Lr=Object.create(null);Lr.open="0",Lr.close="1",Lr.ping="2",Lr.pong="3",Lr.message="4",Lr.upgrade="5",Lr.noop="6";const Dr=Object.create(null);Object.keys(Lr).forEach(e=>{Dr[Lr[e]]=e});const Fr={type:"error",data:"parser error"},Mr="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),Ir="function"===typeof ArrayBuffer,Ur=e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,Br=(e,t,n)=>{let{type:r,data:a}=e;return Mr&&a instanceof Blob?t?n(a):Hr(a,n):Ir&&(a instanceof ArrayBuffer||Ur(a))?t?n(a):Hr(new Blob([a]),n):n(Lr[r]+(a||""))},Hr=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function qr(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Wr;const $r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Vr="undefined"===typeof Uint8Array?[]:new Uint8Array(256);for(let n=0;n<64;n++)Vr[$r.charCodeAt(n)]=n;const Kr="function"===typeof ArrayBuffer,Qr=(e,t)=>{if("string"!==typeof e)return{type:"message",data:Gr(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:Yr(e.substring(1),t)};return Dr[n]?e.length>1?{type:Dr[n],data:e.substring(1)}:{type:Dr[n]}:Fr},Yr=(e,t)=>{if(Kr){const n=(e=>{let t,n,r,a,o,i=.75*e.length,s=e.length,l=0;"="===e[e.length-1]&&(i--,"="===e[e.length-2]&&i--);const c=new ArrayBuffer(i),u=new Uint8Array(c);for(t=0;t<s;t+=4)n=Vr[e.charCodeAt(t)],r=Vr[e.charCodeAt(t+1)],a=Vr[e.charCodeAt(t+2)],o=Vr[e.charCodeAt(t+3)],u[l++]=n<<2|r>>4,u[l++]=(15&r)<<4|a>>2,u[l++]=(3&a)<<6|63&o;return c})(e);return Gr(n,t)}return{base64:!0,data:e}},Gr=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,Xr=String.fromCharCode(30);function Jr(){return new TransformStream({transform(e,t){!function(e,t){Mr&&e.data instanceof Blob?e.data.arrayBuffer().then(qr).then(t):Ir&&(e.data instanceof ArrayBuffer||Ur(e.data))?t(qr(e.data)):Br(e,!1,e=>{Wr||(Wr=new TextEncoder),t(Wr.encode(e))})}(e,n=>{const r=n.length;let a;if(r<126)a=new Uint8Array(1),new DataView(a.buffer).setUint8(0,r);else if(r<65536){a=new Uint8Array(3);const e=new DataView(a.buffer);e.setUint8(0,126),e.setUint16(1,r)}else{a=new Uint8Array(9);const e=new DataView(a.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(r))}e.data&&"string"!==typeof e.data&&(a[0]|=128),t.enqueue(a),t.enqueue(n)})}})}let Zr;function ea(e){return e.reduce((e,t)=>e+t.length,0)}function ta(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let a=0;a<t;a++)n[a]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function na(e){if(e)return function(e){for(var t in na.prototype)e[t]=na.prototype[t];return e}(e)}na.prototype.on=na.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},na.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},na.prototype.off=na.prototype.removeListener=na.prototype.removeAllListeners=na.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var a=0;a<r.length;a++)if((n=r[a])===t||n.fn===t){r.splice(a,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},na.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var a=(n=n.slice(0)).length;r<a;++r)n[r].apply(this,t)}return this},na.prototype.emitReserved=na.prototype.emit,na.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},na.prototype.hasListeners=function(e){return!!this.listeners(e).length};const ra="function"===typeof Promise&&"function"===typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),aa="undefined"!==typeof self?self:"undefined"!==typeof window?window:Function("return this")();function oa(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t),{})}const ia=aa.setTimeout,sa=aa.clearTimeout;function la(e,t){t.useNativeTimers?(e.setTimeoutFn=ia.bind(aa),e.clearTimeoutFn=sa.bind(aa)):(e.setTimeoutFn=aa.setTimeout.bind(aa),e.clearTimeoutFn=aa.clearTimeout.bind(aa))}function ca(e){return"string"===typeof e?function(e){let t=0,n=0;for(let r=0,a=e.length;r<a;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}(e):Math.ceil(1.33*(e.byteLength||e.size))}function ua(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class da extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class fa extends na{constructor(e){super(),this.writable=!1,la(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new da(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=Qr(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class pa extends fa{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(Xr),r=[];for(let a=0;a<n.length;a++){const e=Qr(n[a],t);if(r.push(e),"error"===e.type)break}return r})(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,r=new Array(n);let a=0;e.forEach((e,o)=>{Br(e,!1,e=>{r[o]=e,++a===n&&t(r.join(Xr))})})})(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=ua()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let ha=!1;try{ha="undefined"!==typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(gu){}const ma=ha;function ga(){}class ya extends pa{constructor(e){if(super(e),"undefined"!==typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!==typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class va extends na{constructor(e,t,n){super(),this.createRequest=e,la(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var e;const t=oa(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&n.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(r){}if("POST"===this._method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(r){}try{n.setRequestHeader("Accept","*/*")}catch(r){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var e;3===n.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(n.getResponseHeader("set-cookie"))),4===n.readyState&&(200===n.status||1223===n.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"===typeof n.status?n.status:0)},0))},n.send(this._data)}catch(r){return void this.setTimeoutFn(()=>{this._onError(r)},0)}"undefined"!==typeof document&&(this._index=va.requestsCount++,va.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if("undefined"!==typeof this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=ga,e)try{this._xhr.abort()}catch(t){}"undefined"!==typeof document&&delete va.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(va.requestsCount=0,va.requests={},"undefined"!==typeof document)if("function"===typeof attachEvent)attachEvent("onunload",ba);else if("function"===typeof addEventListener){addEventListener("onpagehide"in aa?"pagehide":"unload",ba,!1)}function ba(){for(let e in va.requests)va.requests.hasOwnProperty(e)&&va.requests[e].abort()}const wa=function(){const e=ka({xdomain:!1});return e&&null!==e.responseType}();function ka(e){const t=e.xdomain;try{if("undefined"!==typeof XMLHttpRequest&&(!t||ma))return new XMLHttpRequest}catch(n){}if(!t)try{return new(aa[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(n){}}const xa="undefined"!==typeof navigator&&"string"===typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class Sa extends fa{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=xa?{}:oa(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(gu){return this.emitReserved("error",gu)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;Br(n,this.supportsBinary,e=>{try{this.doWrite(n,e)}catch(t){}r&&ra(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){"undefined"!==typeof this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=ua()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const Ea=aa.WebSocket||aa.MozWebSocket;const _a={websocket:class extends Sa{createSocket(e,t,n){return xa?new Ea(e,t,n):t?new Ea(e,t):new Ea(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends fa{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(gu){return this.emitReserved("error",gu)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=function(e,t){Zr||(Zr=new TextDecoder);const n=[];let r=0,a=-1,o=!1;return new TransformStream({transform(i,s){for(n.push(i);;){if(0===r){if(ea(n)<1)break;const e=ta(n,1);o=128===(128&e[0]),a=127&e[0],r=a<126?3:126===a?1:2}else if(1===r){if(ea(n)<2)break;const e=ta(n,2);a=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(ea(n)<8)break;const e=ta(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),o=t.getUint32(0);if(o>Math.pow(2,21)-1){s.enqueue(Fr);break}a=o*Math.pow(2,32)+t.getUint32(4),r=3}else{if(ea(n)<a)break;const e=ta(n,a);s.enqueue(Qr(o?e:Zr.decode(e),t)),r=0}if(0===a||a>e){s.enqueue(Fr);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),r=Jr();r.readable.pipeTo(e.writable),this._writer=r.writable.getWriter();const a=()=>{n.read().then(e=>{let{done:t,value:n}=e;t||(this.onPacket(n),a())}).catch(e=>{})};a();const o={type:"open"};this.query.sid&&(o.data='{"sid":"'.concat(this.query.sid,'"}')),this._writer.write(o).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;this._writer.write(n).then(()=>{r&&ra(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends ya{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=wa&&!t}request(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(e,{xd:this.xd},this.opts),new va(ka,this.uri(),e)}}},Ca=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Oa=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Ta(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");-1!=n&&-1!=r&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let a=Ca.exec(e||""),o={},i=14;for(;i--;)o[Oa[i]]=a[i]||"";return-1!=n&&-1!=r&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=function(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||r.splice(0,1);"/"==t.slice(-1)&&r.splice(r.length-1,1);return r}(0,o.path),o.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(n[t]=r)}),n}(0,o.query),o}const Ra="function"===typeof addEventListener&&"function"===typeof removeEventListener,Pa=[];Ra&&addEventListener("offline",()=>{Pa.forEach(e=>e())},!1);class Aa extends na{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"===typeof e&&(t=e,e=null),e){const n=Ta(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=Ta(t.host).host);la(this,t),this.secure=null!=t.secure?t.secure:"undefined"!==typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!==typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!==typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"===typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let r=0,a=n.length;r<a;r++){let e=n[r].split("=");t[decodeURIComponent(e[0])]=decodeURIComponent(e[1])}return t}(this.opts.query)),Ra&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Pa.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);const e=this.opts.rememberUpgrade&&Aa.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",Aa.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){const n=this.writeBuffer[t].data;if(n&&(e+=ca(n)),t>0&&e>this._maxPayload)return this.writeBuffer.slice(0,t);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,ra(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,r){if("function"===typeof t&&(r=t,t=void 0),"function"===typeof n&&(r=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const a={type:e,data:t,options:n};this.emitReserved("packetCreate",a),this.writeBuffer.push(a),r&&this.once("flush",r),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():e()}):this.upgrading?n():e()),this}_onError(e){if(Aa.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ra&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=Pa.indexOf(this._offlineEventListener);-1!==e&&Pa.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}Aa.protocol=4;class ja extends Aa{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;Aa.priorWebsocketSuccess=!1;const r=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;Aa.priorWebsocketSuccess="websocket"===t.name,this.transport.pause(()=>{n||"closed"!==this.readyState&&(c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}}))};function a(){n||(n=!0,c(),t.close(),t=null)}const o=e=>{const n=new Error("probe error: "+e);n.transport=t.name,a(),this.emitReserved("upgradeError",n)};function i(){o("transport closed")}function s(){o("socket closed")}function l(e){t&&e.name!==t.name&&a()}const c=()=>{t.removeListener("open",r),t.removeListener("error",o),t.removeListener("close",i),this.off("close",s),this.off("upgrading",l)};t.once("open",r),t.once("error",o),t.once("close",i),this.once("close",s),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{n||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}class Na extends ja{constructor(e){const t="object"===typeof e?e:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(!t.transports||t.transports&&"string"===typeof t.transports[0])&&(t.transports=(t.transports||["polling","websocket","webtransport"]).map(e=>_a[e]).filter(e=>!!e)),super(e,t)}}const za="function"===typeof ArrayBuffer,La=Object.prototype.toString,Da="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===La.call(Blob),Fa="function"===typeof File||"undefined"!==typeof File&&"[object FileConstructor]"===La.call(File);function Ma(e){return za&&(e instanceof ArrayBuffer||(e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||Da&&e instanceof Blob||Fa&&e instanceof File}function Ia(e,t){if(!e||"object"!==typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(Ia(e[t]))return!0;return!1}if(Ma(e))return!0;if(e.toJSON&&"function"===typeof e.toJSON&&1===arguments.length)return Ia(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&Ia(e[n]))return!0;return!1}function Ua(e){const t=[],n=e.data,r=e;return r.data=Ba(n,t),r.attachments=t.length,{packet:r,buffers:t}}function Ba(e,t){if(!e)return e;if(Ma(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=Ba(e[r],t);return n}if("object"===typeof e&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=Ba(e[r],t));return n}return e}function Ha(e,t){return e.data=qa(e.data,t),delete e.attachments,e}function qa(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"===typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=qa(e[n],t);else if("object"===typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=qa(e[n],t));return e}const Wa=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],$a=5;var Va;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(Va||(Va={}));class Ka{constructor(e){this.replacer=e}encode(e){return e.type!==Va.EVENT&&e.type!==Va.ACK||!Ia(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===Va.EVENT?Va.BINARY_EVENT:Va.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==Va.BINARY_EVENT&&e.type!==Va.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=Ua(e),n=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(n),r}}function Qa(e){return"[object Object]"===Object.prototype.toString.call(e)}class Ya extends na{constructor(e){super(),this.reviver=e}add(e){let t;if("string"===typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===Va.BINARY_EVENT;n||t.type===Va.BINARY_ACK?(t.type=n?Va.EVENT:Va.ACK,this.reconstructor=new Ga(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!Ma(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===Va[n.type])throw new Error("unknown packet type "+n.type);if(n.type===Va.BINARY_EVENT||n.type===Va.BINARY_ACK){const r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const a=e.substring(r,t);if(a!=Number(a)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(a)}if("/"===e.charAt(t+1)){const r=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(r,t)}else n.nsp="/";const r=e.charAt(t+1);if(""!==r&&Number(r)==r){const r=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(!Ya.isPayloadValid(n.type,r))throw new Error("invalid payload");n.data=r}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(t){return!1}}static isPayloadValid(e,t){switch(e){case Va.CONNECT:return Qa(t);case Va.DISCONNECT:return void 0===t;case Va.CONNECT_ERROR:return"string"===typeof t||Qa(t);case Va.EVENT:case Va.BINARY_EVENT:return Array.isArray(t)&&("number"===typeof t[0]||"string"===typeof t[0]&&-1===Wa.indexOf(t[0]));case Va.ACK:case Va.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Ga{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=Ha(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function Xa(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const Ja=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Za extends na{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[Xa(e,"open",this.onopen.bind(this)),Xa(e,"packet",this.onpacket.bind(this)),Xa(e,"error",this.onerror.bind(this)),Xa(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.unshift("message"),this.emit.apply(this,t),this}emit(e){var t,n,r;if(Ja.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');for(var a=arguments.length,o=new Array(a>1?a-1:0),i=1;i<a;i++)o[i-1]=arguments[i];if(o.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(o),this;const s={type:Va.EVENT,data:o,options:{}};if(s.options.compress=!1!==this.flags.compress,"function"===typeof o[o.length-1]){const e=this.ids++,t=o.pop();this._registerAckCallback(e,t),s.id=e}const l=null===(n=null===(t=this.io.engine)||void 0===t?void 0:t.transport)||void 0===n?void 0:n.writable,c=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r._hasPingExpired());return this.flags.volatile&&!l||(c?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(e,t){var n,r=this;const a=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===a)return void(this.acks[e]=t);const o=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))},a),i=function(){r.io.clearTimeoutFn(o);for(var e=arguments.length,n=new Array(e),a=0;a<e;a++)n[a]=arguments[a];t.apply(r,n)};i.withError=!0,this.acks[e]=i}emitWithAck(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return new Promise((t,r)=>{const a=(e,n)=>e?r(e):t(n);a.withError=!0,n.push(a),this.emit(e,...n)})}_addToQueue(e){var t=this;let n;"function"===typeof e[e.length-1]&&(n=e.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push(function(e){if(r!==t._queue[0])return;if(null!==e)r.tryCount>t._opts.retries&&(t._queue.shift(),n&&n(e));else if(t._queue.shift(),n){for(var a=arguments.length,o=new Array(a>1?a-1:0),i=1;i<a;i++)o[i-1]=arguments[i];n(null,...o)}return r.pending=!1,t._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:Va.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case Va.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case Va.EVENT:case Va.BINARY_EVENT:this.onevent(e);break;case Va.ACK:case Va.BINARY_ACK:this.onack(e);break;case Va.DISCONNECT:this.ondisconnect();break;case Va.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"===typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(){if(!n){n=!0;for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];t.packet({type:Va.ACK,id:e,data:a})}}}onack(e){const t=this.acks[e.id];"function"===typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:Va.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function eo(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}eo.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this.max)},eo.prototype.reset=function(){this.attempts=0},eo.prototype.setMin=function(e){this.ms=e},eo.prototype.setMax=function(e){this.max=e},eo.prototype.setJitter=function(e){this.jitter=e};class to extends na{constructor(t,n){var r;super(),this.nsps={},this.subs=[],t&&"object"===typeof t&&(n=t,t=void 0),(n=n||{}).path=n.path||"/socket.io",this.opts=n,la(this,n),this.reconnection(!1!==n.reconnection),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=n.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new eo({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==n.timeout?2e4:n.timeout),this._readyState="closed",this.uri=t;const a=n.parser||e;this.encoder=new a.Encoder,this.decoder=new a.Decoder,this._autoConnect=!1!==n.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Na(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const r=Xa(t,"open",function(){n.onopen(),e&&e()}),a=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},o=Xa(t,"error",a);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn(()=>{r(),a(new Error("timeout")),t.close()},e);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}return this.subs.push(r),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(Xa(e,"ping",this.onping.bind(this)),Xa(e,"data",this.ondata.bind(this)),Xa(e,"error",this.onerror.bind(this)),Xa(e,"close",this.onclose.bind(this)),Xa(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(t){this.onclose("parse error",t)}}ondecoded(e){ra(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new Za(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t){if(this.nsps[n].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()}))},t);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const no={};function ro(e,t){"object"===typeof e&&(t=e,e=void 0);const n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=e;n=n||"undefined"!==typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"===typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e="undefined"!==typeof n?n.protocol+"//"+e:"https://"+e),r=Ta(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const a=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+a+":"+r.port+t,r.href=r.protocol+"://"+a+(n&&n.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),r=n.source,a=n.id,o=n.path,i=no[a]&&o in no[a].nsps;let s;return t.forceNew||t["force new connection"]||!1===t.multiplex||i?s=new to(r,t):(no[a]||(no[a]=new to(r,t)),s=no[a]),n.query&&!t.query&&(t.query=n.queryKey),s.socket(n.path,t)}function ao(e,t){return function(){return e.apply(t,arguments)}}Object.assign(ro,{Manager:to,Socket:Za,io:ro,connect:ro});const{toString:oo}=Object.prototype,{getPrototypeOf:io}=Object,{iterator:so,toStringTag:lo}=Symbol,co=(e=>t=>{const n=oo.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),uo=e=>(e=e.toLowerCase(),t=>co(t)===e),fo=e=>t=>typeof t===e,{isArray:po}=Array,ho=fo("undefined");const mo=uo("ArrayBuffer");const go=fo("string"),yo=fo("function"),vo=fo("number"),bo=e=>null!==e&&"object"===typeof e,wo=e=>{if("object"!==co(e))return!1;const t=io(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(lo in e)&&!(so in e)},ko=uo("Date"),xo=uo("File"),So=uo("Blob"),Eo=uo("FileList"),_o=uo("URLSearchParams"),[Co,Oo,To,Ro]=["ReadableStream","Request","Response","Headers"].map(uo);function Po(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),po(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let i;for(n=0;n<o;n++)i=r[n],t.call(null,e[i],i,e)}}function Ao(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const jo="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,No=e=>!ho(e)&&e!==jo;const zo=(Lo="undefined"!==typeof Uint8Array&&io(Uint8Array),e=>Lo&&e instanceof Lo);var Lo;const Do=uo("HTMLFormElement"),Fo=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Mo=uo("RegExp"),Io=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Po(n,(n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)}),Object.defineProperties(e,r)};const Uo=uo("AsyncFunction"),Bo=(Ho="function"===typeof setImmediate,qo=yo(jo.postMessage),Ho?setImmediate:qo?((e,t)=>(jo.addEventListener("message",n=>{let{source:r,data:a}=n;r===jo&&a===e&&t.length&&t.shift()()},!1),n=>{t.push(n),jo.postMessage(e,"*")}))("axios@".concat(Math.random()),[]):e=>setTimeout(e));var Ho,qo;const Wo="undefined"!==typeof queueMicrotask?queueMicrotask.bind(jo):"undefined"!==typeof process&&process.nextTick||Bo,$o={isArray:po,isArrayBuffer:mo,isBuffer:function(e){return null!==e&&!ho(e)&&null!==e.constructor&&!ho(e.constructor)&&yo(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||yo(e.append)&&("formdata"===(t=co(e))||"object"===t&&yo(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&mo(e.buffer),t},isString:go,isNumber:vo,isBoolean:e=>!0===e||!1===e,isObject:bo,isPlainObject:wo,isReadableStream:Co,isRequest:Oo,isResponse:To,isHeaders:Ro,isUndefined:ho,isDate:ko,isFile:xo,isBlob:So,isRegExp:Mo,isFunction:yo,isStream:e=>bo(e)&&yo(e.pipe),isURLSearchParams:_o,isTypedArray:zo,isFileList:Eo,forEach:Po,merge:function e(){const{caseless:t}=No(this)&&this||{},n={},r=(r,a)=>{const o=t&&Ao(n,a)||a;wo(n[o])&&wo(r)?n[o]=e(n[o],r):wo(r)?n[o]=e({},r):po(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&Po(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Po(t,(t,r)=>{n&&yo(t)?e[r]=ao(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,i;const s={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],r&&!r(i,e,t)||s[i]||(t[i]=e[i],s[i]=!0);e=!1!==n&&io(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:co,kindOfTest:uo,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(po(e))return e;let t=e.length;if(!vo(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[so]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Do,hasOwnProperty:Fo,hasOwnProp:Fo,reduceDescriptors:Io,freezeMethods:e=>{Io(e,(t,n)=>{if(yo(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];yo(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return po(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Ao,global:jo,isContextDefined:No,isSpecCompliantForm:function(e){return!!(e&&yo(e.append)&&"FormData"===e[lo]&&e[so])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(bo(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=po(e)?[]:{};return Po(e,(e,t)=>{const o=n(e,r+1);!ho(o)&&(a[t]=o)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:Uo,isThenable:e=>e&&(bo(e)||yo(e))&&yo(e.then)&&yo(e.catch),setImmediate:Bo,asap:Wo,isIterable:e=>null!=e&&yo(e[so])};function Vo(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}$o.inherits(Vo,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:$o.toJSONObject(this.config),code:this.code,status:this.status}}});const Ko=Vo.prototype,Qo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Qo[e]={value:e}}),Object.defineProperties(Vo,Qo),Object.defineProperty(Ko,"isAxiosError",{value:!0}),Vo.from=(e,t,n,r,a,o)=>{const i=Object.create(Ko);return $o.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Vo.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Yo=Vo;function Go(e){return $o.isPlainObject(e)||$o.isArray(e)}function Xo(e){return $o.endsWith(e,"[]")?e.slice(0,-2):e}function Jo(e,t,n){return e?e.concat(t).map(function(e,t){return e=Xo(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Zo=$o.toFlatObject($o,{},null,function(e){return/^is[A-Z]/.test(e)});const ei=function(e,t,n){if(!$o.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=$o.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!$o.isUndefined(t[e])})).metaTokens,a=n.visitor||c,o=n.dots,i=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&$o.isSpecCompliantForm(t);if(!$o.isFunction(a))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if($o.isDate(e))return e.toISOString();if($o.isBoolean(e))return e.toString();if(!s&&$o.isBlob(e))throw new Yo("Blob is not supported. Use a Buffer instead.");return $o.isArrayBuffer(e)||$o.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let s=e;if(e&&!a&&"object"===typeof e)if($o.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if($o.isArray(e)&&function(e){return $o.isArray(e)&&!e.some(Go)}(e)||($o.isFileList(e)||$o.endsWith(n,"[]"))&&(s=$o.toArray(e)))return n=Xo(n),s.forEach(function(e,r){!$o.isUndefined(e)&&null!==e&&t.append(!0===i?Jo([n],r,o):null===i?n:n+"[]",l(e))}),!1;return!!Go(e)||(t.append(Jo(a,n,o),l(e)),!1)}const u=[],d=Object.assign(Zo,{defaultVisitor:c,convertValue:l,isVisitable:Go});if(!$o.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!$o.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),$o.forEach(n,function(n,o){!0===(!($o.isUndefined(n)||null===n)&&a.call(t,n,$o.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])}),u.pop()}}(e),t};function ti(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ni(e,t){this._pairs=[],e&&ei(e,this,t)}const ri=ni.prototype;ri.append=function(e,t){this._pairs.push([e,t])},ri.toString=function(e){const t=e?function(t){return e.call(this,t,ti)}:ti;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const ai=ni;function oi(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ii(e,t,n){if(!t)return e;const r=n&&n.encode||oi;$o.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):$o.isURLSearchParams(t)?t.toString():new ai(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const si=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){$o.forEach(this.handlers,function(t){null!==t&&e(t)})}},li={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ci={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:ai,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ui="undefined"!==typeof window&&"undefined"!==typeof document,di="object"===typeof navigator&&navigator||void 0,fi=ui&&(!di||["ReactNative","NativeScript","NS"].indexOf(di.product)<0),pi="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,hi=ui&&window.location.href||"http://localhost",mi=Jt(Jt({},t),ci);const gi=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const i=Number.isFinite(+o),s=a>=e.length;if(o=!o&&$o.isArray(r)?r.length:o,s)return $o.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!i;r[o]&&$o.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&$o.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!i}if($o.isFormData(e)&&$o.isFunction(e.entries)){const n={};return $o.forEachEntry(e,(e,r)=>{t(function(e){return $o.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const yi={transitional:li,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=$o.isObject(e);a&&$o.isHTMLForm(e)&&(e=new FormData(e));if($o.isFormData(e))return r?JSON.stringify(gi(e)):e;if($o.isArrayBuffer(e)||$o.isBuffer(e)||$o.isStream(e)||$o.isFile(e)||$o.isBlob(e)||$o.isReadableStream(e))return e;if($o.isArrayBufferView(e))return e.buffer;if($o.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ei(e,new mi.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return mi.isNode&&$o.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=$o.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ei(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if($o.isString(e))try{return(t||JSON.parse)(e),$o.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||yi.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if($o.isResponse(e)||$o.isReadableStream(e))return e;if(e&&$o.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(a){if(n){if("SyntaxError"===a.name)throw Yo.from(a,Yo.ERR_BAD_RESPONSE,this,null,this.response);throw a}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:mi.classes.FormData,Blob:mi.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};$o.forEach(["delete","get","head","post","put","patch"],e=>{yi.headers[e]={}});const vi=yi,bi=$o.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),wi=Symbol("internals");function ki(e){return e&&String(e).trim().toLowerCase()}function xi(e){return!1===e||null==e?e:$o.isArray(e)?e.map(xi):String(e)}function Si(e,t,n,r,a){return $o.isFunction(r)?r.call(this,t,n):(a&&(t=n),$o.isString(t)?$o.isString(r)?-1!==t.indexOf(r):$o.isRegExp(r)?r.test(t):void 0:void 0)}class Ei{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=ki(t);if(!a)throw new Error("header name must be a non-empty string");const o=$o.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=xi(e))}const o=(e,t)=>$o.forEach(e,(e,n)=>a(e,n,t));if($o.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if($o.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&bi[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if($o.isObject(e)&&$o.isIterable(e)){let n,r,a={};for(const t of e){if(!$o.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?$o.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=ki(e)){const n=$o.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if($o.isFunction(t))return t.call(this,e,n);if($o.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ki(e)){const n=$o.findKey(this,e);return!(!n||void 0===this[n]||t&&!Si(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=ki(e)){const a=$o.findKey(n,e);!a||t&&!Si(0,n[a],a,t)||(delete n[a],r=!0)}}return $o.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Si(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return $o.forEach(this,(r,a)=>{const o=$o.findKey(n,a);if(o)return t[o]=xi(r),void delete t[a];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();i!==a&&delete t[a],t[i]=xi(r),n[i]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return $o.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&$o.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[wi]=this[wi]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=ki(e);t[r]||(!function(e,t){const n=$o.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return $o.isArray(e)?e.forEach(r):r(e),this}}Ei.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),$o.reduceDescriptors(Ei.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),$o.freezeMethods(Ei);const _i=Ei;function Ci(e,t){const n=this||vi,r=t||n,a=_i.from(r.headers);let o=r.data;return $o.forEach(e,function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function Oi(e){return!(!e||!e.__CANCEL__)}function Ti(e,t,n){Yo.call(this,null==e?"canceled":e,Yo.ERR_CANCELED,t,n),this.name="CanceledError"}$o.inherits(Ti,Yo,{__CANCEL__:!0});const Ri=Ti;function Pi(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Yo("Request failed with status code "+n.status,[Yo.ERR_BAD_REQUEST,Yo.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Ai=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,i=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),c=r[i];a||(a=l),n[o]=s,r[o]=l;let u=i,d=0;for(;u!==o;)d+=n[u++],u%=e;if(o=(o+1)%e,o===i&&(i=(i+1)%e),l-a<t)return;const f=c&&l-c;return f?Math.round(1e3*d/f):void 0}};const ji=function(e,t){let n,r,a=0,o=1e3/t;const i=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var s=arguments.length,l=new Array(s),c=0;c<s;c++)l[c]=arguments[c];t>=o?i(l,e):(n=l,r||(r=setTimeout(()=>{r=null,i(n)},o-t)))},()=>n&&i(n)]},Ni=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=Ai(50,250);return ji(n=>{const o=n.loaded,i=n.lengthComputable?n.total:void 0,s=o-r,l=a(s);r=o;e({loaded:o,total:i,progress:i?o/i:void 0,bytes:s,rate:l||void 0,estimated:l&&i&&o<=i?(i-o)/l:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},zi=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Li=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return $o.asap(()=>e(...n))},Di=mi.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,mi.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(mi.origin),mi.navigator&&/(msie|trident)/i.test(mi.navigator.userAgent)):()=>!0,Fi=mi.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];$o.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),$o.isString(r)&&i.push("path="+r),$o.isString(a)&&i.push("domain="+a),!0===o&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Mi(e,t,n){let r=!function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ii=e=>e instanceof _i?Jt({},e):e;function Ui(e,t){t=t||{};const n={};function r(e,t,n,r){return $o.isPlainObject(e)&&$o.isPlainObject(t)?$o.merge.call({caseless:r},e,t):$o.isPlainObject(t)?$o.merge({},t):$o.isArray(t)?t.slice():t}function a(e,t,n,a){return $o.isUndefined(t)?$o.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!$o.isUndefined(t))return r(void 0,t)}function i(e,t){return $o.isUndefined(t)?$o.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s,headers:(e,t,n)=>a(Ii(e),Ii(t),0,!0)};return $o.forEach(Object.keys(Object.assign({},e,t)),function(r){const o=l[r]||a,i=o(e[r],t[r],r);$o.isUndefined(i)&&o!==s||(n[r]=i)}),n}const Bi=e=>{const t=Ui({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:i,headers:s,auth:l}=t;if(t.headers=s=_i.from(s),t.url=ii(Mi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),$o.isFormData(r))if(mi.hasStandardBrowserEnv||mi.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}if(mi.hasStandardBrowserEnv&&(a&&$o.isFunction(a)&&(a=a(t)),a||!1!==a&&Di(t.url))){const e=o&&i&&Fi.read(i);e&&s.set(o,e)}return t},Hi="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Bi(e);let a=r.data;const o=_i.from(r.headers).normalize();let i,s,l,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=_i.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Pi(function(e){t(e),h()},function(e){n(e),h()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Yo("Request aborted",Yo.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Yo("Network Error",Yo.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||li;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Yo(t,a.clarifyTimeoutError?Yo.ETIMEDOUT:Yo.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&$o.forEach(o.toJSON(),function(e,t){m.setRequestHeader(t,e)}),$o.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([l,u]=Ni(p,!0),m.addEventListener("progress",l)),f&&m.upload&&([s,c]=Ni(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new Ri(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===mi.protocols.indexOf(y)?n(new Yo("Unsupported protocol "+y+":",Yo.ERR_BAD_REQUEST,e)):m.send(a||null)})},qi=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Yo?t:new Ri(t instanceof Error?t.message:t))}};let o=t&&setTimeout(()=>{o=null,a(new Yo("timeout ".concat(t," of ms exceeded"),Yo.ETIMEDOUT))},t);const i=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:s}=r;return s.unsubscribe=()=>$o.asap(i),s}};function Wi(e,t){this.v=e,this.k=t}function $i(e){return function(){return new Vi(e.apply(this,arguments))}}function Vi(e){var t,n;function r(t,n){try{var o=e[t](n),i=o.value,s=i instanceof Wi;Promise.resolve(s?i.v:i).then(function(n){if(s){var l="return"===t?"return":"next";if(!i.k||n.done)return r(l,n);n=e[l](n).value}a(o.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(o,i){var s={key:e,arg:a,resolve:o,reject:i,next:null};n?n=n.next=s:(t=n=s,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function Ki(e){return new Wi(e,0)}function Qi(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new Wi(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Yi(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Gi(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Gi(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return Gi=function(e){this.s=e,this.n=e.next},Gi.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Gi(e)}Vi.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Vi.prototype.next=function(e){return this._invoke("next",e)},Vi.prototype.throw=function(e){return this._invoke("throw",e)},Vi.prototype.return=function(e){return this._invoke("return",e)};const Xi=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Ji=function(){var e=$i(function*(e,t){var n,r=!1,a=!1;try{for(var o,i=Yi(Zi(e));r=!(o=yield Ki(i.next())).done;r=!1){const e=o.value;yield*Qi(Yi(Xi(e,t)))}}catch(gu){a=!0,n=gu}finally{try{r&&null!=i.return&&(yield Ki(i.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),Zi=function(){var e=$i(function*(e){if(e[Symbol.asyncIterator])return void(yield*Qi(Yi(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Ki(t.read());if(e)break;yield n}}finally{yield Ki(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),es=(e,t,n,r)=>{const a=Ji(e,t);let o,i=0,s=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return s(),void e.close();let o=r.byteLength;if(n){let e=i+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(gu){throw s(gu),gu}},cancel:e=>(s(e),a.return())},{highWaterMark:2})},ts="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,ns=ts&&"function"===typeof ReadableStream,rs=ts&&("function"===typeof TextEncoder?(as=new TextEncoder,e=>as.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var as;const os=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(a){return!1}},is=ns&&os(()=>{let e=!1;const t=new Request(mi.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ss=ns&&os(()=>$o.isReadableStream(new Response("").body)),ls={stream:ss&&(e=>e.body)};var cs;ts&&(cs=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!ls[e]&&(ls[e]=$o.isFunction(cs[e])?t=>t[e]():(t,n)=>{throw new Yo("Response type '".concat(e,"' is not supported"),Yo.ERR_NOT_SUPPORT,n)})}));const us=async(e,t)=>{const n=$o.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if($o.isBlob(e))return e.size;if($o.isSpecCompliantForm(e)){const t=new Request(mi.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return $o.isArrayBufferView(e)||$o.isArrayBuffer(e)?e.byteLength:($o.isURLSearchParams(e)&&(e+=""),$o.isString(e)?(await rs(e)).byteLength:void 0)})(t):n},ds=ts&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:s,onUploadProgress:l,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:f}=Bi(e);c=c?(c+"").toLowerCase():"text";let p,h=qi([a,o&&o.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(l&&is&&"get"!==n&&"head"!==n&&0!==(g=await us(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if($o.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=zi(g,Ni(Li(l)));r=es(n.body,65536,e,t)}}$o.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,Jt(Jt({},f),{},{signal:h,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let o=await fetch(p,f);const i=ss&&("stream"===c||"response"===c);if(ss&&(s||i&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=o[t]});const t=$o.toFiniteNumber(o.headers.get("content-length")),[n,r]=s&&zi(t,Ni(Li(s),!0))||[];o=new Response(es(o.body,65536,n,()=>{r&&r(),m&&m()}),e)}c=c||"text";let y=await ls[$o.findKey(ls,c)||"text"](o,e);return!i&&m&&m(),await new Promise((t,n)=>{Pi(t,n,{data:y,headers:_i.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})})}catch(gu){if(m&&m(),gu&&"TypeError"===gu.name&&/Load failed|fetch/i.test(gu.message))throw Object.assign(new Yo("Network Error",Yo.ERR_NETWORK,e,p),{cause:gu.cause||gu});throw Yo.from(gu,gu&&gu.code,e,p)}}),fs={http:null,xhr:Hi,fetch:ds};$o.forEach(fs,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const ps=e=>"- ".concat(e),hs=e=>$o.isFunction(e)||null===e||!1===e,ms=e=>{e=$o.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!hs(n)&&(r=fs[(t=String(n)).toLowerCase()],void 0===r))throw new Yo("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(ps).join("\n"):" "+ps(e[0]):"as no adapter specified";throw new Yo("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function gs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ri(null,e)}function ys(e){gs(e),e.headers=_i.from(e.headers),e.data=Ci.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return ms(e.adapter||vi.adapter)(e).then(function(t){return gs(e),t.data=Ci.call(e,e.transformResponse,t),t.headers=_i.from(t.headers),t},function(t){return Oi(t)||(gs(e),t&&t.response&&(t.response.data=Ci.call(e,e.transformResponse,t.response),t.response.headers=_i.from(t.response.headers))),Promise.reject(t)})}const vs="1.10.0",bs={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{bs[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const ws={};bs.transitional=function(e,t,n){function r(e,t){return"[Axios v"+vs+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new Yo(r(a," has been removed"+(t?" in "+t:"")),Yo.ERR_DEPRECATED);return t&&!ws[a]&&(ws[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},bs.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const ks={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Yo("options must be an object",Yo.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const t=e[o],n=void 0===t||i(t,o,e);if(!0!==n)throw new Yo("option "+o+" must be "+n,Yo.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new Yo("Unknown option "+o,Yo.ERR_BAD_OPTION)}},validators:bs},xs=ks.validators;class Ss{constructor(e){this.defaults=e||{},this.interceptors={request:new si,response:new si}}async request(e,t){try{return await this._request(e,t)}catch(gu){if(gu instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const r=t.stack?t.stack.replace(/^.+\n/,""):"";try{gu.stack?r&&!String(gu.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(gu.stack+="\n"+r):gu.stack=r}catch(n){}}throw gu}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Ui(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&ks.assertOptions(n,{silentJSONParsing:xs.transitional(xs.boolean),forcedJSONParsing:xs.transitional(xs.boolean),clarifyTimeoutError:xs.transitional(xs.boolean)},!1),null!=r&&($o.isFunction(r)?t.paramsSerializer={serialize:r}:ks.assertOptions(r,{encode:xs.function,serialize:xs.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),ks.assertOptions(t,{baseUrl:xs.spelling("baseURL"),withXsrfToken:xs.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&$o.merge(a.common,a[t.method]);a&&$o.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=_i.concat(o,a);const i=[];let s=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const l=[];let c;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u,d=0;if(!s){const e=[ys.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,l),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=i.length;let f=t;for(d=0;d<u;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{c=ys.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,u=l.length;d<u;)c=c.then(l[d++],l[d++]);return c}getUri(e){return ii(Mi((e=Ui(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}$o.forEach(["delete","get","head","options"],function(e){Ss.prototype[e]=function(t,n){return this.request(Ui(n||{},{method:e,url:t,data:(n||{}).data}))}}),$o.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(Ui(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Ss.prototype[e]=t(),Ss.prototype[e+"Form"]=t(!0)});const Es=Ss;class _s{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new Ri(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new _s(function(t){e=t}),cancel:e}}}const Cs=_s;const Os={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Os).forEach(e=>{let[t,n]=e;Os[n]=t});const Ts=Os;const Rs=function e(t){const n=new Es(t),r=ao(Es.prototype.request,n);return $o.extend(r,Es.prototype,n,{allOwnKeys:!0}),$o.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Ui(t,n))},r}(vi);Rs.Axios=Es,Rs.CanceledError=Ri,Rs.CancelToken=Cs,Rs.isCancel=Oi,Rs.VERSION=vs,Rs.toFormData=ei,Rs.AxiosError=Yo,Rs.Cancel=Rs.CanceledError,Rs.all=function(e){return Promise.all(e)},Rs.spread=function(e){return function(t){return e.apply(null,t)}},Rs.isAxiosError=function(e){return $o.isObject(e)&&!0===e.isAxiosError},Rs.mergeConfig=Ui,Rs.AxiosHeaders=_i,Rs.formToJSON=e=>gi($o.isHTMLForm(e)?new FormData(e):e),Rs.getAdapter=ms,Rs.HttpStatusCode=Ts,Rs.default=Rs;const Ps=Rs,As={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:5000";const js=new class{constructor(){this.socket=null,this.isConnected=!1,this.recognitionActive=!1,this.callbacks={onRecognitionResult:null,onError:null,onConnectionChange:null,onRecognitionStarted:null,onRecognitionStopped:null}}connect(){try{return this.socket=ro(As,{transports:["websocket","polling"],timeout:2e4,reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3}),this.setupSocketListeners(),new Promise((e,t)=>{this.socket.on("connect",()=>{console.log("\ud83d\udd0c Connected to ASL Recognition Backend"),this.isConnected=!0,this.notifyConnectionChange(),e()}),this.socket.on("connect_error",e=>{console.error("\u274c Connection error:",e),this.isConnected=!1,this.notifyConnectionChange(),t(e)})})}catch(e){throw console.error("\u274c Failed to connect:",e),e}}setupSocketListeners(){this.socket&&(this.socket.on("recognitionStarted",e=>{console.log("\ud83c\udfac Recognition started:",e),this.recognitionActive=!0,this.callbacks.onRecognitionStarted&&this.callbacks.onRecognitionStarted(e)}),this.socket.on("recognitionResult",e=>{console.log("\ud83c\udfaf Recognition result:",e),this.callbacks.onRecognitionResult&&this.callbacks.onRecognitionResult(e)}),this.socket.on("error",e=>{console.error("\u274c Recognition error:",e),this.callbacks.onError&&this.callbacks.onError(e)}),this.socket.on("disconnect",()=>{console.log("\ud83d\udd0c Disconnected from backend"),this.isConnected=!1,this.recognitionActive=!1,this.notifyConnectionChange()}))}async startRecognition(){if(!this.socket||!this.isConnected)throw new Error("Not connected to backend");try{return this.socket.emit("startRecognition"),!0}catch(e){throw console.error("\u274c Failed to start recognition:",e),e}}async processFrame(e){if(!this.socket||!this.isConnected)throw new Error("Not connected to backend");try{this.socket.emit("processFrame",e)}catch(t){throw console.error("\u274c Failed to process frame:",t),t}}stopRecognition(){if(this.socket&&this.isConnected)try{this.socket.emit("stopRecognition"),this.recognitionActive=!1,this.callbacks.onRecognitionStopped&&this.callbacks.onRecognitionStopped()}catch(e){console.error("\u274c Failed to stop recognition:",e)}}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.recognitionActive=!1,this.notifyConnectionChange())}async getAvailableSigns(){try{return(await Ps.get("".concat(As,"/api/signs"))).data.signs}catch(e){throw console.error("\u274c Failed to fetch signs:",e),e}}async getSignInfo(e){try{return(await Ps.get("".concat(As,"/api/sign/").concat(encodeURIComponent(e)))).data}catch(t){throw console.error("\u274c Failed to fetch sign info:",t),t}}async checkHealth(){try{return(await Ps.get("".concat(As,"/api/health"))).data}catch(e){throw console.error("\u274c Health check failed:",e),e}}onRecognitionResult(e){this.callbacks.onRecognitionResult=e}onError(e){this.callbacks.onError=e}onConnectionChange(e){this.callbacks.onConnectionChange=e}onRecognitionStarted(e){this.callbacks.onRecognitionStarted=e}onRecognitionStopped(e){this.callbacks.onRecognitionStopped=e}notifyConnectionChange(){this.callbacks.onConnectionChange&&this.callbacks.onConnectionChange(this.isConnected)}getConnectionStatus(){return{isConnected:this.isConnected,recognitionActive:this.recognitionActive}}};var Ns,zs,Ls,Ds,Fs,Ms,Is,Us,Bs,Hs,qs,Ws,$s,Vs,Ks,Qs,Ys,Gs,Xs,Js,Zs,el,tl,nl,rl,al,ol,il;const sl=Kt.div(Ns||(Ns=o(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),ll=Kt.nav(zs||(zs=o(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),cl=Kt.div(Ls||(Ls=o(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),ul=Kt.div(Ds||(Ds=o(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),dl=Kt.div(Fs||(Fs=o(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),fl=Kt.button(Ms||(Ms=o(["\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n"]))),pl=Kt.h1(Is||(Is=o(["\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n"]))),hl=Kt.p(Us||(Us=o(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n"]))),ml=Kt.div(Bs||(Bs=o(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"]))),gl=Kt.div(Hs||(Hs=o(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: ",";\n  border: 1px solid ",";\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: ",";\n  backdrop-filter: blur(10px);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"])),e=>e.isConnected?"var(--success-50)":"var(--error-50)",e=>e.isConnected?"var(--success-300)":"var(--error-300)",e=>e.isConnected?"var(--success-600)":"var(--error-600)"),yl=Kt.main(qs||(qs=o(["\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4) var(--space-12);\n  }\n"]))),vl=Kt.div(Ws||(Ws=o(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n"]))),bl=Kt.div($s||($s=o(["\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-6);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-glow);\n"]))),wl=Kt.h3(Vs||(Vs=o(["\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-4);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n"]))),kl=Kt.div(Ks||(Ks=o(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),xl=Kt.div(Qs||(Qs=o(["\n  position: relative;\n  border-radius: var(--radius-xl);\n  overflow: hidden;\n  background: var(--bg-secondary);\n  box-shadow: var(--shadow-lg);\n"]))),Sl=Kt(_r())(Ys||(Ys=o(["\n  width: 100%;\n  height: auto;\n  display: block;\n"]))),El=Kt.div(Gs||(Gs=o(["\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ",";\n  color: white;\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-full);\n  font-size: 0.875rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n"])),e=>e.isRecording?"var(--error-500)":"var(--success-500)"),_l=Kt.div(Xs||(Xs=o(["\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-6);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-glow);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n"]))),Cl=Kt.div(Js||(Js=o(["\n  font-size: 8rem;\n  margin-bottom: var(--space-4);\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));\n\n  @media (max-width: 768px) {\n    font-size: 6rem;\n  }\n"]))),Ol=Kt.h2(Zs||(Zs=o(["\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n"]))),Tl=Kt.p(el||(el=o(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  line-height: 1.6;\n  max-width: 300px;\n"]))),Rl=Kt.div(tl||(tl=o(["\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-top: var(--space-4);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-glow);\n  text-align: center;\n"]))),Pl=Kt.div(nl||(nl=o(["\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-2);\n"]))),Al=Kt.div(rl||(rl=o(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n"]))),jl=Kt.div(al||(al=o(["\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n  }\n"]))),Nl=Kt.button(ol||(ol=o(["\n  background: ",";\n  color: ",";\n  border: 1px solid ",";\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-xl);\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  backdrop-filter: blur(10px);\n  min-width: 200px;\n  justify-content: center;\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n    background: ",";\n    color: ",";\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 300px;\n  }\n"])),e=>"primary"===e.variant?"var(--primary-500)":"var(--bg-glass)",e=>"primary"===e.variant?"white":"var(--text-secondary)",e=>"primary"===e.variant?"var(--primary-500)":"var(--border-neural)",e=>"primary"===e.variant?"var(--primary-600)":"var(--primary-50)",e=>"primary"===e.variant?"white":"var(--primary-600)"),zl=Kt.div(il||(il=o(["\n  background: ",";\n  border: 1px solid ",";\n  color: ",";\n  padding: var(--space-4);\n  border-radius: var(--radius-xl);\n  text-align: center;\n  font-weight: 500;\n  margin-bottom: var(--space-6);\n"])),e=>{switch(e.type){case"error":return"var(--error-50)";case"success":return"var(--success-50)";default:return"var(--info-50)"}},e=>{switch(e.type){case"error":return"var(--error-300)";case"success":return"var(--success-300)";default:return"var(--info-300)"}},e=>{switch(e.type){case"error":return"var(--error-600)";case"success":return"var(--success-600)";default:return"var(--info-600)"}}),Ll=[{name:"Hello",emoji:"\ud83d\udc4b",description:"Wave your hand in a friendly greeting"},{name:"Thank You",emoji:"\ud83d\ude4f",description:"Bring your hand to your chin and move it forward"},{name:"Please",emoji:"\ud83e\udd32",description:"Rub your chest in a circular motion"},{name:"Yes",emoji:"\ud83d\udc4d",description:"Make a fist and nod it up and down"},{name:"No",emoji:"\ud83d\udc4e",description:"Make a fist and shake it side to side"}],Dl=e=>{let{onBackToHome:t}=e;const[n,a]=(0,r.useState)(!1),[o,i]=(0,r.useState)(0),[s,l]=(0,r.useState)(""),[c,u]=(0,r.useState)(!1),[d,f]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),[m,g]=(0,r.useState)([]),y=(0,r.useRef)(null),v=(0,r.useRef)(null),b=(0,r.useRef)([]);(0,r.useEffect)(()=>((async()=>{try{l("Connecting to AI backend..."),js.onConnectionChange(e=>{u(e),l(e?"Connected to AI backend":"Disconnected from AI backend")}),js.onRecognitionResult(e=>{h(e),e.sign&&l("Detected: ".concat(e.sign," (").concat(e.confidencePercent,"% confidence)"))}),js.onError(e=>{l("Error: ".concat(e.message))}),js.onRecognitionStarted(()=>{f(!0),l("AI recognition active - start signing!")}),js.onRecognitionStopped(()=>{f(!1),l("AI recognition stopped")}),await js.connect();const e=await js.getAvailableSigns();g(e)}catch(e){console.error("Failed to initialize ASL service:",e),l("Failed to connect to AI backend")}})(),()=>{js.disconnect()}),[]);const w=(0,r.useCallback)(()=>{const e=Math.floor(Math.random()*Ll.length);i(e)},[]),k=(0,r.useCallback)(async()=>{if(y.current)if(c)try{await js.startRecognition(),v.current=new MediaRecorder(y.current.stream,{mimeType:"video/webm"}),v.current.ondataavailable=e=>{e.data.size>0&&b.current.push(e.data)},v.current.onstop=()=>{const e=new Blob(b.current,{type:"video/webm"});URL.createObjectURL(e),(new Date).toISOString();b.current=[],l("Recording saved successfully!")},v.current.start(),a(!0),l("Recording started - AI is analyzing your signs...")}catch(e){console.error("Failed to start recording:",e),l("Failed to start recording")}else l("Not connected to AI backend");else l("Camera not available")},[c]),x=(0,r.useCallback)(()=>{v.current&&n&&(v.current.stop(),a(!1),js.stopRecognition(),l("Processing recording..."))},[n]);return r.useEffect(()=>{w()},[w]),(0,Kn.jsxs)(sl,{children:[(0,Kn.jsx)(ll,{children:(0,Kn.jsxs)(cl,{children:[(0,Kn.jsxs)(ul,{children:[(0,Kn.jsx)(dl,{children:(0,Kn.jsx)(ln,{size:24})}),"ASL Neural"]}),(0,Kn.jsxs)(fl,{onClick:t,children:[(0,Kn.jsx)(Cr,{size:18}),"Back to Home"]})]})}),(0,Kn.jsxs)(yl,{children:[(0,Kn.jsxs)("div",{style:{textAlign:"center",marginBottom:"var(--space-12)"},children:[(0,Kn.jsx)(gl,{isConnected:c,children:c?(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsx)(Or,{size:16}),"AI Backend Connected"]}):(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsx)(Tr,{size:16}),"Connecting to AI Backend..."]})}),d&&(0,Kn.jsxs)(ml,{children:[(0,Kn.jsx)(cn,{size:16}),"Real-time AI Recognition Active"]})]}),(0,Kn.jsx)(pl,{children:"AI Training Session"}),(0,Kn.jsx)(hl,{children:"Experience real-time neural network analysis as our AI learns from your sign language practice"}),(0,Kn.jsxs)(vl,{children:[(0,Kn.jsxs)(bl,{children:[(0,Kn.jsxs)(wl,{children:[(0,Kn.jsx)(kl,{children:(0,Kn.jsx)(fn,{size:24})}),"Neural Vision Feed"]}),(0,Kn.jsxs)(xl,{children:[(0,Kn.jsx)(Sl,{ref:y,audio:!1,screenshotFormat:"image/jpeg",videoConstraints:{width:640,height:480,facingMode:"user"}}),(0,Kn.jsx)(El,{isRecording:n,children:n?(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsx)("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"white",marginRight:"4px",animation:"pulse 1s infinite"}}),"Recording"]}):(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsx)(Rr,{size:16}),"Ready"]})})]}),p&&(0,Kn.jsx)(Rl,{children:p.sign?(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsxs)(Pl,{children:[(0,Kn.jsx)(Pr,{size:20,style:{marginRight:"8px"}}),p.sign]}),(0,Kn.jsxs)(Al,{children:["Confidence: ",p.confidencePercent,"%"]})]}):(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsxs)(Pl,{children:[(0,Kn.jsx)(Ar,{size:20,style:{marginRight:"8px"}}),p.message||"Processing..."]}),p.processing&&(0,Kn.jsxs)(Al,{children:[(0,Kn.jsx)(jr,{size:16,style:{marginRight:"4px",animation:"spin 1s linear infinite"}}),"Collecting frames..."]})]})})]}),(0,Kn.jsxs)(_l,{children:[(0,Kn.jsxs)(wl,{children:[(0,Kn.jsx)(kl,{children:(0,Kn.jsx)(gn,{size:24})}),"Target Pattern"]}),(0,Kn.jsx)(Cl,{children:Ll[o].emoji}),(0,Kn.jsx)(Ol,{children:Ll[o].name}),(0,Kn.jsx)(Tl,{children:Ll[o].description})]})]}),(0,Kn.jsxs)(jl,{children:[(0,Kn.jsxs)(Nl,{variant:"secondary",onClick:w,disabled:n||!c,children:[(0,Kn.jsx)(Nr,{size:18}),"New Pattern"]}),(0,Kn.jsx)(Nl,{variant:"primary",onClick:n?x:k,disabled:!c,children:n?(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsx)(zr,{size:18}),"Stop Neural Recording"]}):(0,Kn.jsxs)(Kn.Fragment,{children:[(0,Kn.jsx)(un,{size:18}),"Start Neural Recording"]})})]}),s&&(0,Kn.jsx)(zl,{type:s.includes("error")||s.includes("Failed")?"error":s.includes("success")?"success":"info",children:s})]})]})},Fl=sn("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),Ml=sn("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),Il=sn("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var Ul,Bl,Hl,ql,Wl,$l,Vl,Kl,Ql,Yl,Gl,Xl,Jl,Zl,ec;const tc=Kt.div(Ul||(Ul=o(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),nc=Kt.nav(Bl||(Bl=o(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),rc=Kt.div(Hl||(Hl=o(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),ac=Kt.div(ql||(ql=o(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  \n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),oc=Kt.div(Wl||(Wl=o(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n  \n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),ic=Kt.button($l||($l=o(["\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n"]))),sc=Kt.main(Vl||(Vl=o(["\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1400px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n"]))),lc=Kt.h1(Kl||(Kl=o(["\n  font-family: var(--font-primary);\n  font-size: 3rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n  \n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n"]))),cc=Kt.p(Ql||(Ql=o(["\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n  \n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),uc=Kt.div(Yl||(Yl=o(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n  \n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    margin-bottom: var(--space-16);\n  }\n"]))),dc=Kt.div(Gl||(Gl=o(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  text-align: center;\n  transition: var(--transition-normal);\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n  }\n"]))),fc=Kt.div(Xl||(Xl=o(["\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto var(--space-4);\n  box-shadow: var(--shadow-neural);\n"]))),pc=Kt.div(Jl||(Jl=o(["\n  font-size: 2rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-2);\n  font-family: var(--font-primary);\n"]))),hc=Kt.div(Zl||(Zl=o(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  font-weight: 500;\n"]))),mc=(Kt.h2(ec||(ec=o(["\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  text-align: center;\n\n  @media (max-width: 768px) {\n    font-size: 1.75rem;\n  }\n"]))),e=>{let{onBackToHome:t}=e;return(0,Kn.jsxs)(tc,{children:[(0,Kn.jsx)(nc,{children:(0,Kn.jsxs)(rc,{children:[(0,Kn.jsxs)(ac,{children:[(0,Kn.jsx)(oc,{children:(0,Kn.jsx)(ln,{size:24})}),"ASL Neural"]}),(0,Kn.jsxs)(ic,{onClick:t,children:[(0,Kn.jsx)(Cr,{size:18}),"Back to Home"]})]})}),(0,Kn.jsxs)(sc,{children:[(0,Kn.jsx)(lc,{children:"About ASL Neural"}),(0,Kn.jsx)(cc,{children:"Pioneering the future of accessibility through advanced artificial intelligence and computer vision technology for sign language education"}),(0,Kn.jsxs)(uc,{children:[(0,Kn.jsxs)(dc,{children:[(0,Kn.jsx)(fc,{children:(0,Kn.jsx)(Fl,{size:28,color:"white"})}),(0,Kn.jsx)(pc,{children:"50K+"}),(0,Kn.jsx)(hc,{children:"Global Users"})]}),(0,Kn.jsxs)(dc,{children:[(0,Kn.jsx)(fc,{children:(0,Kn.jsx)(pn,{size:28,color:"white"})}),(0,Kn.jsx)(pc,{children:"2.5M"}),(0,Kn.jsx)(hc,{children:"Neural Patterns"})]}),(0,Kn.jsxs)(dc,{children:[(0,Kn.jsx)(fc,{children:(0,Kn.jsx)(Ml,{size:28,color:"white"})}),(0,Kn.jsx)(pc,{children:"99.2%"}),(0,Kn.jsx)(hc,{children:"AI Accuracy"})]}),(0,Kn.jsxs)(dc,{children:[(0,Kn.jsx)(fc,{children:(0,Kn.jsx)(Il,{size:28,color:"white"})}),(0,Kn.jsx)(pc,{children:"15+"}),(0,Kn.jsx)(hc,{children:"Countries"})]})]})]})]})}),gc=sn("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),yc=sn("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),vc=sn("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),bc=sn("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),wc=sn("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var kc,xc,Sc,Ec,_c,Cc,Oc,Tc,Rc,Pc,Ac,jc,Nc,zc,Lc,Dc,Fc,Mc,Ic,Uc,Bc;const Hc=Kt.div(kc||(kc=o(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),qc=Kt.nav(xc||(xc=o(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),Wc=Kt.div(Sc||(Sc=o(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),$c=Kt.div(Ec||(Ec=o(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),Vc=Kt.div(_c||(_c=o(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),Kc=Kt.button(Cc||(Cc=o(["\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n"]))),Qc=Kt.main(Oc||(Oc=o(["\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n"]))),Yc=Kt.h1(Tc||(Tc=o(["\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n"]))),Gc=Kt.p(Rc||(Rc=o(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),Xc=Kt.div(Pc||(Pc=o(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-12);\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-8);\n  }\n"]))),Jc=Kt.div(Ac||(Ac=o(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n"]))),Zc=Kt.form(jc||(jc=o(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n"]))),eu=Kt.h2(Nc||(Nc=o(["\n  font-family: var(--font-primary);\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n"]))),tu=Kt.div(zc||(zc=o(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-6);\n  padding: var(--space-4);\n  border-radius: var(--radius-lg);\n  transition: var(--transition-normal);\n  \n  &:hover {\n    background: var(--primary-50);\n  }\n"]))),nu=Kt.div(Lc||(Lc=o(["\n  width: 48px;\n  height: 48px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),ru=Kt.div(Dc||(Dc=o(["\n  h3 {\n    font-size: 1.125rem;\n    font-weight: 600;\n    color: var(--text-primary);\n    margin-bottom: var(--space-1);\n  }\n  \n  p {\n    color: var(--text-secondary);\n    font-size: 0.9rem;\n  }\n"]))),au=Kt.div(Fc||(Fc=o(["\n  margin-bottom: var(--space-6);\n"]))),ou=Kt.label(Mc||(Mc=o(["\n  display: block;\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-bottom: var(--space-2);\n  font-size: 0.9rem;\n"]))),iu=Kt.input(Ic||(Ic=o(["\n  width: 100%;\n  padding: var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  transition: var(--transition-normal);\n  background: var(--bg-primary);\n  \n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: var(--shadow-glow);\n  }\n"]))),su=Kt.textarea(Uc||(Uc=o(["\n  width: 100%;\n  padding: var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  transition: var(--transition-normal);\n  background: var(--bg-primary);\n  min-height: 120px;\n  resize: vertical;\n  \n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: var(--shadow-glow);\n  }\n"]))),lu=Kt.button(Bc||(Bc=o(["\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-neural);\n  width: 100%;\n  justify-content: center;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n  }\n"]))),cu=e=>{let{onBackToHome:t}=e;const[n,a]=(0,r.useState)({name:"",email:"",subject:"",message:""}),o=e=>{a(Jt(Jt({},n),{},{[e.target.name]:e.target.value}))};return(0,Kn.jsxs)(Hc,{children:[(0,Kn.jsx)(qc,{children:(0,Kn.jsxs)(Wc,{children:[(0,Kn.jsxs)($c,{children:[(0,Kn.jsx)(Vc,{children:(0,Kn.jsx)(ln,{size:24})}),"ASL Neural"]}),(0,Kn.jsxs)(Kc,{onClick:t,children:[(0,Kn.jsx)(Cr,{size:18}),"Back to Home"]})]})}),(0,Kn.jsxs)(Qc,{children:[(0,Kn.jsx)(Yc,{children:"Contact Us"}),(0,Kn.jsx)(Gc,{children:"Get in touch with our team to learn more about ASL Neural technology or discuss partnership opportunities"}),(0,Kn.jsxs)(Xc,{children:[(0,Kn.jsxs)(Jc,{children:[(0,Kn.jsxs)(eu,{children:[(0,Kn.jsx)(gc,{size:24}),"Get in Touch"]}),(0,Kn.jsxs)(tu,{children:[(0,Kn.jsx)(nu,{children:(0,Kn.jsx)(yc,{size:20})}),(0,Kn.jsxs)(ru,{children:[(0,Kn.jsx)("h3",{children:"Email"}),(0,Kn.jsx)("p",{children:"<EMAIL>"})]})]}),(0,Kn.jsxs)(tu,{children:[(0,Kn.jsx)(nu,{children:(0,Kn.jsx)(vc,{size:20})}),(0,Kn.jsxs)(ru,{children:[(0,Kn.jsx)("h3",{children:"Phone"}),(0,Kn.jsx)("p",{children:"+****************"})]})]}),(0,Kn.jsxs)(tu,{children:[(0,Kn.jsx)(nu,{children:(0,Kn.jsx)(bc,{size:20})}),(0,Kn.jsxs)(ru,{children:[(0,Kn.jsx)("h3",{children:"Address"}),(0,Kn.jsxs)("p",{children:["123 AI Innovation Drive",(0,Kn.jsx)("br",{}),"San Francisco, CA 94105"]})]})]}),(0,Kn.jsxs)(tu,{children:[(0,Kn.jsx)(nu,{children:(0,Kn.jsx)(hn,{size:20})}),(0,Kn.jsxs)(ru,{children:[(0,Kn.jsx)("h3",{children:"Website"}),(0,Kn.jsx)("p",{children:"www.aslneural.ai"})]})]})]}),(0,Kn.jsxs)(Zc,{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",n),alert("Thank you for your message! We'll get back to you soon."),a({name:"",email:"",subject:"",message:""})},children:[(0,Kn.jsxs)(eu,{children:[(0,Kn.jsx)(wc,{size:24}),"Send Message"]}),(0,Kn.jsxs)(au,{children:[(0,Kn.jsx)(ou,{htmlFor:"name",children:"Name"}),(0,Kn.jsx)(iu,{type:"text",id:"name",name:"name",value:n.name,onChange:o,required:!0})]}),(0,Kn.jsxs)(au,{children:[(0,Kn.jsx)(ou,{htmlFor:"email",children:"Email"}),(0,Kn.jsx)(iu,{type:"email",id:"email",name:"email",value:n.email,onChange:o,required:!0})]}),(0,Kn.jsxs)(au,{children:[(0,Kn.jsx)(ou,{htmlFor:"subject",children:"Subject"}),(0,Kn.jsx)(iu,{type:"text",id:"subject",name:"subject",value:n.subject,onChange:o,required:!0})]}),(0,Kn.jsxs)(au,{children:[(0,Kn.jsx)(ou,{htmlFor:"message",children:"Message"}),(0,Kn.jsx)(su,{id:"message",name:"message",value:n.message,onChange:o,required:!0})]}),(0,Kn.jsxs)(lu,{type:"submit",children:[(0,Kn.jsx)(wc,{size:18}),"Send Message"]})]})]})]})]})};var uu,du;const fu=Kt.div(uu||(uu=o(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),pu=Kt.div(du||(du=o(["\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n"])));const hu=function(){const[e,t]=(0,r.useState)("home"),n=()=>{t("training")},a=()=>{t("about")},o=()=>{t("contact")},i=()=>{t("home")};return(0,Kn.jsx)(fu,{children:(0,Kn.jsx)(pu,{children:(()=>{switch(e){case"training":return(0,Kn.jsx)(Dl,{onBackToHome:i});case"about":return(0,Kn.jsx)(mc,{onBackToHome:i});case"contact":return(0,Kn.jsx)(cu,{onBackToHome:i});default:return(0,Kn.jsx)(Sr,{onStartTraining:n,onNavigateToAbout:a,onNavigateToContact:o})}})()})})},mu=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:o,getTTFB:i}=t;n(e),r(e),a(e),o(e),i(e)})};a.createRoot(document.getElementById("root")).render((0,Kn.jsx)(r.StrictMode,{children:(0,Kn.jsx)(hu,{})})),mu()})()})();
//# sourceMappingURL=main.7993d62a.js.map