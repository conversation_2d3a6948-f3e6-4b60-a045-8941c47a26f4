#!/usr/bin/env python3
"""
Startup script for the Sign Language Detection Backend
"""
import os
import sys
import subprocess

def check_requirements():
    """Check if required model files exist"""
    model_paths = [
        "../sign-language-recognition/streamlit/model.tflite",
        "../sign-language-recognition/weights/model.tflite"
    ]
    
    train_paths = [
        "../sign-language-recognition/streamlit/train.csv",
        "../sign-language-recognition/asl-signs/train.csv"
    ]
    
    model_found = any(os.path.exists(path) for path in model_paths)
    train_found = any(os.path.exists(path) for path in train_paths)
    
    if not model_found:
        print("❌ Error: model.tflite not found!")
        print("Expected locations:")
        for path in model_paths:
            print(f"  - {path}")
        return False
    
    if not train_found:
        print("❌ Error: train.csv not found!")
        print("Expected locations:")
        for path in train_paths:
            print(f"  - {path}")
        return False
    
    print("✅ Required model files found")
    return True

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting Sign Language Detection Backend...")
    print("📡 Server will be available at: http://localhost:8000")
    print("🔗 WebSocket endpoint: ws://localhost:8000/ws/detect")
    print("📊 Health check: http://localhost:8000/health")
    print("\nPress Ctrl+C to stop the server\n")
    
    try:
        subprocess.run([sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")

def main():
    print("🤖 Sign Language Detection Backend")
    print("=" * 40)
    
    if not check_requirements():
        print("\n💡 Please ensure the model files are in the correct location")
        sys.exit(1)
    
    if not install_requirements():
        print("\n💡 Please fix the requirements installation issues")
        sys.exit(1)
    
    start_server()

if __name__ == "__main__":
    main()
