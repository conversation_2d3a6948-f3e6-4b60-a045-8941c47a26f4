import React, { useState, useRef, useCallback } from 'react';
import styled from 'styled-components';
import Webcam from 'react-webcam';
import {
  Brain,
  Camera,
  ArrowLeft,
  RotateCcw,
  Play,
  Square,
  Download,
  Zap,
  Eye,
  Target
} from 'lucide-react';

const TrainingContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-neural);
  padding: var(--space-4) 0;
  transition: var(--transition-normal);
`;

const NavContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.25rem;
    gap: var(--space-2);
  }
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
`;

const BackButton = styled.button`
  background: var(--bg-glass);
  color: var(--text-secondary);
  border: 1px solid var(--border-neural);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-xl);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  backdrop-filter: blur(10px);

  &:hover {
    background: var(--primary-50);
    color: var(--primary-600);
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  @media (max-width: 768px) {
    padding: var(--space-2) var(--space-4);
    font-size: 0.85rem;
  }
`;

const PageTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: var(--space-4);
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    margin-bottom: var(--space-12);
    font-size: 1rem;
  }
`;

const StatusBadge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--bg-glass);
  border: 1px solid var(--border-neural);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  margin-bottom: var(--space-8);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-accent);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-glow);

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: var(--space-2) var(--space-3);
  }
`;

const MainContent = styled.main`
  padding: var(--space-20) var(--space-4) var(--space-16);
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: var(--space-16) var(--space-4) var(--space-12);
  }
`;

const TrainingGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto var(--space-12);

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
`;

const CameraSection = styled.div`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-neural);
    transform: scaleX(0);
    transition: var(--transition-normal);
  }

  &:hover {
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--primary-300);

    &::before {
      transform: scaleX(1);
    }
  }

  @media (max-width: 768px) {
    padding: var(--space-8);
  }
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 1.75rem;
  margin-bottom: var(--space-8);
  color: var(--text-primary);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.5rem;
    margin-bottom: var(--space-6);
  }
`;

const SectionIcon = styled.div`
  width: 48px;
  height: 48px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
  }
`;

const WebcamContainer = styled.div`
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  background: var(--neural-100);
  aspect-ratio: 4/3;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-neural);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);
`;

const StyledWebcam = styled(Webcam)`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const RecordingOverlay = styled.div`
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: ${props => props.isRecording ?
    'var(--error-500)' :
    'var(--neural-600)'
  };
  color: white;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-full);
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-lg);
  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
  }
`;

const SignSection = styled.div`
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }

  @media (max-width: 768px) {
    padding: var(--space-6);
  }
`;

const SignDisplay = styled.div`
  width: 200px;
  height: 200px;
  background: var(--primary-50);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  margin-bottom: var(--space-6);
  border: 2px solid var(--primary-200);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    border-color: var(--primary-300);
    background: var(--primary-100);
  }

  @media (max-width: 768px) {
    width: 150px;
    height: 150px;
    font-size: 2.5rem;
  }
`;

const SignName = styled.h3`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
`;

const SignDescription = styled.p`
  text-align: center;
  line-height: 1.6;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 400;
  max-width: 280px;
`;

const ControlsSection = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-top: var(--space-8);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
  }
`;

const ControlButton = styled.button`
  background: ${props => props.variant === 'primary'
    ? 'var(--primary-600)'
    : 'var(--bg-primary)'};
  border: ${props => props.variant === 'primary'
    ? 'none'
    : '1px solid var(--border-medium)'};
  color: ${props => props.variant === 'primary'
    ? 'white'
    : 'var(--text-primary)'};
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  box-shadow: ${props => props.variant === 'primary'
    ? 'var(--shadow-lg)'
    : 'var(--shadow-sm)'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.variant === 'primary'
      ? 'var(--shadow-xl)'
      : 'var(--shadow-md)'};
    background: ${props => props.variant === 'primary'
      ? 'var(--primary-700)'
      : 'var(--gray-50)'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: 280px;
  }
`;

const StatusMessage = styled.div`
  text-align: center;
  margin-top: var(--space-6);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  background: ${props =>
    props.type === 'success' ? 'var(--success-500)' :
    props.type === 'error' ? 'var(--error-500)' :
    'var(--primary-600)'
  };
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsSection = styled.div`
  margin-top: var(--space-16);
  background: var(--bg-secondary);
  padding: var(--space-12) var(--space-4);
  border-radius: var(--radius-2xl);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsTitle = styled.h3`
  font-family: var(--font-primary);
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const RecordingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
`;

const RecordingCard = styled.div`
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border-color: var(--primary-200);
    box-shadow: var(--shadow-lg);
  }
`;

const RecordingTitle = styled.p`
  margin: 0 0 var(--space-2) 0;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  font-family: var(--font-primary);
`;

const RecordingTime = styled.p`
  margin: 0 0 var(--space-4) 0;
  font-size: 0.8rem;
  color: var(--text-tertiary);
`;

const DownloadButton = styled.button`
  background: var(--primary-600);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-4);
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0 auto;

  &:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
  }
`;

// Sample sign language data
const signLanguageData = [
  {
    name: "Hello",
    emoji: "👋",
    description: "Wave your hand from side to side with your palm facing forward"
  },
  {
    name: "Thank You",
    emoji: "🙏",
    description: "Touch your chin with your fingertips and move your hand forward"
  },
  {
    name: "Yes",
    emoji: "👍",
    description: "Make a fist and nod it up and down"
  },
  {
    name: "No",
    emoji: "👎",
    description: "Make a fist and shake it from side to side"
  },
  {
    name: "Please",
    emoji: "🤲",
    description: "Place your open hand on your chest and move it in a circular motion"
  },
  {
    name: "Sorry",
    emoji: "😔",
    description: "Make a fist and rub it in a circular motion on your chest"
  }
];

const TrainingPage = ({ onBackToHome }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [currentSign, setCurrentSign] = useState(0);
  const [status, setStatus] = useState('');
  const [recordedVideos, setRecordedVideos] = useState([]);
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedChunksRef = useRef([]);

  const getRandomSign = useCallback(() => {
    const randomIndex = Math.floor(Math.random() * signLanguageData.length);
    setCurrentSign(randomIndex);
  }, []);

  const startRecording = useCallback(() => {
    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {
      mimeType: 'video/webm'
    });

    mediaRecorderRef.current.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunksRef.current.push(event.data);
      }
    };

    mediaRecorderRef.current.onstop = () => {
      const blob = new Blob(recordedChunksRef.current, {
        type: 'video/webm'
      });
      const url = URL.createObjectURL(blob);
      const timestamp = new Date().toISOString();
      
      setRecordedVideos(prev => [...prev, {
        id: timestamp,
        url,
        sign: signLanguageData[currentSign].name,
        timestamp
      }]);
      
      recordedChunksRef.current = [];
      setStatus('Recording saved successfully!');
    };

    mediaRecorderRef.current.start();
    setIsRecording(true);
    setStatus('Recording started...');
  }, [currentSign]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setStatus('Processing recording...');
    }
  }, [isRecording]);

  const downloadRecording = (video) => {
    const a = document.createElement('a');
    a.href = video.url;
    a.download = `sign_${video.sign}_${video.timestamp}.webm`;
    a.click();
  };

  React.useEffect(() => {
    getRandomSign();
  }, [getRandomSign]);

  return (
    <TrainingContainer>
      <Navigation>
        <NavContainer>
          <Logo>
            <LogoIcon>
              <Brain size={24} />
            </LogoIcon>
            ASL Neural
          </Logo>
          <BackButton onClick={onBackToHome}>
            <ArrowLeft size={18} />
            Back to Home
          </BackButton>
        </NavContainer>
      </Navigation>

      <MainContent>
        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>
          <StatusBadge>
            <Eye size={16} />
            Neural Vision Active
          </StatusBadge>
        </div>

        <PageTitle>AI Training Session</PageTitle>
        <PageSubtitle>
          Experience real-time neural network analysis as our AI learns from your sign language practice
        </PageSubtitle>

        <TrainingGrid>
          <CameraSection>
            <SectionTitle>
              <SectionIcon>
                <Camera size={24} />
              </SectionIcon>
              Neural Vision Feed
            </SectionTitle>
            <WebcamContainer>
              <StyledWebcam
                ref={webcamRef}
                audio={false}
                screenshotFormat="image/jpeg"
                videoConstraints={{
                  width: 640,
                  height: 480,
                  facingMode: "user"
                }}
              />
              <RecordingOverlay isRecording={isRecording}>
                {isRecording ? (
                  <>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: 'white',
                      marginRight: '4px'
                    }} />
                    Recording
                  </>
                ) : (
                  <>
                    <Eye size={16} />
                    Ready
                  </>
                )}
              </RecordingOverlay>
            </WebcamContainer>
          </CameraSection>

          <SignSection>
            <SectionTitle>
              <SectionIcon>
                <Target size={24} />
              </SectionIcon>
              Target Pattern
            </SectionTitle>
            <SignDisplay>
              {signLanguageData[currentSign].emoji}
            </SignDisplay>
            <SignName>{signLanguageData[currentSign].name}</SignName>
            <SignDescription>
              {signLanguageData[currentSign].description}
            </SignDescription>
          </SignSection>
        </TrainingGrid>

        <ControlsSection>
          <ControlButton
            variant="secondary"
            onClick={getRandomSign}
            disabled={isRecording}
          >
            <RotateCcw size={18} />
            New Pattern
          </ControlButton>

          <ControlButton
            variant="primary"
            onClick={isRecording ? stopRecording : startRecording}
          >
            {isRecording ? (
              <>
                <Square size={18} />
                Stop Neural Recording
              </>
            ) : (
              <>
                <Play size={18} />
                Start Neural Recording
              </>
            )}
          </ControlButton>
        </ControlsSection>

        {status && (
          <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>
            {status}
          </StatusMessage>
        )}

        {recordedVideos.length > 0 && (
          <RecordingsSection>
            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>
            <RecordingsGrid>
              {recordedVideos.map((video) => (
                <RecordingCard key={video.id}>
                  <RecordingTitle>{video.sign}</RecordingTitle>
                  <RecordingTime>
                    {new Date(video.timestamp).toLocaleString()}
                  </RecordingTime>
                  <DownloadButton onClick={() => downloadRecording(video)}>
                    <Download size={16} />
                    Download
                  </DownloadButton>
                </RecordingCard>
              ))}
            </RecordingsGrid>
          </RecordingsSection>
        )}
      </MainContent>
    </TrainingContainer>
  );
};

export default TrainingPage; 