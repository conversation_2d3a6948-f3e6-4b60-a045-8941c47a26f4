{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# **TensorFlow Guide**\n", "\n", "**Author: <PERSON>**\n", "\n", "**Sources used**:\n", "- [TensorFlow Guide](https://github.com/tensorflow/docs/blob/master/site/en/tutorials/quickstart/beginner.ipynb)\n", "\n"], "metadata": {"id": "9ty_isQP0VpJ"}}, {"cell_type": "markdown", "source": ["## **Introduction to TensorFlow**\n", "\n", "* **What is <PERSON><PERSON><PERSON><PERSON>?**\n", "  * TensorFlow is a powerful open-source software library for machine learning developed by Google. It's used for a wide range of tasks but has a particular focus on training and inference of deep neural networks.\n", "* **Applications in Image and Video Processing**\n", "  * TensorFlow excels in handling image and video data, making it an excellent choice for projects involving object detection, image classification, and even generative models. These capabilities are critical for developing systems that can \"see\" and interpret the world around them.\n", "* **Significance for Sign Language Recognition**\n", "  * For sign language recognition, <PERSON>sor<PERSON><PERSON>'s ability to process and learn from video and image data can be leveraged to train models that accurately recognize sign language gestures."], "metadata": {"id": "RdsSObNvMB4L"}}, {"cell_type": "markdown", "source": ["## **Set up Tensorflow**\n", "\n", "\n"], "metadata": {"id": "-c23Rw4O11TC"}}, {"cell_type": "markdown", "source": ["If you are in your own development environment you would typically install Tensorflow using `pip install`, however, TensorFlow comes installed by default on colab, so we can skip this step."], "metadata": {"id": "zI7QFMKBvmAX"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SUYNLFI9z70t"}, "outputs": [], "source": ["# !pip install tensorflow – Not needed for colab since it comes by default"]}, {"cell_type": "markdown", "source": ["To get started with TensorFlow we must first import it into our script"], "metadata": {"id": "Vwj-f3UUv2oY"}}, {"cell_type": "code", "source": ["import tensorflow as tf\n", "print(\"Tensorflow version: \", tf.__version__)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aSVcYcUDvaLl", "outputId": "cc82519d-4ce5-43f6-9964-022c6302bff7"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Tensorflow version:  2.15.0\n"]}]}, {"cell_type": "markdown", "source": ["## **Load a dataset**"], "metadata": {"id": "2r5QANL4wVlj"}}, {"cell_type": "markdown", "source": ["Let's begin by loading a prebuilt dataset. Keras, a high-level neural network API within TensorFlow, facilitates the development and training of deep learning models. It also provides a collection of easily accessible benchmark datasets. For this guide, we will utilize the MNIST dataset, known for its use in handwritten digit recognition tasks. This example will demonstrate how straightforward it is to get started with deep learning projects using TensorFlow and Keras.\n", "\n", "![mnist](https://upload.wikimedia.org/wikipedia/commons/f/f7/MnistExamplesModified.png)\n"], "metadata": {"id": "2wHk8xXMweoQ"}}, {"cell_type": "markdown", "source": ["## **Preparing the MNIST Dataset**\n", "\n", "The MNIST dataset, containing 60,000 training images and 10,000 testing images of handwritten digits, is key for learning image processing. Each 28x28 pixel image is grayscale, representing digits from 0 to 9. To use this dataset in TensorFlow:\n", "\n", "1. **Load the Dataset:**\\\n", "First, we load the dataset into our project using:\n", "```\n", "mnist = tf.keras.datasets.mnist\n", "(train_images, train_labels), (test_images, test_labels) = mnist.load_data()\n", "```\n", "This line loads the dataset and performs a train-test split, a critical practice to prevent overfitting and ensure model generalization. The train-test split divides the dataset into two parts: one for training the model to learn patterns and the other for testing to evaluate how well it generalizes to unseen data. Each image in the dataset comes with its own label, indicating the digit it represents, which is crucial for supervised learning tasks where the model learns to associate images with their corresponding labels.\n", "\n", "1. **Normalize the Data:**\\\n", "The pixel values range from 0 to 255. For optimal neural network performance, we scale these values to a range of 0 to 1 by dividing by 255.0. This normalization step converts the samples from integers to floating-point numbers, facilitating the training process due to smaller, standardized inputs.\n", "```\n", "train_images, test_images = train_images / 255.0, test_images / 255.0\n", "```\n", "\n", "By following these steps, we've successfully loaded and prepraed our dataset for training a neural netowrk model. This process ensures the model learns efficiently from normalized data, enhancing its ability to recognize and classify unseen handwritten digits accurately."], "metadata": {"id": "v4dioMYhyhI3"}}, {"cell_type": "code", "source": ["# Prepare data for use\n", "mnist = tf.keras.datasets.mnist\n", "\n", "# Load data into memory\n", "(train_images, train_labels), (test_images, test_labels) = mnist.load_data()\n", "\n", "# Train-test split\n", "train_images, test_images = train_images / 255.0, test_images / 255.0"], "metadata": {"id": "oNkkU9lyycdw", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "32f4e437-6976-40ba-ee01-f33fb8dc4c83"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading data from https://storage.googleapis.com/tensorflow/tf-keras-datasets/mnist.npz\n", "11490434/11490434 [==============================] - 0s 0us/step\n"]}]}, {"cell_type": "markdown", "source": ["## **Visualizing the MNIST Dataset**\n", "\n", "To better understand our data, let's visualize one of the images from the training set along with its corresponding label. This will give us a clearer picture of what our model is learning from."], "metadata": {"id": "USd95QQ9AWoX"}}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "\n", "# Display the first image in the training set\n", "plt.imshow(train_images[0], cmap='gray')\n", "plt.title(f'Label: {train_labels[0]}')\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "MPy3M_b9AcCw", "outputId": "1bc2f085-da33-4014-9749-a30b64f49b31"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAaAAAAGzCAYAAABpdMNsAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAgfklEQVR4nO3de3BU9fnH8c8SYbmYLAbIjZsEFERuFiFSEUEiSaqMIHa8TqF1sGBwUCootgK2tfGKDorITC1oFVBbAaUOVoGEWgM0XGSoSgkTCkgSEJvdECQg+f7+YNyfKwlwwoYnCe/XzHcme8732fPkeMyHs2f3rM855wQAwDnWxLoBAMD5iQACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCAALO0q5du+Tz+fTMM89E7Tlzc3Pl8/mUm5sbtecE6hsCCOelhQsXyufzqaCgwLqVOjFr1iz5fL6TRvPmza1bA8IusG4AQN2ZN2+eLrzwwvDjmJgYw26ASAQQ0Ijdcsstatu2rXUbQLV4CQ6owdGjRzVjxgz1799fgUBArVq10jXXXKM1a9bUWPPcc8+pc+fOatGiha699lpt27btpDlffPGFbrnlFsXHx6t58+a68sor9e677562n8OHD+uLL77QV199dca/g3NOoVBI3PQe9REBBNQgFArpj3/8o4YOHaonn3xSs2bN0oEDB5SRkaEtW7acNP+1117TnDlzlJ2drenTp2vbtm267rrrVFpaGp7z73//W1dddZU+//xzPfzww3r22WfVqlUrjRo1SkuXLj1lPxs2bNBll12mF1988Yx/h9TUVAUCAcXGxuquu+6K6AWwxktwQA0uuugi7dq1S82aNQsvGz9+vHr06KEXXnhBr7zySsT8wsJC7dixQ+3bt5ckZWZmKi0tTU8++aRmz54tSZo8ebI6deqkf/3rX/L7/ZKke++9V4MHD9ZDDz2k0aNHR633SZMmadCgQfL7/frHP/6huXPnasOGDSooKFBcXFxUtgOcDQIIqEFMTEz4on1VVZXKyspUVVWlK6+8Ups2bTpp/qhRo8LhI0kDBw5UWlqa3n//fc2ePVtff/21Vq9erd/+9rcqLy9XeXl5eG5GRoZmzpypL7/8MuI5vm/o0KFn/FLa5MmTIx6PGTNGAwcO1J133qmXXnpJDz/88Bk9D1CXeAkOOIVXX31Vffr0UfPmzdWmTRu1a9dOf/vb3xQMBk+ae8kll5y07NJLL9WuXbsknThDcs7p0UcfVbt27SLGzJkzJUn79++vs9/ljjvuUFJSkj766KM62wbgBWdAQA1ef/11jRs3TqNGjdLUqVOVkJCgmJgY5eTkaOfOnZ6fr6qqSpL04IMPKiMjo9o53bp1O6ueT6djx476+uuv63QbwJkigIAa/OUvf1Fqaqreeecd+Xy+8PLvzlZ+aMeOHSct+89//qOLL75Y0ok3BEhS06ZNlZ6eHv2GT8M5p127dumKK64459sGqsNLcEANvrv+8/3rLuvXr1d+fn6185ctW6Yvv/wy/HjDhg1av369srKyJEkJCQkaOnSo5s+fr+Li4pPqDxw4cMp+vLwNu7rnmjdvng4cOKDMzMzT1gPnAmdAOK/96U9/0sqVK09aPnnyZN1444165513NHr0aN1www0qKirSyy+/rJ49e+rQoUMn1XTr1k2DBw/WxIkTVVlZqeeff15t2rTRtGnTwnPmzp2rwYMHq3fv3ho/frxSU1NVWlqq/Px87d27V59++mmNvW7YsEHDhg3TzJkzNWvWrFP+Xp07d9att96q3r17q3nz5vr444+1ZMkS9evXT7/85S/PfAcBdYgAwnlt3rx51S4fN26cxo0bp5KSEs2fP18ffPCBevbsqddff11vv/12tTcJ/dnPfqYmTZro+eef1/79+zVw4EC9+OKLSk5ODs/p2bOnCgoK9Nhjj2nhwoU6ePCgEhISdMUVV2jGjBlR+73uvPNOffLJJ/rrX/+qI0eOqHPnzpo2bZp+/etfq2XLllHbDnA2fI6PSAMADHANCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYqHefA6qqqtK+ffsUGxsbcfsTAEDD4JxTeXm5UlJS1KRJzec59S6A9u3bp44dO1q3AQA4S3v27FGHDh1qXF/vXoKLjY21bgEAEAWn+3teZwE0d+5cXXzxxWrevLnS0tK0YcOGM6rjZTcAaBxO9/e8TgLozTff1JQpUzRz5kxt2rRJffv2VUZGRp1+2RYAoIFxdWDgwIEuOzs7/Pj48eMuJSXF5eTknLY2GAw6SQwGg8Fo4CMYDJ7y733Uz4COHj2qjRs3RnzhVpMmTZSenl7t96hUVlYqFApFDABA4xf1APrqq690/PhxJSYmRixPTExUSUnJSfNzcnIUCATCg3fAAcD5wfxdcNOnT1cwGAyPPXv2WLcEADgHov45oLZt2yomJkalpaURy0tLS5WUlHTSfL/fL7/fH+02AAD1XNTPgJo1a6b+/ftr1apV4WVVVVVatWqVBg0aFO3NAQAaqDq5E8KUKVM0duxYXXnllRo4cKCef/55VVRU6Oc//3ldbA4A0ADVSQDdeuutOnDggGbMmKGSkhL169dPK1euPOmNCQCA85fPOeesm/i+UCikQCBg3QYA4CwFg0HFxcXVuN78XXAAgPMTAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMXWDcA1CcxMTGeawKBQB10Eh2TJk2qVV3Lli0913Tv3t1zTXZ2tueaZ555xnPN7bff7rlGko4cOeK55oknnvBc89hjj3muaQw4AwIAmCCAAAAmoh5As2bNks/nixg9evSI9mYAAA1cnVwDuvzyy/XRRx/9/0Yu4FITACBSnSTDBRdcoKSkpLp4agBAI1En14B27NihlJQUpaam6s4779Tu3btrnFtZWalQKBQxAACNX9QDKC0tTQsXLtTKlSs1b948FRUV6ZprrlF5eXm183NychQIBMKjY8eO0W4JAFAPRT2AsrKy9NOf/lR9+vRRRkaG3n//fZWVlemtt96qdv706dMVDAbDY8+ePdFuCQBQD9X5uwNat26tSy+9VIWFhdWu9/v98vv9dd0GAKCeqfPPAR06dEg7d+5UcnJyXW8KANCARD2AHnzwQeXl5WnXrl365JNPNHr0aMXExNT6VhgAgMYp6i/B7d27V7fffrsOHjyodu3aafDgwVq3bp3atWsX7U0BABqwqAfQkiVLov2UqKc6derkuaZZs2aea3784x97rhk8eLDnGunENUuvxowZU6ttNTZ79+71XDNnzhzPNaNHj/ZcU9O7cE/n008/9VyTl5dXq22dj7gXHADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABM+55yzbuL7QqGQAoGAdRvnlX79+tWqbvXq1Z5r+G/bMFRVVXmu+cUvfuG55tChQ55raqO4uLhWdf/73/8812zfvr1W22qMgsGg4uLialzPGRAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwMQF1g3A3u7du2tVd/DgQc813A37hPXr13uuKSsr81wzbNgwzzWSdPToUc81f/7zn2u1LZy/OAMCAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCAAIAmCCAAAAmCCAAgAkCCABggpuRQl9//XWt6qZOneq55sYbb/Rcs3nzZs81c+bM8VxTW1u2bPFcc/3113uuqaio8Fxz+eWXe66RpMmTJ9eqDvCCMyAAgAkCCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCAAIAmCCAAAAmfM45Z93E94VCIQUCAes2UEfi4uI815SXl3uumT9/vucaSbr77rs919x1112eaxYvXuy5BmhogsHgKf+f5wwIAGCCAAIAmPAcQGvXrtXIkSOVkpIin8+nZcuWRax3zmnGjBlKTk5WixYtlJ6erh07dkSrXwBAI+E5gCoqKtS3b1/NnTu32vVPPfWU5syZo5dfflnr169Xq1atlJGRoSNHjpx1swCAxsPzN6JmZWUpKyur2nXOOT3//PP6zW9+o5tuukmS9NprrykxMVHLli3TbbfddnbdAgAajaheAyoqKlJJSYnS09PDywKBgNLS0pSfn19tTWVlpUKhUMQAADR+UQ2gkpISSVJiYmLE8sTExPC6H8rJyVEgEAiPjh07RrMlAEA9Zf4uuOnTpysYDIbHnj17rFsCAJwDUQ2gpKQkSVJpaWnE8tLS0vC6H/L7/YqLi4sYAIDGL6oB1KVLFyUlJWnVqlXhZaFQSOvXr9egQYOiuSkAQAPn+V1whw4dUmFhYfhxUVGRtmzZovj4eHXq1En333+/fv/73+uSSy5Rly5d9OijjyolJUWjRo2KZt8AgAbOcwAVFBRo2LBh4cdTpkyRJI0dO1YLFy7UtGnTVFFRoXvuuUdlZWUaPHiwVq5cqebNm0evawBAg8fNSNEoPf3007Wq++4fVF7k5eV5rvn+RxXOVFVVlecawBI3IwUA1EsEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABPcDRuNUqtWrWpV995773muufbaaz3XZGVlea75+9//7rkGsMTdsAEA9RIBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAAT3IwU+J6uXbt6rtm0aZPnmrKyMs81a9as8VxTUFDguUaS5s6d67mmnv0pQT3AzUgBAPUSAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAE9yMFDhLo0eP9lyzYMECzzWxsbGea2rrkUce8Vzz2muvea4pLi72XIOGg5uRAgDqJQIIAGCCAAIAmCCAAAAmCCAAgAkCCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACa4GSlgoFevXp5rZs+e7blm+PDhnmtqa/78+Z5rHn/8cc81X375peca2OBmpACAeokAAgCY8BxAa9eu1ciRI5WSkiKfz6dly5ZFrB83bpx8Pl/EyMzMjFa/AIBGwnMAVVRUqG/fvpo7d26NczIzM1VcXBweixcvPqsmAQCNzwVeC7KyspSVlXXKOX6/X0lJSbVuCgDQ+NXJNaDc3FwlJCSoe/fumjhxog4ePFjj3MrKSoVCoYgBAGj8oh5AmZmZeu2117Rq1So9+eSTysvLU1ZWlo4fP17t/JycHAUCgfDo2LFjtFsCANRDnl+CO53bbrst/HPv3r3Vp08fde3aVbm5udV+JmH69OmaMmVK+HEoFCKEAOA8UOdvw05NTVXbtm1VWFhY7Xq/36+4uLiIAQBo/Oo8gPbu3auDBw8qOTm5rjcFAGhAPL8Ed+jQoYizmaKiIm3ZskXx8fGKj4/XY489pjFjxigpKUk7d+7UtGnT1K1bN2VkZES1cQBAw+Y5gAoKCjRs2LDw4++u34wdO1bz5s3T1q1b9eqrr6qsrEwpKSkaMWKEfve738nv90evawBAg8fNSIEGonXr1p5rRo4cWattLViwwHONz+fzXLN69WrPNddff73nGtjgZqQAgHqJAAIAmCCAAAAmCCAAgAkCCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCu2EDOEllZaXnmgsu8PztLvr2228919Tmu8Vyc3M91+DscTdsAEC9RAABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwIT3uwcCOGt9+vTxXHPLLbd4rhkwYIDnGql2Nxatjc8++8xzzdq1a+ugE1jgDAgAYIIAAgCYIIAAACYIIACACQIIAGCCAAIAmCCAAAAmCCAAgAkCCABgggACAJgggAAAJgggAIAJbkYKfE/37t0910yaNMlzzc033+y5JikpyXPNuXT8+HHPNcXFxZ5rqqqqPNegfuIMCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCAAIAmCCAAAAmCCAAgAluRop6rzY34bz99ttrta3a3Fj04osvrtW26rOCggLPNY8//rjnmnfffddzDRoPzoAAACYIIACACU8BlJOTowEDBig2NlYJCQkaNWqUtm/fHjHnyJEjys7OVps2bXThhRdqzJgxKi0tjWrTAICGz1MA5eXlKTs7W+vWrdOHH36oY8eOacSIEaqoqAjPeeCBB/Tee+/p7bffVl5envbt21erL98CADRunt6EsHLlyojHCxcuVEJCgjZu3KghQ4YoGAzqlVde0aJFi3TddddJkhYsWKDLLrtM69at01VXXRW9zgEADdpZXQMKBoOSpPj4eEnSxo0bdezYMaWnp4fn9OjRQ506dVJ+fn61z1FZWalQKBQxAACNX60DqKqqSvfff7+uvvpq9erVS5JUUlKiZs2aqXXr1hFzExMTVVJSUu3z5OTkKBAIhEfHjh1r2xIAoAGpdQBlZ2dr27ZtWrJkyVk1MH36dAWDwfDYs2fPWT0fAKBhqNUHUSdNmqQVK1Zo7dq16tChQ3h5UlKSjh49qrKysoizoNLS0ho/TOj3++X3+2vTBgCgAfN0BuSc06RJk7R06VKtXr1aXbp0iVjfv39/NW3aVKtWrQov2759u3bv3q1BgwZFp2MAQKPg6QwoOztbixYt0vLlyxUbGxu+rhMIBNSiRQsFAgHdfffdmjJliuLj4xUXF6f77rtPgwYN4h1wAIAIngJo3rx5kqShQ4dGLF+wYIHGjRsnSXruuefUpEkTjRkzRpWVlcrIyNBLL70UlWYBAI2HzznnrJv4vlAopEAgYN0GzkBiYqLnmp49e3quefHFFz3X9OjRw3NNfbd+/XrPNU8//XSttrV8+XLPNVVVVbXaFhqvYDCouLi4GtdzLzgAgAkCCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCAAIAmCCAAAAmCCAAgIlafSMq6q/4+HjPNfPnz6/Vtvr16+e5JjU1tVbbqs8++eQTzzXPPvus55oPPvjAc80333zjuQY4VzgDAgCYIIAAACYIIACACQIIAGCCAAIAmCCAAAAmCCAAgAkCCABgggACAJgggAAAJgggAIAJAggAYIKbkZ4jaWlpnmumTp3quWbgwIGea9q3b++5pr47fPhwrermzJnjueYPf/iD55qKigrPNUBjwxkQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAE9yM9BwZPXr0Oak5lz777DPPNStWrPBc8+2333quefbZZz3XSFJZWVmt6gB4xxkQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEz7nnLNu4vtCoZACgYB1GwCAsxQMBhUXF1fjes6AAAAmCCAAgAlPAZSTk6MBAwYoNjZWCQkJGjVqlLZv3x4xZ+jQofL5fBFjwoQJUW0aANDweQqgvLw8ZWdna926dfrwww917NgxjRgxQhUVFRHzxo8fr+Li4vB46qmnoto0AKDh8/SNqCtXrox4vHDhQiUkJGjjxo0aMmRIeHnLli2VlJQUnQ4BAI3SWV0DCgaDkqT4+PiI5W+88Ybatm2rXr16afr06Tp8+HCNz1FZWalQKBQxAADnAVdLx48fdzfccIO7+uqrI5bPnz/frVy50m3dutW9/vrrrn379m706NE1Ps/MmTOdJAaDwWA0shEMBk+ZI7UOoAkTJrjOnTu7PXv2nHLeqlWrnCRXWFhY7fojR464YDAYHnv27DHfaQwGg8E4+3G6APJ0Deg7kyZN0ooVK7R27Vp16NDhlHPT0tIkSYWFheratetJ6/1+v/x+f23aAAA0YJ4CyDmn++67T0uXLlVubq66dOly2potW7ZIkpKTk2vVIACgcfIUQNnZ2Vq0aJGWL1+u2NhYlZSUSJICgYBatGihnTt3atGiRfrJT36iNm3aaOvWrXrggQc0ZMgQ9enTp05+AQBAA+Xluo9qeJ1vwYIFzjnndu/e7YYMGeLi4+Od3+933bp1c1OnTj3t64DfFwwGzV+3ZDAYDMbZj9P97edmpACAOsHNSAEA9RIBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwAQBBAAwQQABAEwQQAAAEwQQAMAEAQQAMEEAAQBMEEAAABMEEADABAEEADBBAAEATBBAAAATBBAAwES9CyDnnHULAIAoON3f83oXQOXl5dYtAACi4HR/z32unp1yVFVVad++fYqNjZXP54tYFwqF1LFjR+3Zs0dxcXFGHdpjP5zAfjiB/XAC++GE+rAfnHMqLy9XSkqKmjSp+TzngnPY0xlp0qSJOnTocMo5cXFx5/UB9h32wwnshxPYDyewH06w3g+BQOC0c+rdS3AAgPMDAQQAMNGgAsjv92vmzJny+/3WrZhiP5zAfjiB/XAC++GEhrQf6t2bEAAA54cGdQYEAGg8CCAAgAkCCABgggACAJgggAAAJhpMAM2dO1cXX3yxmjdvrrS0NG3YsMG6pXNu1qxZ8vl8EaNHjx7WbdW5tWvXauTIkUpJSZHP59OyZcsi1jvnNGPGDCUnJ6tFixZKT0/Xjh07bJqtQ6fbD+PGjTvp+MjMzLRpto7k5ORowIABio2NVUJCgkaNGqXt27dHzDly5Iiys7PVpk0bXXjhhRozZoxKS0uNOq4bZ7Ifhg4detLxMGHCBKOOq9cgAujNN9/UlClTNHPmTG3atEl9+/ZVRkaG9u/fb93aOXf55ZeruLg4PD7++GPrlupcRUWF+vbtq7lz51a7/qmnntKcOXP08ssva/369WrVqpUyMjJ05MiRc9xp3TrdfpCkzMzMiONj8eLF57DDupeXl6fs7GytW7dOH374oY4dO6YRI0aooqIiPOeBBx7Qe++9p7ffflt5eXnat2+fbr75ZsOuo+9M9oMkjR8/PuJ4eOqpp4w6roFrAAYOHOiys7PDj48fP+5SUlJcTk6OYVfn3syZM13fvn2t2zAlyS1dujT8uKqqyiUlJbmnn346vKysrMz5/X63ePFigw7PjR/uB+ecGzt2rLvppptM+rGyf/9+J8nl5eU55078t2/atKl7++23w3M+//xzJ8nl5+dbtVnnfrgfnHPu2muvdZMnT7Zr6gzU+zOgo0ePauPGjUpPTw8va9KkidLT05Wfn2/YmY0dO3YoJSVFqampuvPOO7V7927rlkwVFRWppKQk4vgIBAJKS0s7L4+P3NxcJSQkqHv37po4caIOHjxo3VKdCgaDkqT4+HhJ0saNG3Xs2LGI46FHjx7q1KlToz4efrgfvvPGG2+obdu26tWrl6ZPn67Dhw9btFejenc37B/66quvdPz4cSUmJkYsT0xM1BdffGHUlY20tDQtXLhQ3bt3V3FxsR577DFdc8012rZtm2JjY63bM1FSUiJJ1R4f3607X2RmZurmm29Wly5dtHPnTj3yyCPKyspSfn6+YmJirNuLuqqqKt1///26+uqr1atXL0knjodmzZqpdevWEXMb8/FQ3X6QpDvuuEOdO3dWSkqKtm7dqoceekjbt2/XO++8Y9htpHofQPh/WVlZ4Z/79OmjtLQ0de7cWW+99Zbuvvtuw85QH9x2223hn3v37q0+ffqoa9euys3N1fDhww07qxvZ2dnatm3beXEd9FRq2g/33HNP+OfevXsrOTlZw4cP186dO9W1a9dz3Wa16v1LcG3btlVMTMxJ72IpLS1VUlKSUVf1Q+vWrXXppZeqsLDQuhUz3x0DHB8nS01NVdu2bRvl8TFp0iStWLFCa9asifj+sKSkJB09elRlZWUR8xvr8VDTfqhOWlqaJNWr46HeB1CzZs3Uv39/rVq1KrysqqpKq1at0qBBgww7s3fo0CHt3LlTycnJ1q2Y6dKli5KSkiKOj1AopPXr15/3x8fevXt18ODBRnV8OOc0adIkLV26VKtXr1aXLl0i1vfv319NmzaNOB62b9+u3bt3N6rj4XT7oTpbtmyRpPp1PFi/C+JMLFmyxPn9frdw4UL32WefuXvuuce1bt3alZSUWLd2Tv3qV79yubm5rqioyP3zn/906enprm3btm7//v3WrdWp8vJyt3nzZrd582Ynyc2ePdtt3rzZ/fe//3XOOffEE0+41q1bu+XLl7utW7e6m266yXXp0sV98803xp1H16n2Q3l5uXvwwQddfn6+Kyoqch999JH70Y9+5C655BJ35MgR69ajZuLEiS4QCLjc3FxXXFwcHocPHw7PmTBhguvUqZNbvXq1KygocIMGDXKDBg0y7Dr6TrcfCgsL3W9/+1tXUFDgioqK3PLly11qaqobMmSIceeRGkQAOefcCy+84Dp16uSaNWvmBg4c6NatW2fd0jl36623uuTkZNesWTPXvn17d+utt7rCwkLrturcmjVrnKSTxtixY51zJ96K/eijj7rExETn9/vd8OHD3fbt222brgOn2g+HDx92I0aMcO3atXNNmzZ1nTt3duPHj290/0ir7veX5BYsWBCe880337h7773XXXTRRa5ly5Zu9OjRrri42K7pOnC6/bB79243ZMgQFx8f7/x+v+vWrZubOnWqCwaDto3/AN8HBAAwUe+vAQEAGicCCABgggACAJgggAAAJgggAIAJAggAYIIAAgCYIIAAACYIIACACQIIAGCCAAIAmPg/j66CP3HBuakAAAAASUVORK5CYII=\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## **Build a machine learning model**\n", "\n", "Building a machine learning model with <PERSON>sor<PERSON><PERSON> and <PERSON><PERSON> involves defining the model's architecture and specifying how it should learn from the data. Here's a step-by-step explanation:\n", "\n", "1. **Sequential Model:**\n", "```\n", "model = tf.keras.models.Sequential([\n", "```\n", "A `Sequential` model in Keras is a linear stack of layers, forming the simplest type of model for processing data. Here, data flows through layers in a straightforward sequence, without looping back. In this model, each layer receives an input tensor from the previous layer, performs operations, and produces an output tensor for the next layer. This way, the model transforms the input data step-by-step until it reaches the final output layer.\n", "\n", "  A **tensor** is essentially a container for data (numerical data), it's a generalization of scalars, vectors, and matrices to potentially higher dimensions.\n", "\n", "1. **<PERSON><PERSON>er:**\n", "```\n", "tf.keras.layers.Flatten(input_shape=(28, 28)),\n", "```\n", "The first layer in your model is a `Flatten` layer, which transforms the format of the images from a two-dimensional array (of 28 by 28 pixels) to a one-dimensional array (of 28 * 28 = 784 pixels). Think of this layer as unstacking rows of pixels in the image and lining them up. This layer has no parameters to learn; it only reformats the data. The `input_shape` argument specifies the shape of the input data which, in this case, is a 28x28 pixel image.\n", "\n", "1. **<PERSON><PERSON> with ReLU Activation:**\n", "```\n", "tf.keras.layers.Dense(128, activation='relu'),\n", "```\n", "After flattening the pixel values, the network consists of a sequence of two `Dense` layers. These are densely connected, or fully connected, neural layers. The first `Dense` layer has 128 nodes (or neurons). The `activation` parameter specifies the activation function to use. Here, 'relu' stands for rectified linear unit, a function that allows the model to solve nonlinear problems.\n", "\n", "1. **Dropout Layer:**\n", "```\n", "tf.keras.layers.Dropout(0.2),\n", "```\n", "The `Dropout` layer randomly sets input units to 0 with a frequency of 20% at each step during training, helping to prevent overfitting. This means that 20% of the randomly selected neurons are ignored during training, reducing the risk of overfitting by providing a form of regularization.\n", "\n", "1. **Output Den<PERSON> Layer:**\n", "```\n", "tf.keras.layers.Dense(10)\n", "```\n", "This final `Dense` layer consists of 10 nodes, each corresponding to one of the digit classes (0 through 9) the model identifies. It outputs logits, which are raw prediction scores indicating the model's confidence for each class. These logits can later be transformed into probabilities, offering a clearer interpretation of the model's predictions."], "metadata": {"id": "sqTsDfRS-VAD"}}, {"cell_type": "code", "source": ["model = tf.keras.models.Sequential([\n", "  tf.keras.layers.Flatten(input_shape=(28, 28)),\n", "  tf.keras.layers.Dense(128, activation='relu'),\n", "  tf.keras.layers.Dropout(0.2),\n", "  tf.keras.layers.Dense(10)\n", "])"], "metadata": {"id": "4UH2pK6KDFKM"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **Exploring Predictions**\n", "\n", "Bfore compiling and training our model, it would be helpful to better understand how predictions work by exploring one input image. As stated, the model's final layer outputs logits, which are raw scores for each class. To understand these predictions:\n", "\n", "1. **Make prediction:**\n", "```\n", "predictions = model(train_images[:1]).numpy()\n", "```\n", "Initially, we ask the model for its predictions on the first training image. The model returns logits, which are the raw scores from the final Dense layer for each class.\n", "\n", "1. **Convert logits into probabilities:**\n", "```\n", "tf.nn.softmax(predictions).numpy()\n", "```\n", "To convert these logits into probabilities, we apply the softmax function. This step demonstrates how to interpret the final layer's logits as probabilities, showing the model's confidence across the classes.\n", "\n", "\n"], "metadata": {"id": "43bUIjzIP_cg"}}, {"cell_type": "code", "source": ["predictions = model(train_images[:1]).numpy()\n", "predictions"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QTBOfMsLPbR9", "outputId": "09293dc7-139b-4abf-bae3-f0cd58026658"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0.8211072 ,  0.462401  , -0.09026444,  1.3257906 , -0.1497691 ,\n", "         0.23355341, -0.18482612,  0.41695774,  1.1724658 , -0.03064435]],\n", "      dtype=float32)"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["tf.nn.softmax(predictions).numpy()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AUSgUpEmVUIj", "outputId": "d393e62a-d28c-433e-b2fd-a1ce95113b93"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.13205922, 0.09225387, 0.05308417, 0.21875095, 0.05001756,\n", "        0.07338332, 0.04829447, 0.08815538, 0.18765573, 0.05634531]],\n", "      dtype=float32)"]}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "markdown", "source": ["## **Preparing for Training**\n", "\n", "Before training, we must compile the model. This involves specifying the optimizer, loss function, and metrics to use:\n", "\n", "1. **Optimizer**: The algorithm that adjusts the model's parameters based on the loss function's feedback to minimize errors during training. In this instace, we used 'Adaptive Moment Estimation' or 'adam' which is efficient with high accuracy.\n", "1. **Loss Function**:\n", "```\n", "loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)\n", "```\n", "<PERSON><PERSON><PERSON> how well the model's predictions match the actual labels. `SparseCategoricalCrossentropy` is sufficient for multi-class classification problems like ours\n", "1. **Metrics**: Used to monitor the training and testing steps. 'Accuracy' is a common metric for classification, indicating the percentage of correct predictions.\n", "\n", "1. **Compiling the model**\n", "```\n", "model.compile(optimizer='adam',\n", "              loss=loss_fn,\n", "              metrics=['accuracy'])\n", "```\n", "\n", "This is essentially preparing the model with the necessary settings for when training begins."], "metadata": {"id": "ZEzAqqy-V1Ey"}}, {"cell_type": "code", "source": ["# Loss function\n", "loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)\n", "\n", "# Compile model\n", "model.compile(optimizer='adam',\n", "              loss=loss_fn,\n", "              metrics=['accuracy'])"], "metadata": {"id": "tfsmqMQVkyMM"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **Train and Evaluate**\n", "\n", "After setting up the model, the next steps are training it on the dataset and then evaluating its performance.\n", "\n", "1. **Training the Model**:\n", "```\n", "model.fit(train_images, train_labels, epochs=5)\n", "```\n", "* `model.fit()` starts the training process. The model learns to associate the images (`train_images`) with the correct labels (`train_labels`).\n", "* `epochs=5` An epoch is one complete presentation of data set to the model. In this instance, we specify 5 epochs, which means the model will see the entire dataset five times. With each epoch, the model adjusts its weight slightly to reduce the error in its predictions\n", "\n", "2. **Evaluating the model**\n", "```\n", "model.evaluate(test_images, test_labels, verbose=2)\n", "```\n", "* `model.evaluate()`: After training, we evaluate the model's performance using a different dataset that it hasn't seen before (test_images and test_labels). This function tests the model's accuracy and gives us a sense of how well our model can generalize to new data.\n", "* `verbose=2`: This parameter controls the detail of the output displayed during evaluation. A value of 2 shows progress bars and a summary of the losses and metrics for each epoch.\n", "\n", "3. **Making Predictions with the Model**\n", "```\n", "probability_model = tf.keras.Sequential([\n", "  model,\n", "  tf.keras.layers.Softmax()\n", "])\n", "```\n", "* To use the model for making predictions, we often want the output to be more interpretable, such as providing probabilities that an image belongs to each of the possible classes. To achieve this, we create a new model that adds a softmax layer on top of our existing model.\n", "\n", "* `tf.keras.Sequential()`: This command is used to stack the pre-trained model with a new layer.\n", "\n", "* `model`: Our pre-trained model is added as the base.\n", "\n", "* `tf.keras.layers.Softmax()`: A softmax layer is added on top. This layer converts the logits, or raw prediction scores, from our model into probabilities by applying the softmax function. Each score is transformed into a probability value, indicating the likelihood of the input belonging to one of the classes.\n", "\n"], "metadata": {"id": "EC0KUP86lbe2"}}, {"cell_type": "code", "source": ["# Train model\n", "model.fit(train_images, train_labels, epochs=5)\n", "\n", "# Evaluating the model\n", "model.evaluate(test_images, test_labels, verbose=2)\n", "\n", "# Making predictions with the model\n", "probability_model = tf.keras.Sequential([\n", "  model,\n", "  tf.keras.layers.Softmax()\n", "])\n"], "metadata": {"id": "EawVvLY4k4O0", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "f8b93805-77ab-433a-d644-4d6f631d686a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/5\n", "1875/1875 [==============================] - 5s 2ms/step - loss: 0.3058 - accuracy: 0.9112\n", "Epoch 2/5\n", "1875/1875 [==============================] - 4s 2ms/step - loss: 0.1469 - accuracy: 0.9557\n", "Epoch 3/5\n", "1875/1875 [==============================] - 3s 2ms/step - loss: 0.1102 - accuracy: 0.9668\n", "Epoch 4/5\n", "1875/1875 [==============================] - 4s 2ms/step - loss: 0.0912 - accuracy: 0.9722\n", "Epoch 5/5\n", "1875/1875 [==============================] - 4s 2ms/step - loss: 0.0763 - accuracy: 0.9765\n", "313/313 - 1s - loss: 0.0741 - accuracy: 0.9769 - 612ms/epoch - 2ms/step\n"]}]}, {"cell_type": "markdown", "source": ["## Visualizing Predictions\n", "\n", "To further understand how our model performs, let's visualize its predictions on an image. We'll display an image from the test set, use our `probability_model` to predict the probabilities for each class, and then highlight the class with the highest probability along with its label."], "metadata": {"id": "cNJCe_rI9xkY"}}, {"cell_type": "code", "source": ["import numpy as np\n", "\n", "# Selecting a sample image from the test set\n", "sample_image = test_images[0]\n", "sample_label = test_labels[0]\n", "\n", "# Predicting the class probabilities for the selected image\n", "predictions = probability_model.predict(test_images[:1])\n", "\n", "# Visualizing the selected image\n", "plt.figure(figsize=(2, 2))\n", "plt.imshow(sample_image, cmap=plt.cm.binary)\n", "plt.title(f\"Actual Label: {sample_label}\")\n", "plt.show()\n", "\n", "# Displaying the predicted probabilities for all classes\n", "print(\"Predicted probabilities:\")\n", "print(predictions)\n", "\n", "# Finding the class with the highest probability\n", "predicted_label = np.argmax(predictions[0])\n", "probability = np.max(predictions[0])\n", "\n", "print(f\"Predicted Label: {predicted_label} with a probability of {probability:.2f}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 326}, "id": "72qO8kDD8vQD", "outputId": "a1ca2a49-2c4e-46e0-eefe-8807fc00cbf0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["1/1 [==============================] - 0s 55ms/step\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 200x200 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Predicted probabilities:\n", "[[4.6812190e-08 2.7573621e-09 4.2093925e-06 5.6728040e-04 2.8949121e-11\n", "  1.6476713e-07 2.7183915e-12 9.9942672e-01 4.5148173e-08 1.5781640e-06]]\n", "Predicted Label: 7 with a probability of 1.00\n"]}]}]}