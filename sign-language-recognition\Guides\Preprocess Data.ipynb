{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Preparing the Google - Isolated Sign Language Recognition Dataset\n", "\n", "**Author: <PERSON>**\n", "\n", "---\n", "\n", "* The Google - Isolated Sign Language Recognition dataset is organized into sequenced data, facilitating the training of our Long Short-Term Memory (LSTM) model. Given the dataset's structured format, the preparation process focuses on optimizing this data for LSTM model training:\n", "\n", "  * **Understanding the Dataset Structure**: The dataset contains sequenced landmarks for sign language gestures, with each row indicating a landmark position in (x, y, z) coordinates across video frames. This structure is essential for preprocessing and training.\n", "  * **Preprocessing the Data:** The preparation involves several key steps:\n", "    - **Data Consistency:** Ensure each data sequence is complete and accurate.\n", "    - **Normalization:** Adjust the x, y, and z coordinates to a uniform range suitable for LSTM input.\n", "    - **Creating the TensorFlow Dataset** Constructing a TensorFlow dataset suitable for training a model on the sign language recognition data.\n", "    - **Data Augmentation (Optional):** Apply augmentation techniques to improve model robustness and generalization.\n", "\n"], "metadata": {"id": "idajnQn5KlvA"}}, {"cell_type": "markdown", "source": ["## Download dataset\n", "\n", "> To adjust for Colabs limited disk space I reduced the dataset for testing/learning purposes.\n", "\n", "\n", "\n", "\n"], "metadata": {"id": "eG_n-gMVQ8xD"}}, {"cell_type": "code", "source": ["!wget -O asl-signs.zip \"https://dl.dropboxusercontent.com/scl/fi/fj6lzw4ftkixg17mc86t2/asl-signs.zip?rlkey=26s7nawa7mwpt2i0gjanixnmp&dl=1\"\n", "!unzip -q asl-signs.zip"], "metadata": {"id": "mnqkLzeR-yrM", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "efb62fed-7035-4887-a551-cd0fac7e6cff"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--2024-03-30 21:59:04--  https://dl.dropboxusercontent.com/scl/fi/fj6lzw4ftkixg17mc86t2/asl-signs.zip?rlkey=26s7nawa7mwpt2i0gjanixnmp&dl=1\n", "Resolving dl.dropboxusercontent.com (dl.dropboxusercontent.com)... *************, 2620:100:6018:15::a27d:30f\n", "Connecting to dl.dropboxusercontent.com (dl.dropboxusercontent.com)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 871848618 (831M) [application/zip]\n", "Saving to: ‘asl-signs.zip’\n", "\n", "asl-signs.zip       100%[===================>] 831.46M  22.5MB/s    in 34s     \n", "\n", "2024-03-30 21:59:39 (24.6 MB/s) - ‘asl-signs.zip’ saved [871848618/871848618]\n", "\n"]}]}, {"cell_type": "markdown", "source": ["## **Data Walkthrough**"], "metadata": {"id": "WxwbJlOXubs7"}}, {"cell_type": "markdown", "source": ["### Import Libraries and Set Directories\n", "\n", "### Import Libraries\n", "* We import three essential libraries for data manipulation and visualization:\n", "  - **pandas**: Used for data manipulation and analysis. It provides data structures and operations for manipulating numerical tables and time series, making it ideal for working with structured data.\n", "  - **numpy**: A library for numerical computing in Python. It offers comprehensive mathematical functions, random number generators, linear algebra routines, Fourier transforms, and more.\n", "  - **matplotlib**: A plotting library for creating static, interactive, and animated visualizations in Python. It's used here for data visualization purposes, allowing us to graph our data and insights effectively.\n", "  - **pyarrow.parquet**: A library to efficiently read and write parquet files, a columnar storage file format optimized for performance in data analysis workflows. This is particularly important for working with the landmark data stored in parquet format.\n", "\n", "### Set File Directories\n", "\n", "- `LANDMARK_FILES_DIR`: This directory contains the landmark files (.parquet) extracted from sign language videos. Each file, corresponding to a unique sign sequence, stores frame-by-frame landmark data, including positions (x, y, z) for various facial and hand landmarks.\n", "- `TRAIN_FILE`: The path to the `train.csv` file, which serves as an index for our dataset. It lists the paths to individual landmark files within `LANDMARK_FILES_DIR`, alongside their associated `participant_id`, `sequence_id`, and the `sign` label indicating the gesture performed.\n", "\n"], "metadata": {"id": "_4wuiWMHDKKr"}}, {"cell_type": "code", "source": ["# import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pyarrow.parquet as pq\n", "\n", "# set files directories\n", "LANDMARK_FILES_DIR = \"/content/asl-signs/train_landmark_files\"\n", "TRAIN_FILE = \"/content/asl-signs/train.csv\""], "metadata": {"id": "Zt_dcLziDNNh"}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": ["### Inspect Data\n", "\n", "In this section, our goal is to familiarize ourselves with the structure and content of our dataset by loading and inspecting the initial entries from both `train.csv` and a sample parquet file.\n", "\n", "### Inspect `train.csv`\n", "First, we'll load the `train.csv` file using pandas and display the first five rows. This file contains the metadata for our dataset, including the paths to the parquet files that store the actual sign language sequence data, along with the corresponding sign labels.\n", "\n", "```python\n", "# Load the train.csv file\n", "dataframe = pd.read_csv(TRAIN_FILE)\n", "\n", "# Display the first five entries\n", "dataframe.head()\n", "```\n", "\n", "This step gives us insight into how the dataset is organized and how we can access the individual sign language sequences stored in parquet files.\n", "\n", "### Inspect a <PERSON><PERSON>t File\n", "Next, we'll load a sample parquet file to examine the detailed frame-by-frame landmark data. Each parquet file corresponds to a sign language sequence, containing landmarks for each frame of the video. We'll use the `pyarrow.parquet` library to read a parquet file and then display the first five entries.\n", "\n", "```python\n", "# Sample parquet file path (example)\n", "sample_parquet_path = LANDMARK_FILES_DIR + '/26734/1000035562.parquet'\n", "\n", "# Load the sample parquet file\n", "sample_parquet_df = pq.read_table(sample_parquet_path).to_pandas()\n", "\n", "# Display the first five entries\n", "sample_parquet_df.head()\n", "```\n", "\n", "Inspecting a sample parquet file allows us to understand the granularity and format of the landmark data, including the coordinates (x, y, z) for each landmark across different frames.\n"], "metadata": {"id": "6SBeIDj5HUm6"}}, {"cell_type": "code", "source": ["# Load the train.csv file\n", "dataframe = pd.read_csv(TRAIN_FILE)\n", "\n", "# Display the first five entries\n", "dataframe.head()"], "metadata": {"id": "CgHNgya-HWCQ", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "outputId": "40ab53a6-8518-47b6-ab35-28df29cc5c31"}, "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                            path  participant_id  sequence_id  \\\n", "0  train_landmark_files/26734/1000035562.parquet           26734   1000035562   \n", "1  train_landmark_files/28656/1000106739.parquet           28656   1000106739   \n", "2   train_landmark_files/16069/100015657.parquet           16069    100015657   \n", "3  train_landmark_files/25571/1000210073.parquet           25571   1000210073   \n", "4  train_landmark_files/62590/1000240708.parquet           62590   1000240708   \n", "\n", "    sign  \n", "0   blow  \n", "1   wait  \n", "2  cloud  \n", "3   bird  \n", "4   owie  "], "text/html": ["\n", "  <div id=\"df-679f0e12-b66a-490c-8883-d96290d09576\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>participant_id</th>\n", "      <th>sequence_id</th>\n", "      <th>sign</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>train_landmark_files/26734/1000035562.parquet</td>\n", "      <td>26734</td>\n", "      <td>1000035562</td>\n", "      <td>blow</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>train_landmark_files/28656/1000106739.parquet</td>\n", "      <td>28656</td>\n", "      <td>1000106739</td>\n", "      <td>wait</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>train_landmark_files/16069/100015657.parquet</td>\n", "      <td>16069</td>\n", "      <td>100015657</td>\n", "      <td>cloud</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>train_landmark_files/25571/1000210073.parquet</td>\n", "      <td>25571</td>\n", "      <td>1000210073</td>\n", "      <td>bird</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>train_landmark_files/62590/1000240708.parquet</td>\n", "      <td>62590</td>\n", "      <td>1000240708</td>\n", "      <td>owie</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-679f0e12-b66a-490c-8883-d96290d09576')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-679f0e12-b66a-490c-8883-d96290d09576 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-679f0e12-b66a-490c-8883-d96290d09576');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-ce136d4b-dcc0-40e8-9c0d-4fafc4db8fd3\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-ce136d4b-dcc0-40e8-9c0d-4fafc4db8fd3')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-ce136d4b-dcc0-40e8-9c0d-4fafc4db8fd3 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "dataframe", "summary": "{\n  \"name\": \"dataframe\",\n  \"rows\": 1884,\n  \"fields\": [\n    {\n      \"column\": \"path\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1884,\n        \"samples\": [\n          \"train_landmark_files/4718/503647209.parquet\",\n          \"train_landmark_files/36257/508554927.parquet\",\n          \"train_landmark_files/37779/2658181039.parquet\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"participant_id\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16152,\n        \"min\": 2044,\n        \"max\": 62590,\n        \"num_unique_values\": 21,\n        \"samples\": [\n          26734,\n          30680,\n          53618\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"sequence_id\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1246500414,\n        \"min\": 3268589,\n        \"max\": 4294662486,\n        \"num_unique_values\": 1884,\n        \"samples\": [\n          503647209,\n          508554927,\n          2658181039\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"sign\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"wait\",\n          \"owie\",\n          \"cloud\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 3}]}, {"cell_type": "code", "source": ["# Sample parquet file path (example)\n", "sample_parquet_path = LANDMARK_FILES_DIR + '/26734/1000035562.parquet'\n", "\n", "# Load the sample parquet file\n", "sample_parquet_df = pq.read_table(sample_parquet_path).to_pandas()\n", "\n", "# Display the first five entries\n", "sample_parquet_df.head()\n"], "metadata": {"id": "FYPNs0cwH0T-", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "outputId": "7d2e53f0-66f1-4131-e033-81a3507fdf68"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   frame     row_id  type  landmark_index         x         y         z\n", "0     20  20-face-0  face               0  0.494400  0.380470 -0.030626\n", "1     20  20-face-1  face               1  0.496017  0.350735 -0.057565\n", "2     20  20-face-2  face               2  0.500818  0.359343 -0.030283\n", "3     20  20-face-3  face               3  0.489788  0.321780 -0.040622\n", "4     20  20-face-4  face               4  0.495304  0.341821 -0.061152"], "text/html": ["\n", "  <div id=\"df-8ecf6f89-700b-4dd0-aa90-fb391d76251b\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>row_id</th>\n", "      <th>type</th>\n", "      <th>landmark_index</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>20-face-0</td>\n", "      <td>face</td>\n", "      <td>0</td>\n", "      <td>0.494400</td>\n", "      <td>0.380470</td>\n", "      <td>-0.030626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>20-face-1</td>\n", "      <td>face</td>\n", "      <td>1</td>\n", "      <td>0.496017</td>\n", "      <td>0.350735</td>\n", "      <td>-0.057565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>20-face-2</td>\n", "      <td>face</td>\n", "      <td>2</td>\n", "      <td>0.500818</td>\n", "      <td>0.359343</td>\n", "      <td>-0.030283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>20-face-3</td>\n", "      <td>face</td>\n", "      <td>3</td>\n", "      <td>0.489788</td>\n", "      <td>0.321780</td>\n", "      <td>-0.040622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>20-face-4</td>\n", "      <td>face</td>\n", "      <td>4</td>\n", "      <td>0.495304</td>\n", "      <td>0.341821</td>\n", "      <td>-0.061152</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-8ecf6f89-700b-4dd0-aa90-fb391d76251b')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-8ecf6f89-700b-4dd0-aa90-fb391d76251b button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-8ecf6f89-700b-4dd0-aa90-fb391d76251b');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-64d4ea3f-64a5-4322-b509-656bf64f8f21\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-64d4ea3f-64a5-4322-b509-656bf64f8f21')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-64d4ea3f-64a5-4322-b509-656bf64f8f21 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "sample_parquet_df", "summary": "{\n  \"name\": \"sample_parquet_df\",\n  \"rows\": 12489,\n  \"fields\": [\n    {\n      \"column\": \"frame\",\n      \"properties\": {\n        \"dtype\": \"int16\",\n        \"num_unique_values\": 23,\n        \"samples\": [\n          35,\n          29,\n          20\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"row_id\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 12489,\n        \"samples\": [\n          \"38-face-330\",\n          \"28-face-323\",\n          \"34-face-375\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"left_hand\",\n          \"right_hand\",\n          \"face\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"landmark_index\",\n      \"properties\": {\n        \"dtype\": \"int16\",\n        \"num_unique_values\": 468,\n        \"samples\": [\n          55,\n          63,\n          33\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"x\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.09932152293412004,\n        \"min\": -0.03181135654449463,\n        \"max\": 1.2943496704101562,\n        \"num_unique_values\": 11741,\n        \"samples\": [\n          0.4619339406490326,\n          0.5358983874320984,\n          0.48361635208129883\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"y\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.22156446361418555,\n        \"min\": 0.22415050864219666,\n        \"max\": 2.1478259563446045,\n        \"num_unique_values\": 11742,\n        \"samples\": [\n          0.39721888303756714,\n          0.3795875608921051,\n          0.5732156038284302\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"z\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.27852817819598413,\n        \"min\": -2.7826244831085205,\n        \"max\": 1.9108154773712158,\n        \"num_unique_values\": 11752,\n        \"samples\": [\n          0.012107877060770988,\n          -0.02674858830869198,\n          0.030765144154429436\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "markdown", "source": ["### Visualize Data\n", "\n", "* This visualization helps us better understand the data and identify patterns. It's important to note that this step is for understanding, not part of model training.\n", "\n", "* **Sources**: [Animated Data Visualization\n", "](https://www.kaggle.com/code/danie<PERSON><PERSON><PERSON><PERSON>/animated-data-visualization)"], "metadata": {"id": "EMFlIGSOI8kZ"}}, {"cell_type": "code", "source": ["from matplotlib.animation import FuncAnimation\n", "from IPython.display import HTML\n", "\n", "train = pd.read_csv(TRAIN_FILE)\n", "dir = '/content/asl-signs'\n", "\n", "## Change this directory to any file\n", "path_to_sign = 'train_landmark_files/26734/1000035562.parquet'\n", "sign = pd.read_parquet(f'{dir}/{path_to_sign}')\n", "sign.y = sign.y * -1"], "metadata": {"id": "SZLMyDWRIEDz"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["def get_hand_points(hand):\n", "    x = [[hand.iloc[0].x, hand.iloc[1].x, hand.iloc[2].x, hand.iloc[3].x, hand.iloc[4].x], # Thumb\n", "         [hand.iloc[5].x, hand.iloc[6].x, hand.iloc[7].x, hand.iloc[8].x], # Index\n", "         [hand.iloc[9].x, hand.iloc[10].x, hand.iloc[11].x, hand.iloc[12].x],\n", "         [hand.iloc[13].x, hand.iloc[14].x, hand.iloc[15].x, hand.iloc[16].x],\n", "         [hand.iloc[17].x, hand.iloc[18].x, hand.iloc[19].x, hand.iloc[20].x],\n", "         [hand.iloc[0].x, hand.iloc[5].x, hand.iloc[9].x, hand.iloc[13].x, hand.iloc[17].x, hand.iloc[0].x]]\n", "\n", "    y = [[hand.iloc[0].y, hand.iloc[1].y, hand.iloc[2].y, hand.iloc[3].y, hand.iloc[4].y],  #Thumb\n", "         [hand.iloc[5].y, hand.iloc[6].y, hand.iloc[7].y, hand.iloc[8].y], # Index\n", "         [hand.iloc[9].y, hand.iloc[10].y, hand.iloc[11].y, hand.iloc[12].y],\n", "         [hand.iloc[13].y, hand.iloc[14].y, hand.iloc[15].y, hand.iloc[16].y],\n", "         [hand.iloc[17].y, hand.iloc[18].y, hand.iloc[19].y, hand.iloc[20].y],\n", "         [hand.iloc[0].y, hand.iloc[5].y, hand.iloc[9].y, hand.iloc[13].y, hand.iloc[17].y, hand.iloc[0].y]]\n", "    return x, y\n", "\n", "def get_pose_points(pose):\n", "    x = [[pose.iloc[8].x, pose.iloc[6].x, pose.iloc[5].x, pose.iloc[4].x, pose.iloc[0].x, pose.iloc[1].x, pose.iloc[2].x, pose.iloc[3].x, pose.iloc[7].x],\n", "         [pose.iloc[10].x, pose.iloc[9].x],\n", "         [pose.iloc[22].x, pose.iloc[16].x, pose.iloc[20].x, pose.iloc[18].x, pose.iloc[16].x, pose.iloc[14].x, pose.iloc[12].x,\n", "          pose.iloc[11].x, pose.iloc[13].x, pose.iloc[15].x, pose.iloc[17].x, pose.iloc[19].x, pose.iloc[15].x, pose.iloc[21].x],\n", "         [pose.iloc[12].x, pose.iloc[24].x, pose.iloc[26].x, pose.iloc[28].x, pose.iloc[30].x, pose.iloc[32].x, pose.iloc[28].x],\n", "         [pose.iloc[11].x, pose.iloc[23].x, pose.iloc[25].x, pose.iloc[27].x, pose.iloc[29].x, pose.iloc[31].x, pose.iloc[27].x],\n", "         [pose.iloc[24].x, pose.iloc[23].x]\n", "        ]\n", "\n", "    y = [[pose.iloc[8].y, pose.iloc[6].y, pose.iloc[5].y, pose.iloc[4].y, pose.iloc[0].y, pose.iloc[1].y, pose.iloc[2].y, pose.iloc[3].y, pose.iloc[7].y],\n", "         [pose.iloc[10].y, pose.iloc[9].y],\n", "         [pose.iloc[22].y, pose.iloc[16].y, pose.iloc[20].y, pose.iloc[18].y, pose.iloc[16].y, pose.iloc[14].y, pose.iloc[12].y,\n", "          pose.iloc[11].y, pose.iloc[13].y, pose.iloc[15].y, pose.iloc[17].y, pose.iloc[19].y, pose.iloc[15].y, pose.iloc[21].y],\n", "         [pose.iloc[12].y, pose.iloc[24].y, pose.iloc[26].y, pose.iloc[28].y, pose.iloc[30].y, pose.iloc[32].y, pose.iloc[28].y],\n", "         [pose.iloc[11].y, pose.iloc[23].y, pose.iloc[25].y, pose.iloc[27].y, pose.iloc[29].y, pose.iloc[31].y, pose.iloc[27].y],\n", "         [pose.iloc[24].y, pose.iloc[23].y]\n", "        ]\n", "    return x, y"], "metadata": {"id": "7fl1ntBNJuxP"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["def animation_frame(f):\n", "    frame = sign[sign.frame==f]\n", "    left = frame[frame.type=='left_hand']\n", "    right = frame[frame.type=='right_hand']\n", "    pose = frame[frame.type=='pose']\n", "    face = frame[frame.type=='face'][['x', 'y']].values\n", "    lx, ly = get_hand_points(left)\n", "    rx, ry = get_hand_points(right)\n", "    px, py = get_pose_points(pose)\n", "    ax.clear()\n", "    ax.plot(face[:,0], face[:,1], '.')\n", "    for i in range(len(lx)):\n", "        ax.plot(lx[i], ly[i])\n", "    for i in range(len(rx)):\n", "        ax.plot(rx[i], ry[i])\n", "    for i in range(len(px)):\n", "        ax.plot(px[i], py[i])\n", "    plt.xlim(xmin, xmax)\n", "    plt.ylim(ymin, ymax)\n", "\n", "print(f\"The sign being shown here is: {train[train.path==f'{path_to_sign}'].sign.values[0]}\")\n", "\n", "## These values set the limits on the graph to stabilize the video\n", "xmin = sign.x.min() - 0.2\n", "xmax = sign.x.max() + 0.2\n", "ymin = sign.y.min() - 0.2\n", "ymax = sign.y.max() + 0.2\n", "\n", "fig, ax = plt.subplots()\n", "l, = ax.plot([], [])\n", "animation = FuncAnimation(fig, func=animation_frame, frames=sign.frame.unique())\n", "\n", "HTML(animation.to_html5_video())"], "metadata": {"id": "OQLoBgk5KLUm", "colab": {"base_uri": "https://localhost:8080/", "height": 932}, "outputId": "42fce92a-0ee4-4f28-ab8b-e478ab176deb"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The sign being shown here is: blow\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<video width=\"640\" height=\"480\" controls autoplay loop>\n", "  <source type=\"video/mp4\" src=\"data:video/mp4;base64,AAAAIGZ0eXBNNFYgAAACAE00ViBpc29taXNvMmF2YzEAAAAIZnJlZQAAnm1tZGF0AAACrQYF//+p\n", "3EXpvebZSLeWLNgg2SPu73gyNjQgLSBjb3JlIDE2MyByMzA2MCA1ZGI2YWE2IC0gSC4yNjQvTVBF\n", "Ry00IEFWQyBjb2RlYyAtIENvcHlsZWZ0IDIwMDMtMjAyMSAtIGh0dHA6Ly93d3cudmlkZW9sYW4u\n", "b3JnL3gyNjQuaHRtbCAtIG9wdGlvbnM6IGNhYmFjPTEgcmVmPTMgZGVibG9jaz0xOjA6MCBhbmFs\n", "eXNlPTB4MzoweDExMyBtZT1oZXggc3VibWU9NyBwc3k9MSBwc3lfcmQ9MS4wMDowLjAwIG1peGVk\n", "X3JlZj0xIG1lX3JhbmdlPTE2IGNocm9tYV9tZT0xIHRyZWxsaXM9MSA4eDhkY3Q9MSBjcW09MCBk\n", "ZWFkem9uZT0yMSwxMSBmYXN0X3Bza2lwPTEgY2hyb21hX3FwX29mZnNldD0tMiB0aHJlYWRzPTMg\n", "bG9va2FoZWFkX3RocmVhZHM9MSBzbGljZWRfdGhyZWFkcz0wIG5yPTAgZGVjaW1hdGU9MSBpbnRl\n", "cmxhY2VkPTAgYmx1cmF5X2NvbXBhdD0wIGNvbnN0cmFpbmVkX2ludHJhPTAgYmZyYW1lcz0zIGJf\n", "cHlyYW1pZD0yIGJfYWRhcHQ9MSBiX2JpYXM9MCBkaXJlY3Q9MSB3ZWlnaHRiPTEgb3Blbl9nb3A9\n", "MCB3ZWlnaHRwPTIga2V5aW50PTI1MCBrZXlpbnRfbWluPTUgc2NlbmVjdXQ9NDAgaW50cmFfcmVm\n", "cmVzaD0wIHJjX2xvb2thaGVhZD00MCByYz1jcmYgbWJ0cmVlPTEgY3JmPTIzLjAgcWNvbXA9MC42\n", "MCBxcG1pbj0wIHFwbWF4PTY5IHFwc3RlcD00IGlwX3JhdGlvPTEuNDAgYXE9MToxLjAwAIAAAB5N\n", "ZYiEABT//vfHT8Cm6Plmy51FPSEwitj6SCi9WOzQMnUAAAMAAAMAAAMBFAvtFYF5kQFdeAAABkwA\n", "10K/8KYT+AiGMb1ahHd39xMd2b++iKJ05Nc9l6yPjRv411EZrwormlZQt2uwCpXRTUWAI/V9TsSJ\n", "Kf9PCF52/KNY7+4QkjEG11p/mfKt32FN9ZZ1RAAFWlQq3/8hkLuG5ULL+weVtfdBHCkbqVbZiqag\n", "F/qJ0TGkX/NVwDzQOq2OOxkXf8de2bzrC0tpj2OsmYmoBUBLK1K4k1qjpNjsf9eHUfBhcTIizBq6\n", "f1ra4w9/hxCLKj41Ka2QkQUc78mQHQNxliRhL/7vEIrSquHdCt46Py9JU8LLbBewVUR5Xdbjnn++\n", "mhYbLUW8aDe88ZOBKcLYYUdSl4rdGSWflRIbkztpph9jvDm9pNIWowNpV+710Q7a0mLMb0IjUJWL\n", "dBZXF10imA8FGTlTZwkUDatsO9krfZvSr85JLnaT2zaMLCR+ccZMNKLidlYbV/wkvexpcSMUn27r\n", "QUp80STPuq+BYVWUZ+Neier9qr/shU/rxrfHOfurvHN/2NMouk1Ph+F7jka09UWPZmEZO5zKON8q\n", "tzrxvoF8wLRk2T7OgBWM1WQza9G3WPsP09uu29foBBotHxdQOv53+Uz6Sb/SGsana9esZNou8DDZ\n", "23UbfkrUEx9kfUXS4P/69HT3pJhlgQyZmhu9iqaVUjB/PiGDDMo7FKTUuWb7cIGA+VDmTvYrpT8o\n", "3DRIYMZiJAiD5EdO/5V6vmwjpii27oxRoGUKGbow+R8qH4hC5t/hFi7W+/AeXacuNDkqamSMOf0y\n", "PP6o3HiA+LvsPqjc72rc62r2CHfn+bbMEungZj4KDa0wRfjxjhH6RVz8pq+G5Vu+lPe1Yfnpaiz1\n", "2WK/w/vWIn5/387tpI03A98I/JI8fMHPZ7ymEGGpOlERmDxcc3tJpMqKh/raxHgAXnSWPkXMlhrO\n", "JrzsBrugM9ryt1L6UkzrPks9VYdIqRY9Zb1YGrYNG1IZe3x76pf4m/4sMZ9IduiJzFmGg0tEGWjk\n", "1soTb5tGMnfcwJ7HXXUZEohHuyw3Rl9B5QT0TNb3rmEhnCiXF0UjCarWbXrHKkkb6kDMQG6XoLtT\n", "8JH9qMPffKSB5Vad+gRKWFg0o4H0I4xB7RQn0SDQqmB5+nsPBWfcZaFMYSwlRBCtvQdU8UJag4F7\n", "5gatjmAfwoV29DAK93nnGBXvEWIMIQApsVk8w1E7Kb4Z17NtuXOp+vLYSkod/vlnVT86q3CDcAm5\n", "w2PKs76/D2cCXf1KBHfP/sREavJ3yO7UHc3uGUfgltgjvopq+8EO7uHsrU28njvfaKCRKbyr/b4J\n", "gNXXdniHcJZYl5PGWTKtnt9EzYdSN9A+DYOudGY7aXnIerHaUa/ZsKSTvz3w69L/70EdbnNh7XRm\n", "UlaYPRNslHUdsmLN8b/ATyotHT7BBA/ABKY4sk2uf4cQRNE52sP1J+LpEzX0mC09kepAe0yHTfN/\n", "lZaMAVhm6M7gAKYiIx3TQVkrYaR9gnU0Tvc3a94iCX21Jh07hsa3OLwmfByCaH/+2zremDhVQcq5\n", "N9I80K36g7RYDDGE8Ie0FN4oArOwWV4DrhNKmrzABw6dIv40qeR+qEAo+nEmpTGxH6Zjy2W0dJ8s\n", "r6oDtBwUnR+zoPOK2ZCreN26vf8jeQvTjxy+AWlvue13sJb4wa7R0nFdEVdoDO/u/pLbH/5jmZC3\n", "WNubEz3rc7fLBd6u9p4kPOsK+QZmvc5uRA0XqKHxJHLBpn2gXdo8EuVVU/4quPXZtJ2lfV5BqT+y\n", "4eGgmO/ZHkEl5ROARMy4nrGiMP/gVV+Kf1Z5IfS11zghhVA+AQfNorc2kCpSHF3FjXQLiUjyBvRL\n", "cv9nMECAnJ/v69lzRkg6Pip2LDv6mS58zQn4BFu1yxagO3t4pff7Z9dsY57C0pLCXCGMFcJ2k4eJ\n", "KKoMbYCjxq/9+p/H77DdFO7NrfwYcTs8PgB0lZv+AJ915Ui/gbdTMgVwGkZbcKKK/ufooz07iXDj\n", "JPuf716l36MZ3n9FZypjXl6gi60ZATvqAPBhvMYT4jACbj2HpZrAhUlPJC3Mo4AHkDkWK8Di8iVb\n", "yLfhIYTL5pjfSfxdTUsSPetH63fqid1vXvIqc4pmF4xGAqBmQWa2aJv7FF46mwZfaV5guVyllQHG\n", "0Ry7/mVSGAOmdbN8ezRU52yqAZhtPV8+leE9Hhd3KcxUNG3um6b+iYCIsFmvbDsA0i2lKwi9u+dL\n", "Y1mEUc8zB9dtsqu4uUOFlqbACl/qDprIznIoCV+pcAf24R6xqS+BzyQNoAsMnzWfQdLe3hNjrnVr\n", "nOx3x455Mzka3GGb8TlWaw6JqR+9YQH3k77CoowXI+eUI4qFZqB8k+PR9obdUsiqwwPGhAMWs14S\n", "gsE2kknC8aEUbpUlh8i8nBex32GKIE6kqE7x2EBFeAkwlWHFb/Kelvnk4gV33QdiS1r1dC2D8yjY\n", "ecE5nJwZQwsTjU+uYtxZsrp94qJ7lWc/7+aYcr9h0k5eoiI9q9IWy3s499ybdgRZfEMqN+tfGDz0\n", "Fwn5QpFU6Sl34aEYAKCfbrl3tnbhC364775RA2yUPDhOsz0bJnri7/RXfvks8fHB4Zd5mb9DAbdM\n", "KdhARa5IsGE8WCSLQ0aOMF9gtrqt04QaMnz6USzm8ebjL0N6g1FiVYZBga/xgfz5zxWSHt4Q/cS3\n", "cHrSGveHu237Ny8iOBAP9vBGiMZauEya9ZMgFjV6lXmao0UEvq0x6wAPeapCpwOjCWHy69NZWVxP\n", "7NdI/49xCCa41BbDJB3j5FTAC1mvPYrl3BM65cMcxTneKPg8I9y82Q+iYFEf89WwMCRm9u/5Kdb9\n", "aV4w0QosKUqtC6zYRT4mOgYleUclRS4VN77U7f0JXhGNePXshUwCa7y9z2BTckct1NfqdChk8/2i\n", "IAbAPqfBZPKJ+c48gJKjTYw40lEh59UmPZUXtHNUZLb8moeBC/6CKR1QNl3UVcv++LyzFDXt4V9/\n", "JBhcNKVFv8+l3eWDM8ZhnnZlzTYbec4595z0fzz0+zSQIxscIpGZ7WKBmYX+hKGNstrX9T7HQ5YN\n", "hq2Q9dc1ghk+uciBggdDhVGcnOpB4aPGHRQVzLERZvNGYh7u686x5Wp1jx1Q7fP4n3zvOjEpd8dc\n", "rRCu/lGD2hF4yThzXd/lrsQi52EQJoumPu9gOH9SQKsb7Dlv1QFc/ltToiHC1kg0fX1eIvo6cjlU\n", "MvH9Ysoa87XuQR5S8y8Zw09JXcf0ukvwVoKWXKoJIJzROSmN6cFTA9RHhp3SmLFW0UsN7DC70f4/\n", "j18Ljnf7uKJzvrwdiMxXH2BauleqG6XjAv2ettBMc+rKPxw6N1oaVs6KQNhz7ICMhR+1cg9HeHO+\n", "9P5ng+qojUCS69Geg6zGza+g6mQQtKtixkObLCwVUG0f6fTmNfshCDC2Johzhg2F4Eb0AiGwb9b/\n", "+Mr/yv1cYV3vmB7h9TbGxfpZ5/9mL5Qh2a4OrzKS+rZgRnYYdzQZbPMJU/zQFjyigtpMja+h+mnW\n", "fJ1R3qH7B8eDjr2uk7gk197OO4K9qUk8/u11GFiP9X3DLX2zuvKf/1MG12p9r6Ks8uXq4PFTPICf\n", "aXwBXAJ1pa7m/cwX1b8OV8f4WaeUulOH5ukNULt63O8UZiPECvc5PLfmaSrNAvxIeoyn1m/vOrF2\n", "b1LEIFtgEg5lMM5rrmmozUTE6I6JbyK8BuOyZ24Sgg2bf3rTD0s1zEKWpd7vWKwjNQEj1Oo8cShw\n", "mqoyyEZp+UOm6JqrMUcPuTtFkV4MZ94cJ6WnSua0hWD5Pfa3sceilSC8hJ0ct7Rqgs0f8wTDa2L+\n", "gllaOPcy9TTFD6pkzXiY5HKGciXu8UnPQLq68RnJd0gaQKtxfwB6gHPudphUSiRC7g8qSvXWtf37\n", "fVkiqoUEZ38MG3JtgQ0pS0DEO/G6TDwY2qtNalayt0nmS0+WL53H1Gf0FHHLC1aazS+hem7tHkX0\n", "1Yghs9UD4895E7KxD032MpYj7wneq8VBeLPIqKXNKo3YNtWfzvsd9iY2jWiPS0z3eTaYX7bCpXE1\n", "R2Pq7h0YmNmdUzdMs5UTB0z06U2/wg0oHac7EUzHzIs9ZbYD+HJyLSFHPC4YwPPYAxHwV5HVwdXZ\n", "FPnkRPyw6xXckAZbo2NBwfBN8KwVYL7maGFC+SwtekQWwhKTGeN+bGHn5s41y9mjjDUaMHnYmI2G\n", "EosCusDTPaXphOQEFD6vks8YRq3gdFoVvZaH066ZXa0D14nJI+Iyaopju9UHDOdPAMenc5nqxfGT\n", "sYTKkQ5NoVDDIvuuL+1qvdSVc26bDEoPFFs/+onsQ5I4tp82Wu+NX5uJi7gqj6sQbtT4ne8WbgQH\n", "NSAQ8dKTrW7rornvIVE1LjgJeVcrlZ9oAqNgo1/E6wH2DPJ40BHe5Ym+3SYG/SHm2TYTRbSF81KD\n", "73JF3CUhoNoyWd8FwllZyKTMU+9zcO4kzkaMa9Nr3FJvPemukh/4YEasKftn0l8Cr8I7moX7qXpa\n", "MQuWLsSG5KbuQrQpirM4tk19/loDueUzUuXr+OBZkaJ0fFFwx+Js7EtW2AkAaB29a0WTwN8A5LvZ\n", "OAQW2SRGOpGcJ2NXR46nYW3k1PBMJ2EOOQOQaNNKaIGCb5xMg2tJ9/0oAMyxogwoKVN4f0sa4zrr\n", "CMGw/DMcDDo0aO5vOoeRGrikYtqnT/R4s49f4wTDuP1nrGOOastY27QndI1Xdf3/w/b064n5MUUS\n", "tU3W+tg51/py9cpV63HKVsGj2hO1OEMh6aAQYQcEHQka656OdhraY8T/rti78zW2tfxgDm5k/O9C\n", "PhV5UEFMpLkUez+vIno+NCGzy2vBfcqF4tRVLs7ACgrmE49YsLf2mkAuAqPY9duzGrwqprdz9mmT\n", "oaDQNz55kutvJi0wTJjd4cL+ehBIHl9Po0H5cIowDWRvugu9YoY2HBVJbjexzpqk9k5vIfjm6fs3\n", "b7zN9XDbCj+SyWJO7WJMCXCeH5tnXU+TzW4brdombOVB/JNtMEZmSz8B0y97a+2BG0FZHmAp/fez\n", "yN4UEJq6PmlkTJCn1bmz7pmzjS9Cz91gzlWcli53EaZr7pGsv1ndAvPVNM+7tmsYxb7y6fmAtnz/\n", "HwAelqhTDPxY9ZAuuXQyDq+H8KfKEK7oZM8AoiQ9MqIV6XkBxo4rN0ukfFSKT94qifqj/5rSRQyz\n", "srKbLTt3hqdmrtCwKHnjvtzga0NXs58WSeoZiY8Ba6ap4z6ph0g948I5P3XXkWmpnkA0c4NdA1pe\n", "KeGLasYrQ4quCVQtCid3wXj06tIb/koeDuc8qQEt91UwnnVgVxtaXlFven+wwr2UjDu2znm22pd0\n", "Aq+n3Af0gr25CgwThDR95LByHbUAxrQX0/XBAdIsYWRuA5kS15Aq7kQwP2XtNhkRJRxg2dI0FD7i\n", "wvA9GUC4CPzRSGtZq8tJ0jq2sWRnKQ54T5/+5FayTUtKXTaWeEj4aEVUy1uX/uHd9bG55DZ105QN\n", "292GdHxk5SfhjyMnxMzvGNro1OF+l95fMzMVg+bmGKJM7zoZe9ZTunfzZ0vysqA60C5k6ZFCeT+C\n", "koxy1i3G2+8cEnk9QkgxylOH4RnJPVhOARMkyGzyubBFVt7mFST9gKmmpAcBjRad/vabHLu+ZJo6\n", "jonBcYO2IYscgAvauEUc24neHy7ZtfQb+TuZMauzNkYGqrHjPfCLl8nga6ECCxdmkzYsXA3xyY6P\n", "Yd6zilhamrve7T4kIuzwpOJkbTHU4Nf0ij1uWNsPPCMz5kZamLtsksGHvKIQywHX/Asgqamb0ohD\n", "1oIRSh7mGeEtv80PL4eItvlPuolmkleonsVvN4PTHmqjBnSOX5yPkD7Jfmtg4JzJFdcNbjuatI+I\n", "rJ4fJG79mOl5K5AqCz04OmvEIKnrPUgqH/JwF4O1gKlBHTlTX3bKEGQMxhLs23EsTpy/YEW2egpT\n", "4wMw4uvrvFHJ13PXwVQlNKf01Kq/Qw3jvPvjWD2jHTakom+TgFyuqtxaErCmA84fYwVfQvm+1CZ3\n", "ktFj4oUVrgvkmA+gMmkzzwBZmBlrKdrNbnauU4E8CDg7woSwL1cvU1RJRS/JGETZjD36agKAsGAD\n", "coD/M6wz+u0XsR/SCDsglW2uVrfsEIf25EYmqLudmbzI9HykHAd4D256zxr5ZdbmLJktjgnZT/1N\n", "z3zyMed1dE9al/vOyFknnnYJ+N3Oog+rnWWzTX0v8bQh6uZjy6l9RXQe4AAXHAO/H33Th1JHnZq/\n", "/Gwz6khed2cGRRyoVbKLHOGdzlTIgylhOsq1tpAYzeFrWDpAq9d7ippcyYdsDD1BvYlPj611OkKr\n", "s0EPErCFRATlGXczgr+weTbN5OkYIuOKHHobYoshZ3pbSwNfygjhptPSgToZo8EC1V4SJtF1gawQ\n", "Ell81L1tjWGM1Y8SDZqNkVAHVvOOIBHbgjp8ayzf3jte0tWdchJgMOYrJ3Kv6S+pGcgesRNq9Ibr\n", "GvRKrARVCs37bsz22dFBbS86DZeDdNJ20a260grmVi1QcPEK8ZC8jg8PMubqSmXOiT7GrRC9Mtgb\n", "IuT3WbCDFCrGbG+KUOrqGc1lmh24VmILtBH9KiQu4bILBxaJsZ33KdEZVr+3PNqrhvz/j8RM+JcV\n", "4180fE5irli42BBlfqtRLTUxI/JIPsNYE0NjW5nC/ysYJqAAMv2znK9NxK32NkKNfh2gOw32H6jh\n", "LXPRpO6mS0zXbhhggAYlmFNOXF/ZUL5pu804UsZrBm37oRZPWXNQP3Ak49qPbGWX3uWtCcP+j2XS\n", "qkHuo6npnWsFcd6vjo56l3wXBXU8P4JUiIhB2WqLJ0LXckq0YPagqIPtrSKdE9nsjptrRcilBd88\n", "JhoOvcSv+5y8UiRn/Nc1JRwwztDRcFscCTK26o4hHNyOCGlII2ZC3PbwMDzqeOfawE2PLON8celG\n", "90aXaehpEYMS/ABL8hKotbHSCJdBRBDqdyFSqeEF/iTDDA/h7Y4BYfzOcwvvxKW4mm6sygjrVF0k\n", "xBeJw9HWSdF43tUKVUc6Al29UuGmWeWEk9LqFIHSmz5RJtzH3FiIbFEFX19aLkKBhgux9ToQWsAC\n", "vQZExYedMPDHcMNaa0ogCZR3qe3iSK9PUPkvr60MGLWEpohrHAn01qNoQVqwJozwkJZ9rF9L6UMb\n", "g5KzprFIO+1p1j+DKbmGqWulcbRfqBXZMufUixoNw00/PE3CNY9K+c5f92wP4I0TyFzffqu/b90v\n", "2rJl4fr9tMZzrbleQvmEU7z8ymwDJo1FSArVmOQN5NJyzWmBER41fnVyuzJxmzdluV7tbD/df3mR\n", "r/MsImgqemE9p0LQh4ETz1dGnAbz6NeKxtkcwRgcWfU6gaj6bvZ8zCJ3oBP7epevDWcO7tLHQiup\n", "u8cWHgIwmbBttIEPkSRCvEHV0+wcCmAfW8Hr7v2uLi4QQvnK7HkPer2x3F+NeNswrAzEhCG02gyQ\n", "toTx2w+OowVZIFIyMuNT/fcbSwWoVdlvvu++kGMbUmPL0kHaXz1izNKLudrlRjJwaXnv/h0MR5H7\n", "eR/SXa0d4dPBZaThvT+3l7lD+BN8/Vm6xZYR4hKBCNjHYl8fu2lBCpCT3Wt8qxYYYzWfQSahM0rF\n", "wlPdKeJW4OOSgiqz32qXKp9l6LKqLFT4OT6ONUTPYGd4FJWmLA+NiIUEr8Chbb6O8V4uzVfpJ6t8\n", "XmrCuumN+KgDSqO4AB3yMkAAAGVnl7ohphioP/ZEGFq5ruNP375xKgERcBTRD5y4wUZfsRvrbgcJ\n", "i3EjIALVsDvQjRoloMZejrqQBbQC1NUMwIvOcZ7vbQOyHJAI1V8kckYGxBHmjf5IGaVu8KDHgTRt\n", "ZDvARu3oFL3QlEQHtbr1STVtLZIGHuVxSCk3j3z+TeOw8FxrBy+LFYjBVqh9D9tEexbh2o+cDPsd\n", "sL+6mkVofU9x2PCvOQgF2Z0/btUnGxetow5uDWbbnZV+S0+nd4GCJQwVu/sgcBqheawJVfcSu4cB\n", "iL7RKBibhnJXOwipszkk7XpNeWibnaX4P9lkJUUXTVl//5dN2AAG/AfyIf7KUI7KoTjlKzY9hU6s\n", "5Qq7MA1K+XzXDRL5hCTj0dc0Sn4QJIP+wnQ4sdMjA58UQCYmt4TFVox9tDYnIHuCZdXnu+VJofyg\n", "dVuUsJ2RQpeaixl+FjObhLy7/Nl6pnR7RdUFi1EJXX4A8Tlfi2t0fI+oUJUSlQ0BAC0Ru8/kDPUj\n", "JMO/KgJ5V7TWZKuJCfryud3sLmKaUbjykV+h8pfNGHGzleCxAofQHsjoMh8kwWFCyVQRRT2W23HK\n", "AVEaon2sx7oZ6tY0JNmOYrTjGK47PHnUzswi+hMBBFnG16q3s1pBACItpUaYTGcvyc5xITOPCeO8\n", "277AVPu6SuCau6e/9xB9PSl4qjPvPKO2E+D2249DVnvMRBxNgzl2RPSIKwzuJCdCHTsX6h/1YKDc\n", "2JwvTUE9iANaZjR4J7xuVobc1Gh2Bc6Z014Tmqm6CpP5NgJw55TwTlNuJ6UREPuFiSFgpExCnVtF\n", "PaAXiEAmN78dDpAlOvF6AgwFimJOfzqWEJibDIbdtJBEoBp2dqEhwSayRYqeMqGtXXMkjfVF/rUz\n", "L3UGEuHNe2ykWS8jr0757YrlncuBLH0+IiuZFar/XmaOh6MzD8eVqoCBE4dR83zm+zzFLdc9MrCy\n", "N3UovpNLBU2MoG7VlYBG2RYHzfiH/KxvM7R6dqsAaH/iX8oPokLtBuMjT3z6PB+Gk1TyRBZdrEHw\n", "isnmrPTFrAaQdGN5ixRemDoWqacuNKcbgRBLfyxfzWuZscBGKAIp7Kv7v4eNNJBkiz6ia5w6WdAw\n", "jDG7b00r0giF9DXO9ZQvomfxcPn6SCaBBS1xbH2FI8z4J8jRcPn65KkeS+LMJqF7zsIbz6fBP1o/\n", "RI+Arr4owuxDDBAhO65SgrHLM5p561lsJguEcp8lhSuabax0cQU6X+VmU74qweD+P9J3p62NmV59\n", "Johpe7K6D8QYiyef/nzLwNqUccPpKoqjwkexB9rImWWcHbEaaoOtODAwgbC3lMH+GIf7x4uzpDwC\n", "AIvDn7F7izdTnJ73CWQJchcQ9l//Qkku9eW9j5ijLaDPRMENfryueFGvu5ls511mv7vK1Dufq9LV\n", "xhcNppcT1Qhh2m6NbkR8TeKJel8cOGjqU7QSGcBwA7echfxFPudIpARkFo/+ckXKL4VF2UPNxngR\n", "2j35TRiRwe+FQ/wnMc9GKOeC2SHI+pHGMgcglWNViqxHMWQDQxUiQQ0RKI4EhkXMHMsTH7A+VAg9\n", "BV55VGoAkp9OqBs98eg6JfD5zHvKw/RDrJbcCKZwjzfe1k3rIhAVGagxNv+E6q3Z4jTxsx/J/udZ\n", "H92aTOutDKVKHxhjItIkTmCYeBGaTcITy6SiBnzjBsLCKWHUF6SdK0KCKeAc8F/FrDppvwf2yGaK\n", "1qC1nrzfitV1PXOAyaMYRmUxCY060H91vJjuc9VfDq5HF6Yl1nvfMgRw42gaeoWc2Xh6a9JlzmkL\n", "VINyS+iaTt5MRC92rWIa/IG7KCqX1Q4Oi5N0Ke0v075pvwQ+2cXpAeLNRrjqIyG31Z0qlAuOKmMd\n", "AiBBGKX5tqpIqxS+dkWcuQFU1Tiq+E4nhshShVk0Juwbrjza3/DD2sANVQhEBXxTKVLA2T/xZlTX\n", "tJW6kuDLUQzAHR6Uu/i58ZmWCxQ/Xkr1knXoGTV9Qn0mNijNl4h4XR0T9rzDTOAlRblXsV5+M/qn\n", "0RyJVmwmmHf5vcNUKsPg3romeGUAXn9NBZGdv8YdCIf0zJ6dTfAf70Qfet29ZsFNpExF3ze8SKbX\n", "YuWU2O3LpybrfosK5CRVEWNT6aGEqgueGOCYWYg1PWMd9Y5Dd5ipa8gy0rDd/OisEIheVUsgHpd/\n", "8AVd/u4ARdRhVhKxH0ft3Duy31BBLMNcjdjSmLOtFSmMipRRmbTnsbtM6+cP6sZfTbcnH/OmYy81\n", "o7W4VTCNcL3b1rkFVO/kQ+R9WfB7yBNQsL/GUkb3bQ7V3TZqkTesA0z+fXyeV8UYFYngQ/RCPeYf\n", "D/TD8ReX6Ds9UzEhiSGMQTyr+MZ47gWi7V2ANWsVB2vzL20YrWrCyZv5rkmAmG4q4zrC9X3GtmR9\n", "fTmofIgd847TPy0vlmUGF3ZePEoL58O0ioE2xduNn55GXWNf9Y2kPghuC+rtXPE6+GZHWlOWYsS9\n", "flh5fIzAkWNWXvUKLmjQasNsa6kInYa0nuVPk9j6XUkDQRZRQhZE0GDUaMDE+cB0wQcqfEPnkx22\n", "FgAAGpEAAAwlQZokbEE//rUqgAB6Fyrd/IARMRqjGOJMBNa5XlJphmJ8JDRgEVM/HaqiLEhw/ATl\n", "7C+79nTwbQ5OyqRhm3VSCeaRYAkFGSXiQVyN7scdL+kpa/3aKtxT6Pb/dS/qeKRK11PQANvh3pNF\n", "dvoVi3Uvyj06R4EuGj31I+eytovm9rnTB0LWwisCFisoWkWJZPctlisbWMnU0e6fSCTpQbJ51SOp\n", "1+7A/6q+Ml4ZFQmSKFgIXxfZKQvLHfQlOusyudXY/qbSoANBFTP9dDReQAdygelcgb6Sbiq9VYFk\n", "PuknK1N5mOv/cudHK+6mf/pu2Vc7oB+y1OV50Rr0FhM1+BnkIotXA39IMdQBPGLp3WDb0O8pz8/N\n", "H3ZuFMQhdLvM08fdFLdgz8Q4u5M/QDii3qwnAy7ELizUx3S9OGXgvfCPiI9iZ3aj6Ybv+HDLxvsO\n", "kld8lSQfaM9AIl6ZiX8WDKcPXfgZj5dSkgcNwSPmjfaAmigiRpo347fYXiSr6RJ126RQmNi13qUD\n", "puZwsOwfseuGRzts9NuHinSjbnyyz0IfJ6CZFAmNX1RFba+H0Lux+4puLbmWbwq1BmLKNpyIGVOz\n", "2XBT27GlejVOk6X4AtnpvcI84rGqvdivKTBYuZodT6i8dTycVwR72WSh6L2ZonAxLM1T0QwhVKUM\n", "VZV5QSQvar4b8eYTHMCoTpfDehIfQyZRSr6iWGNUGvodRhpWKNkVoQxnxa8qSkPsgiVoXvsVIkUC\n", "mvAExpcHGDsidfu+8tob3QW3IyE4puUP6cGXqLlMAwI3eaBfzjIs8yowrApqtYI42X45lG886J1A\n", "4A1V2KBS/zFZjfWS95AExV6GFc06AB1iMIlFGNEHsNebSMpVbqkoObDj7cL7NJ2uy4b2S3SHYNRj\n", "Ms/00axi58q9pXd4DjQFzl8LDVrQ+XZL8d6ZA3+V8T49sm4FF//+Lq40PxlBrYpZz1OuLQvuuOuZ\n", "JgRQc72pBX/Inl4cdVU01ymUjuGnYBktyVjV+5DmwP7QYFyAJsoCwZF5ndK79p6GNEe2sGjkr/G1\n", "oLWk1yymdn0AqQkpigWpHbA0FoLsMBdV0osquEJUwPXhB3OJfHvHgLDqcULlY0nfLOPXr6hqyxQx\n", "4VjEhwc8OELyde56il59uAil7Qh7YlEBwVpGwKp4404/eUUYEJpz06xR8FFNfwTapyqowVItpwhI\n", "q0lOQTHQhTdBhxpxgIcMmFA298e2BVGmrpxA4+JP4GEB6vzwcRGIxifpBtaO34yPf6aXFZr+QiRv\n", "Jw41SrhRz+ajRzzcAnFtPtH5i9CHt4qvxWYzasMgFRSOYpcKUN2RFPQ0YTBHXdpe7NsvMOhfK6WW\n", "VPDiagTEadzRwTpq/x9m2mcnpiL7Git6ARvgKygdX3iZVNpDAgj/W5jYY2T+riNXSu2cvB3awXBF\n", "z3pGsoL5H9jUz/RAyR+jSuE6yaauy+RTrqC8qEUwPfMNq6ilm5wzMZYUH1YsL/VKnnoAea8Wmp6a\n", "9OuI0BbEI2DMclZ2HZ+hPcrCuWhq+CdqwfL7osOA8ujnQ4a5NDT3kLhT3T0mdDooua8TWvu9GHtr\n", "4Iz5+c/ffKCmhuu87gd4bqHr/yu0G2jz73f7ka8M+duXWvBPJSAbknmz/n2xKatKZLF9gWYCaDcU\n", "T3P1y2/RS+KzJeeLOnOOFKAdgunJT1GlD4SR04C+qgqGk1vs8vtLsh26uzhj0Jg8iVxSErRMx5GT\n", "GWd7W0g3m4P5yCEbO1ga28Yzq3ePLijd7iHUZC7ZFv19cd9mbZFmxzhTmDIKRqcNi2JbZLpSjBTr\n", "nQW5xNhqqzc4nRJ/+y2GjGj+jvIvmLHuK8LHwi8v84jY5Wv4NTBEtF3zeV4awcYIjXxnJRQVnZln\n", "j09BB3tt/gIZX8we2xQDMk8/446G03buJSxgFnPCCevR0qIgemp9xHwz5rwsebeQSRWDRzunAtim\n", "KOQVvBL5UgKVYUIB+OFPmtY/RNukCRA/DY+cIcyvFM1Aasyd2w1sT0SQgFk4lc/sIiufkSasj5bO\n", "0ZWMizg+SrarsctG9tXZbkrFOpTSSEbbeIOFRZeWvfHa9HL1kteDHUiyd5/Q79YXusTYrCuIWoxe\n", "kDd10QpQS3JNQQRpJ/0JC26+7L6nlr5+SKr0c2wpF40b6HrTKmKLR+ru4Cp6yGXvswq1xJpr5HMM\n", "7wp57+eJd3Wj65YP6zZ2GPjCcEN19cSFLuhzMGmkOq8RzMVME7ecGK3/nQD47RdyhCREAVpXhCM3\n", "3A0AHQAFxiEMPXdGhVy14khnH+BlTTWYDuCMJD6tIIkeBzTZJ/5Kj/1LQwGOYa9t+Oa0RbjYf2PX\n", "kI2LES9YSVjgUR8+ehDxGSO9oevpM4rbJZnuYT4Vowjc+rHTS9tBmrv6V3KVtwP9F5AylHv8lhyh\n", "VyhfPd+xmJ5vG2ZwS5+TCFbpV1NxNMvjHyOpJz1YnqdkMgxLRkziugRNo6MHOjSxcYgGlbfCcimD\n", "6rlqTyPQxHmpiC/FIYCfRhuuzRWO/4rDVcwAOHJVxhutjibTs477xf+puRT5Fmv574eehRzQPKyt\n", "86nVRMqSZDp6dBjplNsGqQLyE5sYZkxQDlFWAsUDzZ9NtHrY/YLe5+TvIjqSCYnMgUbmT9rxjEH+\n", "7rC3P28KUqWLfzk1c/4k+vlogPdXZPF6g44kHlhoFsCp1Fvj+EFdX8bXEvR/NbKn6DiFqftKxyn1\n", "46AYHMl9wGRaK7FgqZjEMSggfBLppyUyYiju4+HD+FzDL2pDwZviLISLLfmzNdOvKWO/NP0N5JNn\n", "85IHQTiokRM5L5azF2BhuDVv181Ilq2GOo3cyBYN5WBsaL1mVUjBnJ3ML2DXJBgiLE27aWSCDNjv\n", "uIQygKbmaBUUQ3xY4ymWtJxlA1HT4EerPQw95JPyrlXNK50yaqERYK+7OGcaZqj0k4yua05ZDAiU\n", "aDEvFEdcsaDnuDmThK2g2jzjRzSObAYz39XBLpdVzuk5JhSCK6Aafr5a7vZKt39K4QDS3GlCqEcP\n", "RJv/jdGuc6s69t62GyT30YXqxwYudTh820yiCjpNh3ZdKokgAStmc+22NlySM7NuPlH+xNNWfobn\n", "L78Z/c7ODqr95Z4rT5/y1r05h0eN0S2w/I9T9ksv0dihqsexZPia2H3mLU4VzZiPPnwydaE8CFn8\n", "tMfEgPhQ2nN88wAtYwn/KpTmXaLKeBz9jnDE5LIewcVJvztcQVfKK9z6DIBEcxDMzZeDXRuXzH30\n", "deNz9SA8f3vVhk+wusa48YcyIXcufsNBCc6U4+mqPZzYsN+n1Iqu1z/SY474Qp8ulmUlP4aFlu/f\n", "FOwR+fDH9LJecvFskBlp4tXkXQbCEmwUYqkCewsKexETGMP6e5I8fcQuf4X88rK2JS8VBsjqXUYo\n", "hI/47M42c76/96SNR7PnNGuZIcVDoD4O7hiO2gcd8bCq3cy9FUCtxazMvVXVJJcg14Ibb2GPnO+N\n", "PQnO73R5/JeHKYm2nCKwAZYFyWHWsXvIqQ1121yxu3WdlpsVx63Z2szisaHd3s80aWK78HweDVRF\n", "8QjjrYGr12Yw1uQfmuSSPthcPwZ0Djkhnoa4WGy/WcEOgCsXLtsxBVnj21878vy2YLx1O7l2Kdcf\n", "cxzgFxz9PIm3haFC31OHMr0sXZkIH5oirrPfp8ltAI1Io2RxrwvBxOxCa0Ser1AFgxr3T6tQZ1kP\n", "V9C3qfObtMgYUIRXUTKhsyrA3T+D/KHLJNQYTsntGVT1IWFwXT5Jvio3fraFbt7dphTW2/P+3z5d\n", "V1t5iRXG17dWP1Qlak2Um4qIEBKG5IZHgZws36aq6n+KjOObCJzQ59G2tDcTBC09g3naVSjAO6Pv\n", "ivP42sW7sRjevMWvpP2wGAbbjkEmnK1XJ3VIIcIgyo6HE5hnm4nwxVIxmqiRkGduI6Ruj5PcE+iq\n", "WVLeCpimdB/XHOKiY38vhANXxZ39LyWW03WFx+YG63DkBVNhNNSHxCZy9LCvn1tIW5qEp18CrSYL\n", "EBmFaR2CsASDK2gMzhdkBPuawTrJ+DmwaGV+ig58bgOdy/8vZCUO/3mlhfWEPmhIwIDXlvbIwazy\n", "6RBMg5xkPQkkry79izktDzWSKR0Qz8jycX9uMYBeP6Vjk+X0c4DWgAAABldBnkJ4gh8AAJpreZnH\n", "tREyf+ADwxMUanVconaizg6AvTfrjZ8BeyWNmqR8PqKoVGzpJ2ZkTK8QOKZZWtIHCBu+48eXfs3N\n", "dX7Ye0m3mrYp+TrIEvsw2mo/PZnxAt9SwGjf+97KiWFIkc4FPAyUR3nQMzZGECCobWWImXE7t4r4\n", "skg9aVe4g6PeaDFUfTXmau5/aVXmeLcO6ZrHizIf4aBEFImLkS+0cgBiXug4y+s64j5HU70hV8cV\n", "Vnf4ZUu3irrozWh2e/Fj9/a53r6IIiaZ7cmB4+ZnAdcE3YECqTtZobFSpXWZWHCotohvbAIJ4RtB\n", "G+thy3mnejf6fjM2H4w+RSvq0l5VHCNfmMJBds3KuK5hN35Z5uqmlVGF2W8VtM4wyoMBt9p52GB2\n", "JM8gjZpIwTu315oJvUh2ErGoBto27XVl6Rct86sDcjxPHz7TP8XprXcQX2U2OVm6KU9NZHhxtjAP\n", "YX2V93tThRBV8IFaCpIJaqyV73acNyjPJmBAgaOrU6LDddLNt+fnbI0T8tLt71bs5zUV4PO4alfB\n", "1KMMd+PVEmZWWjiBL8FfXusVKeMjY3ppiGBLxxs//44CCaQfP0WVdF+I00XvYjIHNUetwpu77j0a\n", "LV4GrnPgdgkDb7LuXgajwck5jEaP844RziVKx80ZKVDZSYQmrlEhNGW7wbTYqCLsC/Uhdjxpt+Iu\n", "jdpgIdVoYF+dDlNYIO9+Y0suqZoPvwTDplAObgPcEz3wouhdtp1H9y8QuAS0LbBHOhQB7WL3x1NH\n", "QO1ImQKF2XnxuoYi2txA9inU1HUUBZ/ms5mYoD4QWCdUMFmn4APg9TUGYxpM2hXdrqd36J6OLPp7\n", "/ffrVyxJZEIm0DFsHXc03h/9X053ot54xO3N73a2PRe5vB/d/53rjWbrC8m3u0BSXouPBD0WQZCq\n", "xbOlyaEc7N5/Hqs3ajepzO4JExK48QVOSttKIUAJArGVOiBGX5YgMHEXo30vfJnwkiGfeV6x1zH3\n", "zpDjmzMTSy3eZ0X99khG7Oe7y5AI2gp91+i4j/f3EV55OxEWSuQz5X3NxjBykGx1uEHz2UMaJPww\n", "ud+WKjJTjyeH7ir5DnIanRSjWhm0Gtt7aGW2etolTquej/kZQFnM44JPLvCETwTfVtLluZwj624c\n", "1m6jJUufGkErqV9D92my20Od/5fihEk5vtv3Gb1LtYIOd/zadC5HZwgRdGhycurZfbYcaHgy7oC5\n", "H3fnczD3IZoZ5dB777jgqIajD+wi6fDXW/cCFRN5ZZAjCZZOQuJvTMMLobh2pIvXsoHeeNzpNEkp\n", "Rnj9qdmNExGa7wZ8c6JRBpLP0kU0YYFGLgek6dC7Yf3fPjh4ONB5c7/6IJwWFhZSlWY7WalBFzUn\n", "dflqmb7q+JI3wX/FShmb+VJ39CrIVsW7SITeV7RuHmqWJkeM7Wg7Mb0E7aHoKU+GpXaBV3TFFnby\n", "lL5dUWeKaUTFzDBe+lxtC38uEFr/KhY+xi6MH0T/hObA0QuYHrB+gwXggo6ALDAEqL92yv+rTM9z\n", "ZfTKbAFYsj5NMBLpBZHEb0h5RMNUuEuts0C8cGAJIkNUPuF+7DbJH/Ypw2JChM1emxCFvSezj6DA\n", "2VvCZLM4onXqmx0tmLQhyiOuoSZNRLy57XYWLDEVHyeMiicC3oQoa/Kz9R/srF0oiBg93ChFctI/\n", "2JpeZt1vzSyPLKchj4TWNF1FL+d3tBiGRQTytbB3037IVDWYJHhS8OtzGK3+QTfJpPzlGHnYoO9q\n", "UoA7nQPIP4STmeo1f/4BdCvGIrId+pWzkhGC9hG3E+H4aMMxlhDitKAupSNqCBvwHy59Y5hO/Hce\n", "/B1Zc0998N4g+YZeBqmhvjpYny9aG7M1bczzGegFOoiv2OEa3O6sPdLyX/sfE2XstPGiRwnnHUfW\n", "SQQwsBlERVoXhnVZ2VGxjE8uttF5wETAo4RJ/gS2BR4wLgI0mVV4wDLBKC7+BRT8/6o5HHqy0xYo\n", "B10wzJtjZx8zJLdJzvED3gjv9A2a4IJFF2DkmiYGT4vkmYvAAMjS0GrLhkkF+Vogrko1bvaz/yY2\n", "QonRxt6Fkd7yqJ05WUMuH4hI/FNQZdGbA/MmA1G/mFPByNL14NMPLAnuC/kGOsbDmuo+hLv5T4e9\n", "Z0hQbWthBuRY9c+S9mEAAAVVAZ5hdEP/AAFWolo84hvAB5rMTVLJxIfe5KYN3AhcT9zPZE/3wyFK\n", "+7jKnu5WlY5ESB9xgtdCTSsBRao7hO6PSgKwg3ifltKzF/TvE2qLb8HNfahyGna0F9raS4npgacV\n", "lJlOcWieRSQE5+4ZmyirwwJiCubMJGRHbwMP44qYufkTdrI9g1PSMOO5P4r/JLLOf/yu4j8diLI4\n", "X2u+NoPBgu5R+qx+qsyQ5Y9sBQ6suihln6LrGmifkOtyKYQ2QRhaWntHKkMjZscHUFHI4r3LTJl1\n", "zq3MPnP3ziTgN1HyT+nNlWkZD9C1x9ls1/nQPtoIAfuCrmd579p7vJWt1qWXEJwlQ8tEM4v+Deog\n", "Tx1dbB0EKHfR5pqY0wyNlJWKEADFl+vJmSq48SDny0I02dXMtS9i4EqFdBKbHOrlEL38D80P9nRW\n", "4tV1SOOGX7YmY4vowiwzL4FQuJh5EYKZnVnAcfUNHUJp56+xQ5n1qBkm40BUE8vCy5wNGXn3xpDr\n", "YRuvNdv+N5ftpDPn33Dy4pxszPwEjl9fcDmtbnLD63ax4gz0ZoRNiJ+LnVZBjUPJl6sqLrk1Q4bB\n", "jK6TLaAJ7qtsUha9P8MJK0xbWzO+dYks+Lqp3wp3i0SMNgLqrV2biM9/XGwgxiIkdr5vzGzYjwzd\n", "70UvQBqHc+UJmfzZlM+LHi2HYDOw+Sn9ZKVlCKN+kKKdXuM1XAAx+vCqIC/W2O7zkb+cvn/gvEDU\n", "meWAP4VBGkBr5lOo8ffDjqHGECg4sxGj6FXGZgWgRRz4Br8G0EDytktgSAz2anxUypjW402xNrrv\n", "Cc4ar09gZX+M+ha25AGY2Td81Rwu3wy+QJSA/2m8YLgevTTSUR2OYNzwfCNNCkh8G6GGtvcuRvOt\n", "piFMEWUbEy1YdP02h4xBY3T42zz9s++wcri7rDL3oyFdDbCy4xsbvXs8Cq8VBRItEUDyhDKDTuka\n", "V18uQNT1BoanumQC3Iju0k44kxszdCoh91U2b4IiX1c9O7EEQfx5iQ7rAnhvoy9xoauZbA0kkXPx\n", "x+taboOTdfBCKsMN5G4RgNDJlN46dmCnDjBp8Wg34+QY6G/105rJp425PFuTZeuxlT3K7rh7XmQH\n", "tpuXOg5rqDzgGJiBOtd1P5IN1LsqmAKUl1JKMWZPI/zyYFdRJuhMR2XkC94ww/AI0aL5w0u+d0Vz\n", "MintQhyvXSrZkSm5B2L7eAuwvYT1/TXzko+whf8xLaBX6pUZpRqCNL8LOdbfxLN/v/9I/dG//lUG\n", "rSvyQfujWzHvC3/xvavHoNYKE77G+e42u1JsM60jSGkpqS8CS1nVfOoleT/l54lpkgYKAUTR5/tp\n", "7YifRUasvoA63C3jZHHl6oVUoHBnX0ZFoNMZa2y1QZGMnThIhytW2Hukl+EB8okbJCC8+G6xQA50\n", "2jZrCHKhowY4gNPEXW0UZ7gJ8zFmwGNveUVlk55b/+jf5TJg8ti+0/Ovorzwy25y7cM1FEfxVuqP\n", "+7ccdwUpUwDLYWHP/vUExRSd8Pk9hGlX1ajP97RIYn9VCtxzQ8ySgB0u7zF6njujWbsenQj3Qymc\n", "FO9j76rRLJY7IbdEekual2xaSjifCgr1SOV8wB+4QUnSHPiMdFL6XADy2AiCHBfMPyxjXRSkIOIn\n", "spzj8qXFBItQ/x/b1UU89+cLg+umJKjAiItT1B6H28+NrwOXY4LwfAAF5ruoFFPTx3LObL2GAAfm\n", "qS/G/3zAA5WCkGoo7dBZ96VAr+D1mi0WzKf1V8rhz2qLPRFOeqy1ItjiULoEbOhqj5EvfnWPdcu/\n", "7f2tYPWXX7lAuiJT+YdyAAAEVgGeY2pD/wABVQMp/oAPzVubcyaTfWCfzh841+uxTYpJznFCtxuz\n", "XfhKiX/WqU8pceabTtp9mpBZ82k1d66MBnf41AwmJbw1jPZxchxfVF5R1L01yivU3MBDy2UEBMqq\n", "BwmFFJOYO55R443fqr4hcu4wkTX5nC+4/oSz5yvcUoxMTB40Rof265NDzx8lDCN2BYGsOgr9oGCu\n", "YgAOyzvjgbl8ciWSG9eWegTF8+DUWUEuDrlYZT1Yf9pL5qIbstmX7ARS+bwRKatqIdsWKr22NFoR\n", "KJnwknkQc1x38BP/Jx35zl1WejdvFaUxUdAxk92r+FQ/xMdSAQLo95QKfFiUB6zZxcB/OIrltPZn\n", "JTr1vjFzRwlwi9jBwbgSc7O0IxyAEgZb4TOhC1B380h9UZwPUnAcFHOu9E4grglPW9GNXAVW75o2\n", "xjTJsh83QH4kQsNAwVxlVY0yZUpWGiEEImLHYR9vbGiDq9/7U1jCj+s/TxaQj08liJl8LNSrMT7m\n", "BOwpxBj8dQMUFrI2/occqX5B+lBCMmRGosVJda7PEu9I7e/C/eLtl1Rz6e4W0mV+3obz8Tyx7nLg\n", "/+F7fF+SewEIOWNtbJrayWUHtvBsesUFOGiuMsuplNpklxA5aPpDxcSPjQxzQuood9/p1TsrYoFH\n", "lK43wK6SPWWHbNKgeoDo/6I9V+qln6s6ZrQA+IPmRl9T/syfY6DoqhrDi/A7pNTJo2z15AAi52g7\n", "jRtNLmqoMs39sTxGxaKBjgrxVy4nK7mR/HRvlJ7jVhbBXuroPqQeW8PBNFor0vNMhCgF/xXs5N+5\n", "hd/7wsp1N4KKVireS9Fp3p+QtzHE0mgmOWEqZmxZATI3NFgDQgyPF8TrrtKnqdZoXEaxjbtkO80a\n", "Y4Jp/FqXXYxi457Byo4l4nEzF702CZfqpVwyliK69bdmgXwJKPLSFvenrBrmEM1Dcysto9o12ybu\n", "N4ywUpLaNb0q0WREBiaPNwXT7MBCXyCNkpFWAm4ICOv9Gpzu+FoZTcnoboR2DHtcIumlq8+gTnjI\n", "Ar1yobdQGiIqiOqiZS5ziMYKkLjUSpdoARzz9Ao0+7lZGJ8uVOYtNOX+f7KHfUtleuUToI+VL2nA\n", "6rTsmiTW19DvTgnQyx/4J6w19eIG1vfnLFq1klv4H8JGsf8GbKEiLvy6WTd+w55CiQtIjTZ5med9\n", "+gIXVGVNbftGUgbU/2tYaBdVdj11CBY6ku8dTCoTFF+5AZlHn6v2wHxX3LgzbsszIOIoo2RYQy5c\n", "JPXka5nd1JeN10VE+w/9MQ6TsIRviycMg8lXGOL4Qc/SrXhyvhdBdc2kUbnrUR10BTHRpX7Fxe5q\n", "sB+D2/z1qpGBWHP5INy0qqTo5eqHG8JvBemkFC79QHuPezBv2QF7E5dSHWADIAc+LUfhssoa+jSB\n", "YN23tzYjGRSQbzpj4YMZuwb0k43knbj35MMrC3l0sYws8zi42D0bmkmIVbA2YQAADaRBmmhJqEFo\n", "mUwIJ//+tSqAAHodnmUSIAUPR+rnwqSWz4idDSydxMy5WEcivCweSIdLdRPkw4yLNeqROwanBgMs\n", "zHDZcZGe4wAvWPHSqLliaNnHOCYQk5LYITZQIdpPaKQj0yyzTmAG22XxPfSe1SzTTQvGXFIMs4b2\n", "nnk4Y/qz4GH2G6TO9vhBZftpNIDO+gDRS94bOStrJTpa9GFXTvzY+F1vW8ivZz6XvkzxjljSODuD\n", "XeRQMGBIGlNo/wzongemWaA7Gs2Udhf+YFKKGrlf36RYH6GXOz2vtsRJ3dDKsF3ozXipP6O0T99n\n", "kkc/J9eb/vQ2L6pk+yRl2TgPHor4nCOsmZLgkLHXgc6OoN+Icmo9Zu8fe79qDaQ2gTHFhmj4RMI7\n", "kdNXkpTo2/iX53Hqflxa7Z/hg9SykqQDddZQGvXA4evkeS64KYl43cQ4xQGtj8d+mBAFxWuWS6wQ\n", "CU5TlofLX2516gG022gxeZablQtMUOKZsNbF63yNI5rthdG5Aw7ymETsTwf8Z5fstAtc4DXIPsDs\n", "EE96trux1cg65Ai+Jks0MGH2Ddx2j5dwug5vWOQqMJTHsUtcPv2uU+PKR4LktEQVhnf3oGng3QZ9\n", "Hq1wFLXbkYcyikSQkB94tUScCwh00Ns0ZbXmy6modHLa01WwoQe5z51stgsyHxxe1HHmIn03mzfc\n", "27Gsu0NbNwUcYBwYxbsNCyy+JabWwlvFQ0BxSeADPPgThaCtGd6iuewT6/JUceMYeHyyk7HQe2wT\n", "3yvSspuStFWnEgFnYVoIgWkmtcJtO0cGHk2j+XOwrYL5MplSbihckMd0Xb6F0DFjbCbwaPhhb3Tf\n", "D5DuxXKchBlN1Fk33+rVjP3ZSdo7pje1E55RqDVBOhLI7EI73mGA4H6ThaLZpS5IEavaBvXS9mLj\n", "btayUWChDUmCQUmh7YIfVh9ay4QggPIY0JFoQcZWtKjl+1GzKo/vclgrUNHLWcaWpk9hwb0IZ68Q\n", "94IPKm0uRaqw1jC/RC21t7QpmuiLmbqlv4FVi8ap/YqKBR5NiOMSREj9oKXwqNHVJ8BoYXXLkIkd\n", "KBdA2F3lZf03TbfM2VDNMFKtkzuPdE8EtJHb4bWc1I3OOuC5zEENa5bMSuLbZZhN9NuyEHXAvOla\n", "WP2YYbroGBLFgbBYPyX9kqFTZLUri8b9ImZ5m4LawIKikQ3Zw9xdnozzQWh7KcSTCuDdI6UvYx97\n", "LXSXPMSznHbobQjhCLnckP5CXkq7Ji0VMFFmfNV4pJAl8eARqbeX/7/3vL0qbcl63EnVG2V34omQ\n", "UXfCyaodAthFFU8C/rGhCtk9ahOGYyko43J7h+RzP6q4RWnCVtqCtCEtibWxsk9mgW5KqiGsRBi8\n", "nqnLbp8cMfOhWyNjetsIKazJ7VYeR/nD+q90bWbLaeCbcXK2UsLPVTtqaHpYOurgGEsyQXwDn+Pr\n", "qkExA1B+GIpWsTRKMDdwXalpaE0CPegkTooGz5jTWvcl/MIJfdK76tz6qexOewcVkuqMv1ax8GJG\n", "zaJqxGJoL3sKJGW9QusMQPQQ1J5Tqc6dp2C3rWxYyJPuvcgddpLQHbLPIC37GfH9Ud1GjZqeGLsW\n", "5TICCqP1nA+EElj9Qih5+xTbgjN+pg5icLWxi2ksEezX6ACynvD+0jdygLBPzmvN4TvsGVUepYBo\n", "JWBXa7L6/CNoPkwtK7CwnCcFtRx/DDCImD6U5/JZiE9uwYhJgUL/aOSDHB52fAooE97skNO/kuRk\n", "1r7YPgr/ZqxgCRTmTTo5rT9LNtAPXBk3QFsdJQm/spzKzGzsALnfiYMRtpHZya30ba5exbAA0Dfc\n", "6MoyN/JsrpuOtaM4vpCmVQtgCuwdbemk4vEbepKPpuUt63udfR1c5YptsmCnfvNWNSjx7DhVlgz5\n", "yDSwgdFgC3mBX12bRt7zdRLB9m85a3khBWqy47KzpE/KIMX6AcI+GhgKU9MlsXHdCV8OgWBcvVIH\n", "KZ7bQwgV4+Lp4Y6JZ7u/jugscMCr9jLopoEio/kpC9zSooMdryijxWviKJSkpr1bU66kzva48KUG\n", "P1z/hP0EXLAb7U+USGS2CIIq9g8UhNxPp/vH3l/9zT3bn3eQ5dlsyg0KrOtgb3dkA/kPpyTkzZxf\n", "i6SLOeIyRxVsNplvrPplZDwXg6+pNPaa+ZXGcWPrf3epsUBZouDmUdqzUfEN+2LBowIXF38jt+DI\n", "qctJHncEhVAwEP3LDJDUHU5DclYHCajmpGpwFBtO6+nhOVCJTRSjIpChadNJ7w3AY63H2YfdBEUR\n", "VQbFjMFjt5KKL9lVF/glTbp3Szs8Vqz99Goma5jR32ANh6v+3K7EnATFt6ANQ1T7T/mvOB9V5GEX\n", "htaS7ILddcSVmVdWQL/namvOChHzv3LIQpnOEaiAl6yd01Xpi1s021e1Pc8CqmXwDVu51fb3AW2z\n", "uiu1C5V/S+SYFN/3B+okNM7itl3j/SaLTbIpMbrvNp6JeQG0F/aGOICGxr+5kybi+AyDuhYztVi6\n", "gZnbLHhncDnqN42scof0G+x37X+XPoSHoFa4iDipc81vQ8eHhV86JCBeIKIxX+9qNPiqY18uUO0t\n", "4DfZbYWnwgHRr6EZXU8gtaOP4T71Pnwgv7cfOD1hB3hqWXF3uwIe5ENiNRS5KDLTzFLK6iRySPIp\n", "G0tyfoNrbhUfVgTpeOvizC7bZ3fsM8cZtRSApHE2kkv5hNjHjNV4r3IyJRnyBPrzQI/gLmA5mBoV\n", "WC3s5TP+QmPUw3ySyZ/xMiibkd2PeBfGtGmRmh5WPNfSpXIsXPDWQHzK8u4giX87cJuGvtrQ6X8E\n", "8vItlwhISQ7fk24q/8J9qptYRO+SEa9jiXnjTRDkDhgFBDQ1bnEm52ExuQAyW/ltqhmi0wzVrgXn\n", "UcfdPyeXf/esYobFIlt7BVBU3+hkYWORu1Ke57lUGm/10N9TakdjFCxLd+OZXDL3RbxySzwJVP0A\n", "D7xSzMeISoYaKg3mAKPjdg8e/WhKK2g1hIdXvTT0nArrdvVCLOLGvffvlsfUCqmrHVFjb+1BIA8E\n", "1X5TjkjstnYa3q5r0jpY6q6jr3lUl/pXCOXEOxwa/2yzgP/jOTcigexx9Q6eyGZQS5ErDjOf9jgK\n", "rz9j3EBV9Sd0INsXkcRePntUI+V0xYKZPa1USuXRHA3uI+valUF2vfggAcCLgRj66S1GJ8hNQIM/\n", "hFIk2gPZksxMkjDiFJPlU1t+xlAS32FZbfHMssxpkMK/x5N69U3vGNN5nfYW8jpzNKXf3AFdIBvI\n", "1ahUl/2lHHXZ7L1mism52kmmFxiFiEn2AMIPoM7IMK+wSZtIkK5dCXFhVfmXMTqTQtkm2l7SjXuE\n", "MWf3LzjBzAOCzJHx7Jv/+re5G+hfZTtMnIxTYObI0ooaBzJq/yU3s1L0DrftqlYxwOg0+pvXHlqL\n", "/515JxSIXbdy7Ff3lS5RYX3QfbfxN6YPv26ZJ/W2kkz5lcTgP5jBI6pgosQtjxrUyknqctyKpo5J\n", "A6Wi+9Q3r/MX4go+zKp9iAiGxFPsQeOt3qMzVPCAsPlIxLKB70vg8dGsDzByG7iRn8jfyxpwYtY6\n", "BblOjp5pjUj1oQMXSYZ3FDOQlBmo5LuGTXLJdoQ55oLmc1jZSWFbonD26Gt7L/+ZSp8v7+g1al1C\n", "6wAGUlpGF92w2GYeHVSteXc8fXka7KWmsBS23EIpsl0OaCJS0j6j81MAyjahMPBbfwKZq+t7lpH/\n", "QveC9bUsMMHcBzYtBebfdoF5xXIihl5ekjj/48fr/tf6M2P8n9nRAUDXtyhe5EzTHvihXU3UKkjN\n", "VLY/CXEk3IH1pzHk4Wk+ndpkPbjI+486WIosNN8iRUb6Hr/Bw1p0tLeqtzH/1I+gXIhFIgzndxqV\n", "oycWiHXtbQKGm9yhhx9cup8O/PDTmxjr28dBSjLFKmTuHWk1vZrTQvKuDUNbuALxZ2lVdUeQNwRJ\n", "LxDignPpGR/iuKIOvS5M6q9GSwXsedSOen42M0VhmJpMjAXKWCWO1y/np1RoSiUZ9AhkH1zk0YYr\n", "xtK8nNcU7F1zFfJUUf1LOIaWcnKHpXttESiGF6Rrw/iF4qzx6695x3VGo8leVld7SXJ3ahMep2oL\n", "s1H5R2ppzkK8TjgVVed5cfs6m4UDvL7gh1kMgklrhY+o7uAJdyerT9BRpgBfdXk4KZEgv0uQE5Di\n", "U1ZYkUHVdtGvaXy6t9PMsH06OAwRUbSMjbW/Brgr4Tkkvru7mIXcwDsXHx+db533cCeGHYLk4dma\n", "+RZVWuNjo6L9M7LEWrHEfYHhcx9qCHqoNugQNfB09JabGiN9GjqNuOzAjfV5bb1FYtXtWU2UYoAd\n", "j6tRSKnA9nuXMOBSUpSWBxlJJW5gqWIvk21/MfgU2Zt1ccf27HD8ouz2UZLb3ISyDiVRN4EINFDV\n", "HpY6V3Cr7OOS29k4Aar00P1q+UaOQrE1nz9r/PpOIE7Xe5bBJM49W3rnDroxczELCL6mz6xQfvSr\n", "u7jEw8bGlpqfBHyCoq79mP8wSLUVG1JpMN2D4Ol2/7fKeWQybRVYQIsasWCo9m7yydXZQQv6zZ32\n", "ztuMxDPj7/WPK087V3WBSkpgAQYYFOMFfI+rTTE+pWwzJBfJMjciFlxb7aM78V798As58aSr/iKC\n", "SCZ0+UC4BgUAAAfFQZ6GRREsEP8AAJqXToKP4AARjbcVj+QWmwrjBB6rlOMGz2FO9J+Yk22P+fo8\n", "FnLfHTvttjyjU0EcNNGvgqpRxCiqlkqWAAfKGvruMc/Mf4/GiInJfFHjw1sXv2sgDUdH86Cs3mf9\n", "RHBrjA7pJM5GLrKlR0xw7vFJSQ0oTUtm41PLWRKcMtTzXrGN5T/pak5p+NYyH9Q+FiQcqoSvUUc7\n", "SZ6tbGRzkYtGrV6xoB2rRGkcmiywQkotgPC+5d8oQLAWrLGN+/0wn9Wi53UCAgqx/GCYrLLcjV90\n", "gybFDYyVcjuLh5LUms8fFb5QbYICWP3O/mzRDBfDR8IVwKfXK/cbJ3UX57dae4y5t4ULFMsVyomh\n", "/iM4UtnjCp9H2buu266ZP4XmSsLlBCFrlgPBTIDllG6RXrL5ojGxeIM+2b1ZkJ8YSbQTOD0l3LoC\n", "5bPtZyR+kSQ17bk/YxIVeQqkoxMGnuGeP+qNCBg/ZVEpqkcfXhWJ4P3oaRUHdBklynOLBxA3vpBL\n", "0Ftd2n1NOQak5HdwcRcRi10PJv98J4I5br2EaoafXW0XmgTtzyH9ARNWctl3yVo4panpqT/ofcEE\n", "fTAL7iW4gp/aPTqOE4snzzB2TBw5DT2i5BGOdfeU8fRsVyx+S3Ggeq3+LAUVAbffJQA6cbbCyc7i\n", "bSN9SlNSPN8yFJMDE1TyWxCqKCQuWyjg/iMRqYVJt2HGkTIEykoqF8RUkgIkQEUX+knelq4vYN69\n", "GrJhEyvvRLlUPeFk1FOoKnWCj/eqpzy6FDShvh6kSx7YKYQ5aHjnhfgSSZQLKnQduTKnFzfydYnC\n", "0romfSfE8OUZy3qII/rBQa+SL34frGH18EUG+GcXo6mPub1/OrShaz/qgzSlTneNgSFQHo+KYJBV\n", "ASibtXH2PqOeIANMlNeoadflsrteWS336JAkBTwQ8h415pY/Q+byqaiJ+kuA5m2Kr/ja3LdMaGxR\n", "m3evJwHkrVQaz64jN7RwzZGNXJk8COf7qlF4xgyJNOQ3w/8yKJMm8BB3OW903fI16QWKP5csJDVK\n", "uHXcdd5yXqwsBpBlXMxUvyoTr0SMioRAu7yKIxqcS66hOdcZPCjzE/Wlwoin39HW9+FDvqfHaRrL\n", "Fp6oQHZkpDEBP08wVRcWA+Pf/IInuGjaZ7/YT3PBIYD4EtMs5bHcaprKdVpUdtqAL521/H+iGwe3\n", "AAE1jOCpX3GVVzkTIhXTmAZ6zKI1sXfAkzozT0t5CHlqF8SNt8YURoNTSx7KSa97x1t7hp3s9YCG\n", "DzuIoV52hzjEpEAZndWMq72mQp5HT5JkwTApRUeinviodJm133YzZBYPEebsgS8QNkmzR0uc0/6H\n", "lf4kOI6RNDj4ShPBxTFyEzxaD8e/Y6Yu4pP22MJXJ9Zj1euPA1SxmnprQvjwlleox+ZI5srTqQC0\n", "dnC89vHVwJA60A5tSSmX0xmidAmCLD0slNKOYd1Nae9ZWeIghGErIb0TfALWgtkIRa+IxR9Oj9pI\n", "oRqoKFZqTLHLWyWxTWcY1pldRLyXursMHXxD81Uw3w/dGfyYENHgS0QrPbJG2O7bTl7P7quVmytO\n", "cTFuGRdN8j7CSvLVTYmplVTK9ofp9/JK/3RWFPDZ13AYiU3+RbSTZOP8fMh0+nc+HGX9QwrwZKf3\n", "4jUHm+wewU9ggoEEkkUYI+mCmTLXNCUIDkVwyaNkcD618mp5d+CNO+A8WDCPQQdX4MK9mB+8mLU6\n", "WTLxTsnCUVrlsVSzTBzYXnzrDnWNeUuq79+Z4seJx+kG16Cdm++dSsx9Tqxu5/RXWlHgAaFUhzG2\n", "X7nozVmGSwxi5TVxjOCmx68PRqJInhyMz2r7VsLhvvqHDaSDw8J0i5i0iNIlfe0YDm7ri6FBIh/9\n", "CaT/peqT994K/5maKP+QVVXlz4xoq25/fyIOUZ1PoU7H3AWNXbyZ+OQz0lMjjK/eLeYhVBKn42rP\n", "XpS3prTPjXIm2qOB77YCnjyeziiHmJRTNX2Qhd1kh6VnWlj7FQgAuqwrfyq/Ae1mQ405aQEHXMZd\n", "XZZBQobslHm7h4sWfv3y7RfXaXfyWOHWwCmMzXuQPFGsVIuAgKDA9OIjzFPODsBTHNlgupfaz0B2\n", "UzsIhCNDYP2p653vf4ePVyXXqFxOuWD/zb1pQC5SRMftihL0Yh2p1pWM091tRLE+3zSJ5q7ydZx8\n", "xXFeP1LJktujBtOazRW5VC+Bz1UTx/gTIEXGZOYjBNQsoWgN1t+AmIzBV7tZBWmwedlzVF1Fhal8\n", "YTJurvjfMW6Y0phhAQRxMfPZjou0C84tPRgKIgJDFC8Q6AaNCu8VOU4YsZagZKx/+gTraDzEDFtR\n", "TUpQ0HWzxSUy9VzU3+4eCfUIUmmCtNS9Q61biyHbjXy3Jh7B2HEPgNHV6J1PvRSTTU0bWt46Rv03\n", "IcJUoq/6xvnOXumLXceIshkTWwglfTRz91indAN8X6fjiNFvSV8oMA7a34zrk3Boomg8ZVKt1GpA\n", "Sq0qtEE2TTKdCrmiTYBLghEf0lO1wNHSzR9bMmGey4tLeJGJBddXtpt94pX2+G0N8MqO5sk1ZIHc\n", "qIR0R6JyeoSmSqzGsLZuiZKoYP8jhXzq7ASa2dPkckW7P+GluUTUEZWZHFdO+8fmEr1q37AjXY+4\n", "tpZfrsdlAAAEagGepXRD/wABWUpncHfJgA2q0VdREorZVR8gSEgPVKHEKcetboXKF9+Y6jSFA+7u\n", "U/lb9il3/PEpoKiBjwQtRUfmKFDL1HsZbIkrrhkobPdITxmdE5YHVbSB3Ozhi/JfvqD0t8i0FpBn\n", "JfbDZOluAFcIpFIs2QCJnDC/jEMdzP1/YPPwgsUiGlxkUkC4fGhEN6CzLYi8w5TKBh2qbBpcLl2a\n", "03gqkjFLDb6olPpeo1MHkUuqfWXbnkWYoq5sKvC8f8mkrS94DzXCXUadwVuJtdkUKc3QUrnj0tj3\n", "koqjK6yQLSItSHx2IepxdUiml0lBYVDhD+FqzQCfM6Y1HS2XCOvDt0TqGgItVVHBqyNe39JNIGGg\n", "lR3/5hBN1sMKtca1FkHNiVZG3yNh6F6x785MerXZSMPU8hexcFGmeEjuoXoHyYDcyzMsW9PwYhof\n", "DvBoCgyPQyIVsr7IFzjISGE21z+Ga/nXdVE7Lv1xULz+cVxBRfY0Nn4gC7khmWEz4q7S7HFuHT0a\n", "z/No3ulp594zpuoPVpoVQvOFuN9L4yLRzekyHHBe4Awai/H0N0xJ7po/iwynSDPsGqjb/e92Eybi\n", "WeLmd3yFIBzTnI/0MbKjGaVofEcuxuHbrLjyUvv6xwdmP4X+SNNaOThND2PvM122IyPTm+Azcxph\n", "Px9u2nlpCow+W4qXkbY9vIsGv8bXmlda4EhP+lFFMN4l16EKvaZmtl6Q7mx0kXTTRV7LDyrMOwJm\n", "KM3GIgQzXY3Yyij1aptN7ekN+sXVfgCWQiQefFk2I/YPSwelkA9oNiWwIR2cScSJomBJ+1C5p74h\n", "VZjFE96wF/jyY4juJtgGJ6YaQcz4mHVpvKBrEdRaxZ3hSWsitQYBK7YAa13ptb7ovrReXyuTubLZ\n", "JQ7VN8ecQlhVP5JNICBF8jSnAmPVbGjqoxqnn/Li6F2eLh83ngZQyCTl8bk8l/iYM2m725FzqdGt\n", "/mdlfEDi7ucNIkrXFa5GZkzLNWTWoEnOVD43WayygvyD45iGuUOmCPhXMYFBytqudQqh6TTL4edH\n", "wZetmfueuhKFFj63NCRLs4R0Pu9JM3Ur+Xr/62N/EYi8ZUUMXyyCqtvysY5wh9Pn2DDAxORb2w5i\n", "tgmL3MJf25fneA6ZEInd4mDobv70qeyi/T6Q8HGzoYIQb6GQ4N9bmVKNep9euWk3bZMdwuqaJXB8\n", "CV2mAFAEw8Gceao/3z7Ik0N9zbY+TtGuOOfVlCsHPE0Vmi4hr0F9F7E4NfEU2h6oUa098+bSBxFD\n", "kC4IVzz50RUhgBGxPg5uwv2C+DNgQpwh1DkjizmnLWVDlDQhNLjbbdL+az/5oowISi7qBss1rB85\n", "WNFnWhKwyqdlSHPTwDU185EJTA+m0rXZE67l8yenCnP2pAB8UT9Xdn/euqcf4i4nIXBr4Uz6Cjrj\n", "0yPUTCWtXnJJQzIIYAqTDaqB0+jS6LM0p3ZmHyI/qf+CWxTCSpo/yMUyRXsk/a1nIjw8kL9AgCPh\n", "AAAGkgGep2pD/wABWVtVvogKEovACE02T/kWqFAorJ6Z/1/TTXI0mrk+2oRB1FWoms5jaVefepk3\n", "Bu+S2TMIYuXt/3Zs5xpusU9YeD1KFFMHtl0gGMdmHP9G/3wEfgfH55YNteYkPjBxQGo5u+Z3f3Sx\n", "6vbz6nvrncOca2e/+hCamksNo9TNbRr6+1xsM1mkvacMeKHHRY2hT1Hy2Q9tNKblwy9wFLpk9bt9\n", "fTM3x//5wDtoE8qLUgpKGWgIWqFgYksM0kcQOtNEYSF3Wg0YjTaPW/f5caE8MBCZcozFOo+1V+rn\n", "fJ865bofxxP/5cjqVjLWBhyQdi5yOUOIkfxVpR1cNTW1M8xBRasL+8gjHF7Vvk+lSBJ0CAONxzGo\n", "+YkBNq93p4dlGEP4fvT493RVdP+InrrYbZ5LnREb0AhWl3BRmv2/ifF7fxZPDJ6f94rMa1OmQW8Z\n", "f++Sx+wri6rLvJwDbD2n+6783CrV1iUHGMLVi9G//PVc2PNoA/XDt1v5DUJTVuq6Sj+UDxbZ0Jv5\n", "13Gw1Jq08VcHT6W1s/eMvYHr9FppnJbYf55vNyPGiAygOhDw5B1dXx3axKKyd5TfOWgfNtf41NU1\n", "qsyq+uj+N1dyLQQDQau78axTWFNriquVx9znYfykEvwVlJsJZxhjegqTFOYDZiI3SoaRnE9R+2Hk\n", "6eqnrlWjauJZOo2lv9HluiItZlF5nCMw3LHY8ayeaCmWf0EEoleK5Ktaw557BKZjCRdYOdlKfiQW\n", "3F3zaIcVBEcfVwCDBe0yASKs+RequMMr3UpLEDwJ+1phoh4dTAi+Db07wn1/tm6yiQVwEvesPBQu\n", "+zg8u02QRUmV4SoR03gAUaXLO5FcBVzNklSFdqqP492vqbbo3jiuIVrU69r727pxWxNBgfDk9urD\n", "btioAMITqs0IqocXTZOezO4cK7U2Ij8+A4CRgPW+WFQUdslV3UUd2JQupac+EyWGxqJnIWvOajLY\n", "//IbK1Tgx3Qnpcxt6CY2NxnjWz2hKBOQON0vsp7Ih5MomAJ0lt/5bRt/rJcg1cxQLM2YBx0xrbxW\n", "ihV5MBIbRS0iAT0JeAHaNsSqvVA4kdph3T+tee8KBP+rOitciKB7AYzdbh956gH/Ubv6/KQHGz7i\n", "oMsUr7t0G53qNVRUMqPH4g3hojIrEghIG147OSZeyiNusMAdlrpJrF6RuZ3aB2n3aE46wHyBDFGX\n", "eE93URmcd/oyXxp0g1z9dGvcraWbPid5CxndJuq7Ll3US0Z7Q+oQGoDViuEcL/6ux2eXddeW14KV\n", "WDm2jmom1jGnbWOXQHi3AcfsuUS5kmLeX/AxD7hOfMn8QnPtZM7XcUkeP0dyxImEfFqQ+ACpQ78z\n", "CwmbFs90KNfkUDP4LnTsi1HcRYyI5knDB3iKZn4OO+Kb6K1pFGMFjJAb6XK1y3TzwZS8jfzC5EEV\n", "lAVGX0eOdEIoKa5/HiGhgS5jlweiTqUSqIUAG6mjRIcFNuwtLnAmf9pk+P2BlkBmj2CTrjYSsKg2\n", "FDV6Eo+oAkfW7WKRR+hbvt0BjyvKaUGMPuM+AU04j0vCJt6hrHt+82Pg591wGHVOkzYRPnCxEu6X\n", "4wEJ0imbC9RuR03GAtV5hR1sbpdSlizb2liXFea/UJs4NKSGVJoPkB4/u1+epNGqrDd05bUSWU4V\n", "15Px4Rpw5IPMNPcDq1WG6HJlBn3pjceTSK+bmW7QZkfQp/JX1pKcQRxmiideD23v+AbB6nNKjLG4\n", "Vwb4AKxtjrC5DVN0/DGcBAsrTL2QBs3B17Icn9Dzl53Hsv1CL6QnPxIpWwHX2KQYu1+KK+60QbIB\n", "Wqrb5TMjSQGBNDcVMs437K5Cx2c8Zr3FS1kDlshEC0qvOuwvG6yYhKzkFb+MJ5/11gcMW6oY+jrz\n", "jkuD8qIlebJazdGQIs86frpd6ERPtxmOP/ZmTX+eeYFtrHYF+n47xWEd8Xn8UFUycSgaCVBWqa8i\n", "8Q8GdNtYB5ZE6+PWc4XY+QQ/s3aeeX/JcKUiSrrGcLwWod3JuTjbHr7wOi8XUd3+Ci1yhbjN4F2T\n", "NESCk/6DlOeXAfFUutmkCfoIj34mSdFbSelwGZML6tHWPMut0QSvdb0i7mqiznlAGCz78hFnl7sr\n", "q/Um+wql7c6plP+hH37YoBCufEWDJC4LDFEM4qjHX8LV2fX9/q/iV9+msOB0BR7G8BvWAlLjh5IV\n", "UyyHYKVLCuMfAAJQhFNAgJll8v5rsKS/5i7Bnq8gvZ7AAAALKUGarEmoQWyZTAgn//61KoAAeh+N\n", "paCc5ZVgAJ/XHueWhzTGzjpN6Kfw5lTyL6U3gLnnx2Ofi4js9CHUBS2iBIn21Xl5t8tDH3clL84J\n", "Pqq1iJ4g9VCcCFSAFF5yvfGmtUK8BD3mh7gTNUY+CDeq+5zxJk+6PIk+cB0aAl350rgN3xWc1Pxi\n", "lA9mYhG+qcefDF7qttDO34fglFiM4G1D2Fe226kK7nneGjzZAVqJyiOzVt5SlXqx9Uer6N4ZA5dH\n", "si9eNIJ8fTo/39vXNhMSC0oZs0bX1raVZBwvrnD5hSi6pTCCaxgi9ji0Al/ahwYZtucVK/PZpK3i\n", "hF6Eb0IXLsYKFU2t+qVz5Dtbu3kWrhdyRoaYeb/PXGrpbz0FOC40JaHbLQ4YU0YTY1jDWUBg0oiQ\n", "fwCKN3FeGKacv/OaSh4DoEnmgSWY+YDnV+v5zyzmdYvZEwOrvlfvkw5+wqQwlqKbnYbT/PpdVmZJ\n", "JfUcnkfGaOgpdKrNn3zus5Q6uQycmeH8kJVL0RhKGOB12JOhCojmrzeG/1Tdq+q9NSfhfDl2qPBa\n", "VuGye48LzOUedCeo7Y9HhOuw7pC57UvWvLg6mL/508yRQNA9haZwEuvYGlsp9noJ16DMViA7PNsF\n", "9Dt1bMTkiA3C4g7WRseiyUJ32LSZQ4uQJYE4u2XY+x7xWQQOt/mJ/ZNTR8+lV5P+3Z7zbGHyGAH5\n", "+5ZSYQSpP6sVJc5jVLoOGdQ4jmCjn7SiTX9Jeaqahz9A1qshcv2SQ3ytxggXoPrTimFzVDiyx+9l\n", "xlWB8D8WslBRWpLSO6c/i2cgo/C+eRM82wjiuX0npnUbusAvAnpdC00DNsLJ92RUKAe2rvli/hci\n", "v/jg5ITc/a6c3De62TlQeljEPpcNKPOv1/YyZskgnjH6z4/s3eOULryrrfu/62fCnvy1TMgp5Dvc\n", "yLcH2rATUe9kglrxzu2XjjVF1zjOqk2aGd14p5HHokCFsgzGM0Pe9CCv7Z/jeLZKt1eXcRlLnTf5\n", "QOgkR+Ev+Le62+vNraiQhoJdyYqABs16crDGdcTRg3FrAxBeeMLqefUczd6nMaXkhUm8y1eNDNE7\n", "Wn9bAKfo2iyQj8a24AZIO5kQA0fwKeI8xYTlRCxP/1XfXB73edwDexn7913/Ad3zYKwt0N9y/MQz\n", "ApAvEGH45K3ee5fpK1i1d1Z/rE24QlK+2ccGjbIEna6R3X5TKqxNnMokw11EwB+wmvRHzMeVDz3H\n", "biL74IZE3l2L7ToipRA/kZGT8LjTn+daVLYjY6DiNIg0YQo1Vr2qJq49C2dvCBs3WTc49jEULf4j\n", "jXlwcwGyX8UiKo+Kt/lNyletDDvg5pBTmGEjr2FENaoui7p0a8QsfT0dqsb/1sBGI0F2P0wbF/B8\n", "MTfGu/XbzmkDvpQzRIT4NI38Nd2gIkY4q5NEGHmk53NMkDYyfTBoNX+UnlMdMkLNqH8m4VNNg3F9\n", "nSzwx32yGkjHtr2YcJkJauP5teeILmN0AaiyKHnp6JrX0yNHjOlTbVNAxpaIo9dufS43MPzmEw+4\n", "5EOQsXSSROXiA3zcrdBQXHLDKoxwDU6eWgI+99tQ2SzSivNGdedgK+a+ToDzuxRWgIik6WJRMlLs\n", "zEGkN+p8n5+7KoG/B67nj3ZUKOiypPTRmWTw2tO+DS2wNpoJmDzq1e80XdUiUml2heSoWgRZp2mF\n", "uBv4skKhhmEV5TFNAoiavy4+9Q+QJ6HgONbcK+fQfjyJfTYtaT97Baf8E8Zs0bv+TRRk8BayyfPO\n", "jjGXME0g9emWbVGfM3UyprCMoeouwtNYHDud7d3LpCj1yMYPKKC7JQb7MRa6JBFdv/PWT8Rt8BqT\n", "8ZQIB52c/wGL/uhjAmdA0tTfmytATeNfl3bA2aaSeutFR4P7JJlXgxR/ZEWkj+cjsRboD7/4m32R\n", "Lt8IuliXcgyd/E3Y7yrMspvZngbqvC15w6VEdx1a7gSKaFJShveDsNlgeRTEwzEhREccE/oOHeGJ\n", "QCMSJ3oOe3OHjuti0EJvh2gke+uj/lKvxULCYbkWY1sfrCcvHsF3d1SAKwYyrBBc9aOZXc3+/Nku\n", "k/uMgXr0lkLOL43QYzH6MNAV+DKvilQhAMwoWRptLg6VPc+cyY8/3gwLQVZobivvf0m3W9Wl/+rX\n", "9MH/0rcUEe0OYal4VavqzK6vnGlh6+YjmXSphQWWzQFA6P7cEuuT5Kl4cvbdc7HFn90kLvaChnUM\n", "U+BBjO+8+pIlbxEza9gdNPhRhPxE2yWHBQu2y0e9YxxQQp81v9L+UvRDwlF6a9UnLHxxYxu56Ieh\n", "NPzfHAaIl3Yre0hN7ROQYFQmhU66WmoIzCDmuOrMXBCcq0w/E1odV+yuttP54u8byzeLvEG48/Xu\n", "j8m14zuM3NhP5CGpkcoLThklzTpD+MIuMGkyypLJr3+0akm37ceUcv+ig24eVwroyjguAEIK26Tb\n", "nv+5V0dbdu+rYKejl/SRHYRe9yu54RXqXTYXiWT9D9ht1LumoJ8SJdeeYCOjJoDQIyEseafUO69r\n", "OHOBZB5O2ToYXuLCAWgNKkCXkUb2anIW2F0JK2rzyxK0bnLs4SPeq34ou3S3JkL7Q7h1Dtkpair3\n", "epZLCU7XTLf7/LojUtY6NUZY9irDUAne55nU+MUgxR9c1QAnX4A7kDi9jqiDr6Emzg33Zu2xkLIi\n", "vvcfsd7I+ozOq86ngpy7jgMEXtf0dGs5IYlVGY4YCXeNeQfVMz0UrMLmejcPUFDeqSPj5uj8iWFe\n", "oUCdUHP0ApNvWFFYRtXaMrhgNk2ia9ORZXUcBaQAkvo43aX9qUs2/ZPk1M9PD/V8DP1Qgp0vTW4Q\n", "cDg9Xfw5jqYp5mKiF7qnNXtGPbob7pSU99wZtIX311fBFyC3MnhjTb2sCG9xX8x3gh6YC9yuvDnc\n", "6m5HLrV0keDlr1RDa4Ipz7QA4mQHNJq4iBoTy2KPjSQ/70a7mJ8RfRQgx6T3O0wg4EAm//IKiqtf\n", "NxRz1fcR3IvbhA0jHYxlj32yx1J639h6j9UOrsqKRZyhDC3qQJ0xJCtHot+NdiDK4Slsd2/2qyBl\n", "2TkmD43RhIjH5gYNdTj6TsW/myPXgKKtDZgq3lt8cq/LV0u+OXL1RXp2Vt4LP8ebuGaXOfqKZ41n\n", "mBVWcD+fWD/XFBsDhRzy7BU7YLzupKwgdYDHIDsxMDDpb3eQMGQwnD974EhZdDbb9Fcfr6MNnUnB\n", "IOFDAndNYZycFiZ95Tg+29wkPirZHTQdgnZXaIMBQYfFkSX46WTAtxkm3q2GcVpEQgaz4BV/I8fk\n", "ZzKXbC9No6urTPRHM7UPesTtJw5nkH8qWAjsUwEkLmJnd5FljHIUZM9q6orb/E0V9XHMxaAUEYRg\n", "MWDQnFT3L0wwloHv+HQsvaN7wozDJFqPy4mUobHfFgvfhjWR8wJBSI5BbmxbZMSl3bKfcbZ+asmE\n", "qvSzDPnJdEGjUfbDWg5FPKvI4T8wM7GzgircCVlCnNAoApCwfTNTEg7v49FjF7sbxYcQgeb1xyDL\n", "gGCNOORSaCl5cUp9GUS2FHBm9Tio9n+sM94Y13g+GoVJV1z8KZ1hIy+xdowRiLNQj6wrZuZpHrNS\n", "lovuBlgPvVR47G6R37WQec10kL28ziGi6Z/Oq1q1JjBip7gXhrc0OgwR3Rt38Yg1oGaqZ626KaQ3\n", "+dFykpUMhZh3YSchh7EZlZmx33hVadoiXQoMliihnPCkMcA6NyK67is6QTBb3UBTUku1JIA8KDyn\n", "did4rl3xC3g7nL686XD1XjWX5w7Y3QRZDJrX0xwczYHyOVavvmG6jUYD/oAAAAZcQZ7KRRUsEP8A\n", "AJqh2Z7KAAhs9P7bGTkpnOp6FNkFG2AXrskOhZGEFhi/a3HNH9G61MdAf0EpUCyhIuxY/RlvG5lt\n", "mFmgnGhVLuEkt2QR2wlRlfh44wP9eGNtg07LEOKj5UDQW6THDRP886d4lsDLhbxtV72QyW8bH2Fw\n", "AJabhFI48rMrdOSixSOfUFLQIq6j//YURZt2XnBvTIiDi/ruk7oOy+1Cs62n4eZGu4AvDh+ERM0u\n", "MLTMnkcUnnZ7Zu9Z70CD/dLwqNOFOORIU+csq2/9mg/+cYMeSYebn/Hy2pJn4OZq9F59ikqjtb4X\n", "+yL/lgzHB3Q0PjtnL99yicw76y50T2YFiyLr+1estlLoYnDd3N4AAou3FtUDSztYORYT7KYcX/Hs\n", "MLio/+FMnkd57lKKCtFSfwwqCogLbun1uhi/q04H+zVhBUSxUWnur3QYCFHMmV8h4i6ewflhElS8\n", "B+MuroEfjlI5mGhDsKEvPvCOfoMe9S7LQXWZsmE8zlh1GX9pksbE9QW4v/24n/1zAdvStMGkM884\n", "5QXnNPDhAqkAKJ8uMxUWQrQaPr3uY/lWhlkdgXs+bKEgNQAuWB/xO7jgkviCcTeNknkid27FL+0Q\n", "hP8u0HB1v23RMBQhsrqwwIygNqeGenwC6fCiSMHfcZVY5ONMpnanficeSXtnRifPzkPf5zPJt2Nk\n", "0+IpbgCpZAdf8doyyRP20oykTzmI5aFiM/YhsmmU1d7RCNwHm9Ch6mVxJu93XM3E0Tbc2hYk/gdu\n", "NbSweyntcgAS5hHB3UkBvMIHA/3gszQZNNz8UqQJK1nXqjLeAayKLzxABHDydABV9dgFCxLWFlsZ\n", "rgAZsYLcVDQNB0nYnNqq7v0/n1s7mj+OQEGQ1mf9xscFoT4bmTXQ7CqorIqzvIu8Z5u7+6eFjwHi\n", "otUJgjUOAGJK6R6iqzJos8W7BhnXp1sdNzIjkW4Bh9w5X+5tXq29o8c8sdoAZfTgy919PTDGFrGf\n", "/V29BhJnXUP6gT9icfvp2edW5E3Q0Shnk25X2171FqAPcaawqVUsSd2djOIY4x1qsdJ51YQ2p1SB\n", "oYreArs8aZ+qin+ldY6l7xGngZ/rHIxX8Z3gVbt4rtXVPUbzxDwgAwr3M71BQd0bG2CFWX6LDyUs\n", "GqgfJX0B1RDI2z/PfViZvzxVUlSZ9yrsdoKBh9l8wKQ0PIR9wBPeN2CGBmRSP/EAtmzONnW5vsK5\n", "g2H0774DX6TLDrik1rwY1pH8pguBtetbV/YBPGOQO9FoOr1T+hfunavbjeU3jADffoxjUqO9MOgx\n", "NiqcnfBXhOCLRbvrjSqQDp5Df0oEwkVyttJ/HUArK/ywEnS8C10Fq+Bt9EbCTvl4eQ1ghU8kaF14\n", "1aKT57vi7nWkslnNSvY+wwDVds+BLrjtCeZJKB91TSt9eiVll5XbtU5s2jrs9kJvoe5H1owFAGIm\n", "w7ZM2BzCfMpUeY9xcUEzi/yjWnLft+6n8ihMf94TS6i/UcYUiOa3P+6vtlWVoTBumeuO4BCLe2uD\n", "fgvwtD6KWunLcszUpiez4p/jeV3j5zCLE8abIAhWpKf6Cb4nr5IiPJlV9siW/GN8Bep4yUI08FkD\n", "bUXKi3EfrG6jpUT/FtRQ5CFsLn+2DKfboBCk8aEcuOoeciIt8zaTRsAg+Ar6Qdmn2yFyLbN2ISKZ\n", "iGliTZ4vRSfoegiFJPemqg8Z+X59I4qb1oA2kJGyb7AttJi7XoVwsy4eahDNg4T2+oM2DV/vcjRH\n", "f8dQUmWSw+jwmwPlCpbMnRAs+LGFJFIB2+cOzcl31KyR7KtSFHRTELdJrrU4FYl/GBxfdUrqw2r1\n", "QEDX8+Iphwg7t1OcfNr4su3CerhuPwIGro8ZXbGyg8XUkCz20aTVAM43aiqZqGzIKxZoxt7ke6Ff\n", "jFhgXBDm8aaOwsWNvqinSbsJt8Bb9x9B1N7lTGPFr1W3e/3O7PvWUF0sPNXjqmQ0sgxYO1NXMcou\n", "kkphEEKOtVIllKoJKcyv9Ma9/ZLvLTH4W5FTxP/s6qQnFkY6SSHXxDa6V9cdu3uVaWJNr3nzZSIk\n", "4aPmnu9OUvPuS+mrwcpj3QFER6R0oux/PK7f+bE1JnAMuyycdQUf+v/Mf9HYAOkS1/T/e2RTPggJ\n", "l6b9MAFTInq8+WucWC1CmaYi5Xye5rUAAAe0AZ7pdEP/AAFZFuXo1oAH9IdiBHrRrWdinbmYh4oH\n", "MoGTZTLe1Nhci+VXjffjDYkxCTQ18gWa1WP/N1SDM2a+WTWdTD5xYk+mIN46gn5Ewh4pfls6oooa\n", "1XvxlUr57fNCAKYzUJ7bq2lSG936OF8VxYPyna4TJDDf/GWNkooyK428yxPKpZ2yhrX6DawH+M92\n", "uNsboymHUujfRZkTmBncqvrNi2wKFVZ8OBRI05wCvt11xASr4a/ZxkRIAh2+p3EKpcg19xlpMBI5\n", "Lsb+xBwB+4mcfEJpOEp6KJtYzY+9ytzOu/g372kLWRscrDLiwC6mmp081PgYy8Ke7ZGhiJmN1v9U\n", "bGn3csWF8VwYJun4DqEzJ+kmBC0Tn7isjPnVDajlhY7k+nMBU80pkwfHlDlAh+Q1/cElLrtnryHD\n", "AGFT3gaX2M6HOcKAOxVNcHIHt0q9xHJmHRrL78yxgiacJOIZuDjp8Cn7R+XaEuM6uT/FPdDBY0j8\n", "JIR+1bInmciRJ0X4WhGBN9CDg9jRKVNB5Wc/PDt8Zuhdw2idheKr4tmmpfM5eWXLpdDdkKHZP8/b\n", "dKDlsdH4MAPvqAGH6FQ4oR/MQRG1ufpNZB7LiKN3jf3FIxuIpu9qxXT2VHznMN8LuKfhm65BjnRc\n", "1NbNPxQStXxnf3B76qCvoniIiv08+A3ZojsTTCgrUsWNcWbR/LQkeUwp4iT7aSoT3f+vALdcqfMv\n", "UrJVhATqBE+gWpv9vsFoH74KXcST/uPopYE/TWp7nVOinv/fDy20pvRdeQJwKATgJpeYm5hPS9ct\n", "UNyzhu8yC7x7pSwG2FmEHg7RUaouZafjTJi57IR5kf9GS0vzC+G3O2KCmIt2JIX957eMsDAS4+Nu\n", "lF3piEJyGLeqcR+8fuVDkmqC2JcJLkuWqPuILXwrLwJt6jhn5XLgNL0SAELOoqpuSaS1cZWiUpml\n", "1VzBXl6wgJlEviKWMjJn1LRrMzu5O5oB/J1ovWj2WJZ8mEdkAPqz6gG+Fh2vwol/oKsN7S//TcKK\n", "46gLaS+JPy00pKr+4y/FCFDha3abEm0MREvdFruxLKVr2abi3Gz8cBgcyxXJNhxDm4y6LtXV8H9w\n", "Wqdoh5bB/CwUSMjtVPp/dLk9glm5PCreL0S/f87/q39vUT8zZ4b3/C80lfSTOPS4XvLI6RuoRCoV\n", "uSFRnwNRSOK7Nhrf5lucumylEt3stXvPnLGEp/rtuwEVXJQaOzpHwLRP0qx1pilNqUn0Q8YFEbiy\n", "0saRTy3329XQm5LHC9XswkvXzy2cH4IAeb/8DsoSxc66p71lMt5ppPd2e+055PRLPPls4qT/9y6C\n", "WrzDlaHS0yAL4EwuE0wWWgvUQ1pcCF5SDcBhDg0d9/NEfJ3mJIdPU0biJocwuhRAWtDqZyLLnCvw\n", "fPz0AakoM22ScJUb/2ezpdXgQNwDwLS4l2+F3rdI/jvcnPcd1vt71WEP2DWratswLVRPSSkG2yO/\n", "lqa17u1h4yhehrIMDhyXZuknBaGz/ZhV66uzlaX3jn7ESINrcSgbI57W2HKsulVJAnQ/+cZ0M3F7\n", "ZyAZj+hpyJpUpgieqia+ixqwvTm4e2AYILE5AOASszvBV2B437ewbpIBMQW+tEHox3Q9Pdi6zNtb\n", "U98ka2j3AYLCifYnPE0HUKwITZztTOnfL4HzhDS2eeSeLlOLSA3xb+We86k19450f6C+sa0ATLPg\n", "RA6n1hw/0j4NZT+YuTFOd2vKu8U4oBPy/JB0wkUq/7HdBogVkr5CJ13+MDWoA+0qdbPA4JLPw0w1\n", "ukoYBtoHM/8szF0jTFHuaHPnxAals3pYTfcncgM79qXgIbsgq0EGdTR025ru99DABWeWAgpGbid/\n", "GC/Skeq0e51KDM2s+ZbCziHQuegRe46Nxok1cu1qHVIjrd3qv3hmeQ2tR8PcbwqQZ5g+XQcpafmX\n", "5bppsTUYIs/OstGvbu5JI/0geF/u9Jf4YYo9055Xs+ZLUxuAE87Qo28lw64T2JgyFxECJT5oo5V0\n", "8iVpDOn/nd2Jinx21Uu76Ts9gDXQT0mTTC1Ylld6hpHE+WVel7DEvc2Ep3fQPDdf/YNJwBRVaxvL\n", "5m7kP8JBZtZ8OQVVCEfTcgPPj6dwnn8AjzgMLrYB9yJlXzmbsFND+ydWmvsE0Mq0Toy3mqDF4U3f\n", "1632STV8bzBEkJJk2sCgrtLlI7P2bQn0rSJV1ziyg/AfHOuJxHUuF4d3qq6UeAA6zcOU0F1Z/38g\n", "8QDEugYx3xVOWkPl5quc0Cse598iyf18L/7GjuYNEkIVmpBGNdfNiaL9fRmOL8mgQMP4h5vkGbXR\n", "AfcJ1fTxPv1xp5Er+oT+3M1vxCWk5K6IgFfW/NeSc9Jny+xWsURCCZkR1ZfVssw0/6kPKdTuVmQx\n", "VCUzAi0y4we4yLUm4RFodJFg0nOswtr7RDBp9Tl4zmP+jaCOM81q0HL0gtqT5d11KNRpQ1rzynwr\n", "2xmJaQJs1V7EpzOqekCEjBW9dPXmyFB6KwFsZ4qqDyunRO3ZcXLXI7h+XuOKMwpLreHZHo/ipZ4L\n", "c+jvXWt7UimstsYbVq6Tmrx/1G0e+w6E1F9a6ttasS5dQynGOhDQkiBWbKIvlA261DyE1exEGyzn\n", "tKEH6AAAAycBnutqQ/8AAUxaYi+gA/o8vbYTdrsSFdsMBVAGxHTLZp1Nja6jaN4EiM+6Ui/SkWRL\n", "w/OyLglYC3wlcXSrPz5yV3DUeDhS381yi6p5eAPIwM+Lv9vXmdQgR35yisp5lI1UULnlHiPNXDpf\n", "k8z7wdkSqTLXMAx/fbjPJcWLp1i5sXqPRYAWVuxfXh35DTI4nFrfnLfeJs29rFY1VeQkftecDe8R\n", "LsIy2TIAi2/gcW1VTqzaZ0dY3IkXroHgDP89qzQ2dsAdKfLMknRRqlHSoES7AJCYY6TrGCHa+u+V\n", "2ISKYPeEWbphl5xEaGcw5TlWYjS+P6N0S9deKlW8owmY0Cbo0HaozKAWJvk4m52a2nON7rvTHtfJ\n", "yGiou93Iqcqs4F2wzL1POuKSPEsNqSCc8hwsKMAgEGACSm9FQLEpz65lrMMVUKGmsrJhLGvfrC3n\n", "xNIUMGuE0Sdiu50K9qJwziDfqwRhGPY8oY60QKlqc+Q5yaQd8AmsPUsFtkCymSWFE4M0QLSexs0Z\n", "w9eBoUL6ZIf6zVH0SnhkABXuI2ta9zQ63csF6ByEukGSmK7wp8OcILRwGFqm0LpyeyLKF8Cm9Eio\n", "PQCyIqqVZwwyO1aUt4UNAA+KhKr0vSe8MIhWnY32GX6DpwdmbKv/LsxGEewrS8QuP2vKoyeHK8to\n", "GHL9j0DcGPItLJwHe6nmnQq7/WT3zEyURCPB1i2le8h80rawE8kYsLXAGX5R7lDdvDJxLrtKr2wU\n", "IapCpazz3Chaxu95MsUalNWkV9EfPpaWgISz+vFF5vzGi+if+9pVh1Q7tDObnoiRDO1UB0noDHTm\n", "s56R7fEYTzzYHAM8OQwJJpIwbvN4I8nZzWGJnqvYp5fEsdVm9LD3xIH0LQogq2E2iKYddRyz2rDG\n", "TAdPfDJUwVAD+ks1sDNp37SD/8BWFq8jGZYKBDJGCqojD3dezJ4fO0dJca0bup91EODF5UTSuL5z\n", "q/msUMnlzmu2YxnWxN33FWXb3OYTXeDE1ElT6XpLR+h6/2EXQH8q7c0p1DOUacHR3oGEaUD03VkL\n", "kBAlZiaTvFoRCyZTTs4NvuAAAAfxQZrwSahBbJlMCCf//rUqgAB3TGvKytZ5QAfURj+AyUnT3U8w\n", "PmWu9eL1jL/RxL2v6tXA/4jH8XcGNQamkKSrVhIZ9g8FnFnoU10zkArf5nJye4hFWSC6tVzn1DYS\n", "ET315CygHpmex2zXxjlUGUZpQB0TB4w3yZwLxoEIxH8/wDfN+rHaGKIzdtImIGk5FMxRSyhTWYJl\n", "xOfTqmw9cWbBCpA6XGJb7IlxngjNE/IcI9rcdLjxfq0oNSxouth9YvvSlmF83SrbZmFxl1UsSUEb\n", "6KOgW6gAkS71xtDOnpy3Z9Funenm5aNteV1LbNj86JTg5tmoH9gEISPia5FLXvevzYPOC6HhfK9D\n", "ysSQg6I03LSuCgQNrqzncml1Q7RQv0poBuATuEhqBfE05TCaLx30lEKi+6TLBEzXm3H6AzI7CuOh\n", "T1FKAZGuOfje/zr2m+bCuqJZ6v14kHs0uHfH8J4JPLn6oFbQCAJ5WZAfZvMl6zqAXjK8zCg3ry+n\n", "h6lQKyUSPKPbWUxTh7/LdNqOpg/YDkn3z03BC+fQIvt54LhRWp3UwUtiyXg5SHMOoLwcQ8yhgSRM\n", "x/QOeoMaITUyZxTohhELUVEYJsZC54GhX+Rpt0xblmTkm7MM/JTgrd7BzV3KjbEWNUQYgU+5JM3v\n", "HCVu6Je/cfSxZu/n69syvrO9LiTKd5vDEkRWpH9oawm69RH+hAd4TFA51RLhUj4iTzqOH6K+zQob\n", "6vi+c2nizGt869l8nB0T4sbtSQ5qOfBl8wyFPo/2Ee4AdltgeEJ7zctrmGxVbXF3Ov8sf8Qf7If/\n", "yw2tZgfFYJK9SttXWATAp9f9j2JZUSgM6SggbA4B3PJ3zgVvE3El5VGxp9zaYFXVKzzn8ckmM3zw\n", "5ZcMaRmkdB8QsLyO0ANQPqAP97n2+lo6GWbaW8Pmc7jBn1g8XZHr0mVjKF03Jxwv/BptGuChFblW\n", "THVuupItr3dN7drb11SPMtlrb4FCDg1uh0Bd09Vvz7ZSuuFi7TymRvarRdI3eMwtg737zrWNS6LF\n", "gYqrP3dLVf+5Rno9vJsuxca9rfaApR3ZEsF1HPLsSUVVDOCXbkBNhjuX65hbLtjIqjj94Y1qyvmm\n", "rJb3Lx2FQfbbkxzms5i4R4hswiY1MFCD08/Hx6KcofCg0y/gBoWtn9vZnQB0WTlwdLhWtLDO9E+6\n", "5uA1Xqz5vo6RItcmPY1AcyOd1zU+T8v7iyO/RbNpqPNtxmOCQYjn1TbMSjFwImIdZpiBBPCzp9u2\n", "58x03R8N9NX+IUBET0tF4UsJyaN3SFQVp0IxcQyWAbs+ZEBVxPtFCGIkGgdHoL5EdEYuDLjpMBUs\n", "vFSmTBzfL4NiXOuoCluJyUTnmqtmsQZhYLZifUWiGPRjaUucQ0ejrJj2uM/K1mJ9q9stlgsXkEWn\n", "P/AexmYq7pQFvHoIp/G3alOaaSa82gRpQzd4mvp4itDCa2tJDDXgNtZuZzM9BjeCT4vnE7jsNFjd\n", "P0jnH4Bp0yJSNpjyFinB/UwLiN9NRjhixpq+OVWkUbsrgxvqpjc9+pHMp3iEsCyQaMTQqYmuWQsw\n", "mNvbsfoEXOvqjmCFnfa+0hZr9gr8LN7cHNTpwwbdTLoXvPM67lDCptWOe9YPg6OaekJAMGZxYUEZ\n", "js6Pc74g9VxEKh/Bl6GSwuv5i5kqRrnjB+BAf+d5sDVKTEXGIYypRfTLn3OTQki4eR8AivvPFH2+\n", "teli0PjWKanbq2LqRlV8TEhuAyM85se/1XQrY6Ad9DQuKjFaWu3JUMmUnrKo9/wafCWs0sODwrWf\n", "h9RY7j5/W6azS8e/XToU5akFZFiHvYc2XHKaxouajeEqIOKKAkO7acOCqPxwLi9jbfHTWoJ3rBah\n", "88YugDqokl+edrxVqFyYC7kk0jfjqSm76cuiOacZGnx4RE/h0Jcj0GYXws1Ccs9Htqn2CJkBY06x\n", "WxS4+rPawlvobv9W/XJwh4JrBFQEpUFd3Gss+hS0YRZOQFGGpB5egDBydVU5wbJu/p+2WIy3oMMD\n", "AQDRe1o+xDM09pn5ARoSp+DMg7XqruQ5TMdeO5O7Mo0ytkGsyaOToYOs3JQQTvVkkl+2ybBTgnQM\n", "mEfEDa/JIJh/BsWu8yxFpVGyuDn0LNkUH1KCrzg5ZcSaKuzfZtQOBRvuZj2Tuw4k6n5cQtP7aJ7O\n", "2wH+cQcbTl8bqx2ofsx9yyvN6FGfgjPc4b6af1D8700rgeWZyBTg1fuhbGF41frsTaWX786XaLxZ\n", "RoiDXCQPvIdO/++HdMTWmqqfEfFCZni83aQMjyFAXIB53cfFBnhVkLwAKqC074WgN/E8czcso1x8\n", "vpplrBQnULV3MtCS9Z8ZD5aa8rLP4IdLWRugqk8yCWO5maDNuvIrw7fBM0DDrNQijrPzbghFu63f\n", "kTl1TGlCwd9IZL2XhtQVbUicqfG0u322fB/aicWWVfFeK9XEnezSNyS7vj3UxLLxRmLRwdVSzj5F\n", "U4b8hEip7/rQKDLGzENy6lKQ17qZ8ZnPC4OrWMCnqqXooyJGQxI0JPwnxAmHioPV9i0750xAG1lB\n", "xU4veWOC8btydHPc8k9Z6eg0dQW/3uJ4K/Qw5EI4L3c55CbU4wXVfsBw2Ny9VOmmApNCisdNMHUJ\n", "ZcTsAzxxQx7M8VBJpiS/g6jqUiWkwO+0GdQPthf/4xjH3xX/ju+m5EzbrTMwZskjFXf9hE6Mu5kK\n", "W0EAAAOTQZ8ORRUsEP8AAJTj5jVkUAAWufQUKTmPkVCc0K6n8Z+DrDti0Q7NZsJ4jqE8j7HKX3ws\n", "TI05xypvJORyLjoEeVZD1+W/zPUZsjGz89ycqBBKizo12bCHCtX1Kzej/BIF6HhvhPOXkICORk50\n", "BNTFb8+Ak30mapsBsNwpAjOIjC6PSO72xtEiay3tbzY++ZawkUlFmaQPcC9asd7gfGEYzQ3HMm+0\n", "MkdrCYyP5nE0aiiVr1Pb/keCTlg+yxCq3umakJUXHgVJrDDYk1LVcrUIFSf4El8NRVuQuNdgG0j7\n", "zjsqJKrZUl+SHXrCn4NQxlp+463ITrXdIhuKp57r88UTHsrB2CzA87xTb5cH/2rQD/yPULv/19Nc\n", "cZEwCcTlTDE4qlZ4FftVn1w5nkDy2mkJFHs4cNFTEwKNYMe1i2IHOGI2YhyyViBDHqUiNgAMMKNE\n", "HoQcjukDq8Cw+HvssixEoqS6dvH5LyOioddfpoJg7V7dPibjR756QuucBnlfsndWTdSAaNp558/B\n", "iicCY9TQpFAV6Y63E/WvvBVw2a3EdGUb25YkXiUOxalpIOzB9m51VUhphLE/r4wCjc59T7MT045W\n", "6/eTu6+myzI/rXP1NOUOXv55CWJPVKwPIE7DJ7NWDFoWhcLSAbv3XuGzFrDWXGdRV1gF2VEE0PeC\n", "Jz7jHNYN7oOsdBVRgVtDxu096V/FFEVwmL4Hdg8aoCui1ud4v9k3ecj65nXQ2e+cp6PVJ764Lg2+\n", "cv1cOaRuAJcN2wXY3K9V3YXoBtQ171X7oTH5qC5vx3k6V0SUF/AWkO1To46vXgXWGR/WBc4D+UX8\n", "iKB/aqavKlszZVqrq0+poqOxdnYgBYWyT9KoDDofxdbDAUteVuvwMd2MDduXZ8Y7PJMHAYQ+8hNg\n", "jazrlyS+JFT/AUAuaGe/YJ0SafeRwn9QOIT4SWkCxG6QcKHKU+3disAHs/fdaHG1nM2m8BNLLNKm\n", "DLHCtXjbd4dJSR7ck4zGQWYSM8asDAcQN+TdWHQllJUHLGgJfcy39PpC/OHLh0h2BczV2sNhcqE8\n", "D8v9C0bAma4FBsnepQLWghNa/cEleLHf/yYwAPQDlqJGrUHDymCq5FZwlNMLfY2eX4hM8FKfG+K8\n", "eVP7/lT9k44LNErprBlNugaaqAjaLUbQ5oDX/hPqsoLm+GgnhmC2jO1pRWuZACYBz09nCy0hUP9+\n", "JkqNflHe10DBAAABzwGfLXRD/wABUFPLwAf0DZB/nj6fmc95hAGSTZ1j3B9cf/M2+JI4bN/DKGGr\n", "mXrG+NZdqrX2MvJ3D1bjGi0PCTbj496pLr4r8mlHLo8jhOm0cdbjJkPzdk0IKVvI9CREiX92fXVI\n", "PZ9Jc3Au1uAAiMLCQHwkaj0aG+nYOgFfE5CXdzhYHskFmJJYK7/GBGLg5celFM/HqjdhEAMa8m1m\n", "NDl4mKxBNq8iMm1rDDA0O12vZeQbSkNS1H1XfBjq88hQseTip/Y9BG6l6GHDymBudxNsOv6NqyYO\n", "+lac0pd8bt94IxqcEHHU5q3A3U3BkefxKDObmq660ursomiixDu0IpBQUzD4BtwoJo9IDwaQ6sqA\n", "4FR2DueEIWo2XL1/bFB2MNwYz8RTpVBl26sidqlWbluksMJq+10WVzEPI2Kra0Kvt5HoT4WSVwfk\n", "0N+rc+GLooR2feb710jlNlO4alHx4gtIbEk7Z6nWI/XXKzTpThZqvjXc4ev4o/V1Cij3Zlmp3ZFw\n", "FZ74rVdSMQsq+zd/OJ/FalK4bF04HSAjsYiA0EM4c2MCv9AUBekcdBNsgYiAi+VsULeMRhgbZP/H\n", "z9P78IFrJLxOV+Ev+mNQyaWxSesAAAJ6AZ8vakP/AAFQR810GAD+jzB833WiLgJMxBETpuveFQoT\n", "W1DQuPpCrJ2bzQAXXMSL3Q3h7/HOjhau9NdjwszfvCeJvVo7y2QY9bABKJ6pCeQ1gt2mQdWWPo/4\n", "a3sHZFb0pIyjxhDrJeTajHyAdePg/nQNEmWG1JGlasZR6JNCaW0d6zFNbimYuMrkiYBt+o21eSVk\n", "NoE4Rhx2u/2Q1apRP8CxvWsLqCPzUfC20wnTkBdiuHTL7cTyjl1b/yh5KSc94sGLBGTjaWr9mwkT\n", "1Jg0qY2SdvLM9QzcdNJxjvV4JbODISO7KioHWgPWYQlYsVMdgwxuo7EXC0Tn+7Z5JjkbRGpUIwnY\n", "vFI9gAeIoWlOq0HAATckRX1QFtFxY1B8oNWeiDlAfxL2WSoD55vpFfy4RfBCFaAr8bzi80zryjl7\n", "C3J6dWLIuoMIdIdW/zRBVWOScxH0q5jogM8YHkgcuhXooHRhbbPt2dp9Za0/B8uqRmADMCGxhDaZ\n", "Twq4sMoyLX2R3UM5xXi69hTzo6gbhuzAAQWe3VT/z+yd7AjQHhiJ3qeIRtwLb/1GMldpP0h7WagJ\n", "dssKCIjaFZQk1CvByBf8D1+pM1o/3zn8TNPvENDIogBqiprRtJ7NHvTYL8/q12svuGZTHVZ/gRd2\n", "I6cZwM/uKEiDCT/YfyHT+aLjVwp7089g6BOlZib9uoy4cZitoUvr55NRZJxSz/mnf0HUjQN/lg0f\n", "+eo9OPMpr8INR0NYLQICfUfMb1M4jSDuF+ex5BItqnWAC7gosGnALEETmKf1iuW/PY4eG6Bfnorp\n", "pWuAGBQ8R1YLgaOCWcNrwGjjstouP2hEmKIzz3og4AAABV1BmzRJqEFsmUwII//+tSqAAHoc6VQq\n", "suWoAO5SZxJxH2uFmZo8v0EBfgafi6qEhGA/Abm4180WlthDR0v2ElGhY++S/1CetLOqBimvZHWL\n", "3zowTzY0TfVuupBdwIsta7BLtkhdIzsB2DN6rRPfxDbR76kqZCGHd771rnqIN3+qZYboXd4Ctj/+\n", "+/qcODmhV3iiCGa4FFtJ/cd4Mbi42QcoQGaJyS4fFIKwY9NABgzSxDv1yy17lTSdvlXtxEDRcPB1\n", "avJOjlFopeY/7qX4VImHdA1+bY4qhJQnGIJAPLolU5hkJc3Je/7p6hsZAtlvRPJE7e3vROX8mOV/\n", "i8ognpeFFGxuI3PiSqJ4+WosLCzt5wZHQ0gmgPVZvQUNv8wywQpLdzO0UlfYa+YUWgkhQmwQvW7m\n", "0ls+TEe8Maei5WKE3lEojDZWP8S1NY5urrZASVuyPzq23PKSHwapzAM4nwjv2JQl4CcZr9FqcpAQ\n", "Baa6ny9GQLLoOGkzsDARKIUc1gXOEUjdBRsgEazyWUAR8YOUp/LOQOWHBMFrqfUlpWWEOGS3i8PM\n", "r5xPZ6XP9ZGcTcXgOKK76Tw4qHWzIALMmXyp3ov8Lfr6B9kUc49N5v0rQAhcjhWOI7oXk6AmBwCG\n", "ewCeHr5+AMR0Rdv4+cbg53tmuMCy4SAzUxCB5BNi6P1ha2hIJj/3d4gTKzotOS0BBbf+i0PEtsm1\n", "hp1vwhOuEFh1ib++JK22q5swC1UwQLLkXXpTmjTPoI+oH93nssTOCpoyc9cqAXhJd0J6Bq0DD1o/\n", "CSt5AdjkdnoNDCd/amyVgwxG9NKkwYJzeKuM31wctVOwEekfM+fkbbp4Xx17PUDBnF5kRNuNWTOn\n", "BAs/Utp745utKrKBa4caMTGwwzX/VoBPPJyq1a7moY5sGnAMduGq1TFFmOZPU6eIr46szNZVYZno\n", "DlReaMVSO148cZK+CDOQvd9R8VdDPCIe12OwXANk4CGSaCl93HVNktb5APHufR9wXJD2f/4zW78o\n", "96U/LXGyK8MuzSglwyoAtEOjUrx0h3lAayQS1dbUK68WwtvtBIDNEeGWvIcZNcEwlRahX2rCDnVG\n", "TcYgobyE9JWjnpfUu7OxOxn9djFPs/YdF8+wWU533FOUspSn/DevsH8pgm06ZoaadKBMqZtBHrqI\n", "M88puAg15njqDU63nsMumG0m3v809bdDS4sgP7Ruh26gUKysSnyIwoGqtrTl8vlUIFCvRCrehv/k\n", "NhBe2w+eF1UJjedr/sHUWpbeksfbabcJT0MJ+Tm3j33irokEIwlm2y4AQU77iY9h+5ttZYTbgn8g\n", "1gcXz8lpfSHUnkV91BhHPzBuU8ThsEif+DaKeG4jNxSYcqNehO5meA/zI0lE1N1MvlG9mei4iIWO\n", "t789ax6YxKYSu3nZkeEPCZvosJ34f0p8sm1dbBL4V5HlvwQtBj+7GQKUYJaNrINupbD5bl5WWI/7\n", "aQXCUpfQP5+wxmCq0yNQ8eNSbdljYxuQ8HlQvBmki56SCpM3hHjsMA69zE7hk0BoBWvUQ0N7vlKs\n", "5VZ09rt7IYXy8d6urxvoNC4emI8W+92wucjW1m5dnaYL/7tINjLmhN5sdMxlExnIP06PWTfooDCg\n", "u0xzhd+iHm0iWPfETRFNaE/R8mjYv5UCI6W2LFO1lgkOte73T2pIVvMWP3HHok2hT8KJgJzZ9S7h\n", "zPNipBkaB2h4DF0xCk+9RDkUsLJAL0SCdtop92oLMLKPQdrVa4ZJDWdkDax+1szJ5qZgkQu7m4fB\n", "dfD+/Ril44wuPyvxmc2JpvRlvbkvygLP+Q+kwwQawsuPsauezqDlgAAAAsdBn1JFFSwQ/wAAmrZI\n", "hrpyAAF1Xf3OpcDtAiPUCrV8Si8WMseQKtmjHDVYHNCg+pAK/mdoMdjmKjlqBwltj8M2sJGh6x0+\n", "wl4s3XFpUDtGU55yOQSwdYGFcQ96R2VcH1/XPLqZ0dPxhrHAiTstu6P5wW1p5efEQQz5uA7GqRnN\n", "cRLZs3OL4CVIiHk8KG8qUNo6t+8NMiZ27rffMz4vbwUqJKU3+0y5Xc29tibkKWwMpzzgFFyhPTCp\n", "8q67IlkjAOfMUdi+uVx/YV3E0Z6jGjAWwYY/FfQ/dr58U6QTjCLN/6cDtkT6aHvveBMznN3up+TP\n", "ftu1Ch9SW70e0dZJj30+vIuHlwE8NCZXocUUFVGvac9QPjimVvj4sNm8G1AUnQGAxzmv+9/KvMSY\n", "o/Oy9le/8lSZ9UaQHgSdWgne7UVqhQFvVXkEq58PUMcVdeZXqCra4Y5tfCwzWKbwg2OKmQ2IWZt2\n", "LHJi7Fp0OT0/BaH85J6ecxs1BUMtyWYD8VTOi5vZ0BPUMsy9DdhGQVIq1Xru0WMIpkJfOIs3pN60\n", "2ievTQAlpN+1F2HzAz6Jj8pcm/nWY1GN+LZPMIlkL7JmJMUOu/eaZfmBqr8bh5edG9kKtT0W4frZ\n", "6P68s2a3TAHZ09Iu6VGLUv6Fg+23UjCoJNN0sDBWrygJQ3ylO+gm2XJR7tIy9grmlzflmQcQWmkX\n", "35UMCvm7sOg1ejRriSM0mK8qVOVc0vL4NMnNgJ536xUPAa81K0UbgUOVv3C99Cew0SmXT9rHcnHY\n", "59wixk0iRsDNIrk8J/pVW+PrXx+8yAaRy6t39LEXTAFWVyyOlWFmZpo4B6HEB3Xr3k3faw8F4OAd\n", "0xr1NRJtG+jWPoyytse5MVBLV6paMf3tbjz8qZa/NuZoCZBY8Z62YOJZCt94rNL4U47Ffw/eKoXM\n", "RTr3NwjBOZybPCd/9IEAAAGLAZ9xdEP/AAFZFtG3r8ujwp3BXABNXsBJvh7CIFcl1j4QgnZHf00c\n", "Hexo7Ear+izPqa2s+rUB9OCuqUwwzrjaSzjG+p4Dr/WKxMwT1gBye6PSfUZgbQzcggnBgzaLOoJn\n", "uRpjd4Ue7zEQ1b6u8so+7+5L/j8Jlwgt3pRr2FGK6ygz8rzB/n+U5FV4NaBQZxWuiUZX0p7sqQB3\n", "OadXUqrhP92ZmtSMNdQZSPkzkjumHdjkWOLUzO4OPuWki6pXKvjYtFepM6F6HJTGEvk5InnHz72y\n", "4F+vWGga/TqPdc+chhsH8J38B7Kczcb8Qi8KR3nzcqAhtS7rKBaDFcAVD3g46I333Bw9IQyHAsSe\n", "3xUZEVKXSAOKhw72yq7gy1jtxCK8fFNECyX9TgMDkprnA7BopV5V4jHlmc7+cykDlWrWroqTubWJ\n", "fzUTLUgbWxniKZwVhhLTELTsMculdw8YvjEvBN6/8TB+9D42s1lzK0oluQ/euO1xZSMYwzeCxcOX\n", "RdnNG8pskzEuodMoAoIAAAIQAZ9zakP/AAFL2pXZ8pW3JACVV7SSHOC8GW5ikfTjGQ3fpsmSSMN6\n", "7SCz5wcPTQw30Q9JseYlBb4m4v83JIGTpd0+K7U5sF1ZDEBeZjCo67arH+kbWl+DvBMiFdD+b4A0\n", "58TWrBDeDxyJw7sBarbapdWbEkBAr2HubGGKv7zCfDa2DHIB2vmqZXHBaDY7w0aoPYL5qMb2TLB1\n", "CCnZheAfniYWb3s1LQb66TOiSI+288z+wPskyO0wWYS6AExdyrojUMNnQSy1oIic3pJ1YdGoC0dn\n", "8+7iqCPgPF9iplbBZYhosjKi6CK2fdgSQBFOGnh1FvWDs5w0t0TvhYIuLF1Xm3MOLF6lYZkJPFYp\n", "sncOCiajyns4UOpElOF6TYRb8em59U4HGIpmphOEO+sRTQfwUY9/FYuoQob02HQN5aE/rDG9oNVD\n", "xEnV5H3NuueYJ1Y1TBrGfk6YOWiS2h4KNRvRS90sqYIiwmgy62KfN6JtumYnQg27jY7pSf5dyTgg\n", "rVryia1K6Sx9DyJVYlv4OnzTBilWkqK/7676cWVkwPTzUjN6SQYNWvAsA6UO0jbQkXbQNSwG09sh\n", "6hL7RoFYwdvi460Y+RksDgccmsFQMZ1+ugJuIjsOeXy5tRnoFwBIir5aIqMPK3DQMdWYQPeUQNmY\n", "xiFF+SMHrgLTwfTtjJMoATz+tFFRnIkI921SobQjQOKAAAAF80GbdkmoQWyZTBRMP//+qZYAA7qy\n", "oXasIAP4NYE5WG82WaA4b8jNKTmw3OmQLQIYJj+9fxgUb8nByu6+UtZebUDzAscWbLSEflSNnaM0\n", "2EDQszIiPldxuZfrpJmzd7zWFmAaeYpoeVH+QAGyL1AGo/DogNiP9dEZW2AFegN77SkzyUCM6xTp\n", "Q7fesm0EUt1YTi2AL252Pequ5HW3MZLhMuib0XXg0YC3UN4IdIJkhENyXknZVA10DobNY/SqpNRl\n", "F6qt3g9eBgetVTVhP14UeXb+2YzL5Tu6Oy9LhkJCpAvFL7Xxme/Tz6/uC53Tvfps/ZJnNQspkys2\n", "VPEXH1Y/8AWDq2YxQmSRatovF7QzLCxaR4tP+82MjgrUF2oxUNnwV1DkrITu6m+uMcm3JZ7dGwT6\n", "xe4urvKM/CTrY+/BIHY2b8yFhbNJL6TzYZta9u7OQFT2FIX57WUvmE3DVhQtd46guYlN1zJZtwng\n", "anDxlGhQms2iyN5AXmSJeYmVepR2YEoFTnpnT9gMMEcuW9mgIu1po9vWs+61CJLm+0RYsUZzKcB4\n", "DaUSXr9zjmdLz/UzrnJfpBJJGbUtpS2Cqj3Gh9FO036izJmDB90Nt6HfsGqNN7YHOOQ+px+91lrF\n", "vB8W5Ii5E8x3G6x745gNwDS0b/S7tTs9CweVHGcqGd60baCvK7CH5q4J1q4vgQnKOV+/ODE+Gx64\n", "q2w6wNTjdmCWuqATZzEoIG87iMC1oC4KtxFy+HTbR/yMJzq4DtlGDFA5AaxPClC8RwBvMKqH8CmG\n", "/fqRmoqgXkvBqBC48ETLKb1TEtGqjpiknFPo6IUAK+K0j32ajgLecfVWQjlZbV/wyQQaQ7A3kHJp\n", "QklpmId71QS+ajpKKYxiUIS5cdc6sGWlJfsqJlmHERbVAINx2gEmtSoGuEIlzEhfdZY6lw0n7P1I\n", "Qk/ClrgMazEBwJiEGg9obBqtmMVc8Osyjh+H7tQPvKcsL84Y2gBN4DlpdhPBvVaAcfz27IQtcMiW\n", "gZEcmr/JiYqccJGvxmePvBdWqUv2XcBJ+0w/ivTJ/iS0l24X3y0BmI9U+ZmOmXPGpZxCEKa5C1O6\n", "TWoVG0d6Zu386kPKHDg6KujUSmsgwDQCDpf9Z6lBXCjqKbjZTEa47NVpbeJK/h8WNEJuq6IeP/Rj\n", "up9nehpUqsM43ZcvYRJh5TUny2uehKrW1ihxrm5ZKnZTQmCsIU9ckMG9dmt42f53tPlvj/qgWvDG\n", "pJG0GU/VicWW5dB7z7P5esbzCgG2VeoQdf7GcHK0pC333D0oAWMdlvhopYLOtukvgw4e9/nXKdjb\n", "hZ36qAxKzErAa1a+sVAfq9kXD6P0gVQKbALMiL1RgJFqiJdFOK0f3/4pUkzTDL6+biIQSDhFh3ja\n", "KZNtpVH/lVKI8rZmFx4mWbaq8erpK9jLLG/68d49Z/oyHCHDQtz2wXCUFpBuCMG871gqPYpMO8id\n", "vArLP2rk4ZdmFuWBHBCrdJZ4FaUKLP4C876XiF7WV2XEpPwOZqqvamfgh80kwf+w6DcTBLDkQklC\n", "sFASQieJ/IiBxOm3rZ1GmXazUsak+SrKrYfoICCNctZpjGvaOdfyhq9yhY9VthdBuo0EUxgb0IZd\n", "4s1n5SxuxRfH2o/8UznEqGfPOyDPTUHArLVCojKw/PflAgZLnbSjSCq4eDmYGj2X2qG4Naw/NsxZ\n", "FbfRZPcymchjdc8ZwtEc/Ds9/O7jQCuMxnNkWEj1W7qUCCp1LLznmWEXnPRniusHm2MvLY9RAiun\n", "RDXboiuG7OotKHQBB1dU2OmJX7iXLCzaO8oJSLAbwz/eg6jyEA0wmFOgA4NtFhABh0WEL0uFJxXB\n", "jcXh1DJrpV8AA5P4HhijTBFLjxl0OEL27lWfRwsOoPKM27vVSw1KEL334T7b0GcRpXCewQKuPAFJ\n", "Ho+hDe+7gOkZeEitMbA8QPej4Pso8B+yRLs06ibB+mu4AeRQedOqTFkaGcQlgkiyjxgLpUgsnf9o\n", "hZAf9oy5EvVhQZ3KF8SUR9lQ2ou5AAACoAGflWpD/wABWUeANyCQAkmBiKIxmtoBMVIioL8dQ+k6\n", "+nRvYrdlTvg9Pf7oo+1eEcOMHbNodvnvsAEBoDGhTBjyEG+VzdwyZ65G20HQWXgS66T50Foio6pi\n", "XZMKU9Z7BfiZAJjlne7iDG0WnAkX1jxiqsUT8ltwC9UvAUsleZ++zrlr23uIhITUXy5USDEeb+hw\n", "D420pd24OceICrYUaNNOYHIKtPnuZa6YUdNp/8QURdVm8Jn/tnFAg5P8LEyJTNVU5Oy1cU+k+7up\n", "EX8+8XOxKLt7tCUYrmLqYy9OrB2D4/gv9RqJJBK2Y5heHCEBcyELtK3lKWdJnDjVYtbCDj+p0S97\n", "+bqdwMHngqLk7Oe9ep58tsnPwGYYNOSWzVXIWmz8edfbJVhrisMSp3B0NSoe7Yl73OoffUOi/HiY\n", "kZ4d+tfaXnKZ7uXGrQqpFPXWEjhsyP0Hkcmtib9SPNhEmGMDLtNJ1RQSHITo9prAEkyzE101khlk\n", "jVCu+tVwzijcEOPFe9SLVNZyrNqpZZmhp/o9yLQVTA8j2X0AlZZg3e4oaQRrDDhE80H3SauHDUyi\n", "v5hM28gM3qT9oF8HdHOkJk3tUWTy/Sk7qM/HPo/kbZQorAx1zvScVWBuDcj5CKKr44WGnpwVWsEq\n", "sNTtUyrsVZkeTMfwWsXaCv5HkC5cQTLf/OsgHuURduC9pSfeUf/NhINsQiENYT8G7mHuFlXQzjKf\n", "ctd0DS37XnytS/Mj805vZGdUkTu72JW1DIVgn/ySNFQkiXvMJinRCY0ld7EH3BQEB+nr7aPFMrIS\n", "GtUj5AYadFj9cL1VQVujTaNHwvHTUGN70meotHMGDLUZY47tINsQ1Xr2M6qiVKs74R/exciH2317\n", "4zM7NSsOOTC5Ik+dxAAABEZtb292AAAAbG12aGQAAAAAAAAAAAAAAAAAAAPoAAAR+AABAAABAAAA\n", "AAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAA\n", "AAAAAAAAAAAAAAAAAAACAAADcHRyYWsAAABcdGtoZAAAAAMAAAAAAAAAAAAAAAEAAAAAAAAR+AAA\n", "AAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAEAAAAACgAAAAeAA\n", "AAAAACRlZHRzAAAAHGVsc3QAAAAAAAAAAQAAEfgAABAAAAEAAAAAAuhtZGlhAAAAIG1kaGQAAAAA\n", "AAAAAAAAAAAAACgAAAC4AFXEAAAAAAAtaGRscgAAAAAAAAAAdmlkZQAAAAAAAAAAAAAAAFZpZGVv\n", "SGFuZGxlcgAAAAKTbWluZgAAABR2bWhkAAAAAQAAAAAAAAAAAAAAJGRpbmYAAAAcZHJlZgAAAAAA\n", "AAABAAAADHVybCAAAAABAAACU3N0YmwAAAC3c3RzZAAAAAAAAAABAAAAp2F2YzEAAAAAAAAAAQAA\n", "AAAAAAAAAAAAAAAAAAACgAHgAEgAAABIAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\n", "AAAAAAAAAAAY//8AAAA1YXZjQwFkABb/4QAYZ2QAFqzZQKA9oQAAAwABAAADAAoPFi2WAQAGaOvj\n", "yyLA/fj4AAAAABx1dWlka2hA8l8kT8W6OaUbzwMj8wAAAAAAAAAYc3R0cwAAAAAAAAABAAAAFwAA\n", "CAAAAAAUc3RzcwAAAAAAAAABAAAAAQAAAMhjdHRzAAAAAAAAABcAAAABAAAQAAAAAAEAACgAAAAA\n", "AQAAEAAAAAABAAAAAAAAAAEAAAgAAAAAAQAAKAAAAAABAAAQAAAAAAEAAAAAAAAAAQAACAAAAAAB\n", "AAAoAAAAAAEAABAAAAAAAQAAAAAAAAABAAAIAAAAAAEAACgAAAAAAQAAEAAAAAABAAAAAAAAAAEA\n", "AAgAAAAAAQAAKAAAAAABAAAQAAAAAAEAAAAAAAAAAQAACAAAAAABAAAYAAAAAAEAAAgAAAAAHHN0\n", "c2MAAAAAAAAAAQAAAAEAAAAXAAAAAQAAAHBzdHN6AAAAAAAAAAAAAAAXAAAhAgAADCkAAAZbAAAF\n", "WQAABFoAAA2oAAAHyQAABG4AAAaWAAALLQAABmAAAAe4AAADKwAAB/UAAAOXAAAB0wAAAn4AAAVh\n", "AAACywAAAY8AAAIUAAAF9wAAAqQAAAAUc3RjbwAAAAAAAAABAAAAMAAAAGJ1ZHRhAAAAWm1ldGEA\n", "AAAAAAAAIWhkbHIAAAAAAAAAAG1kaXJhcHBsAAAAAAAAAAAAAAAALWlsc3QAAAAlqXRvbwAAAB1k\n", "YXRhAAAAAQAAAABMYXZmNTguNzYuMTAw\n", "\">\n", "  Your browser does not support the video tag.\n", "</video>"]}, "metadata": {}, "execution_count": 7}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAi8AAAGdCAYAAADaPpOnAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABRUklEQVR4nO3dd3gUdf4H8PdsTd1NAumEElroVRAUsKAgdhBREIVTsaDeKaeCjcMGetydv/NQz3JgAbGBoiAWQBBEQCD0YoCYkB7Sy/bv749kl4QUsiG7M5O8X8+zj1tmZj8j7O6bbxtJCCFAREREpBIauQsgIiIi8gbDCxEREakKwwsRERGpCsMLERERqQrDCxEREakKwwsRERGpCsMLERERqQrDCxEREamKTu4CWprL5UJmZiZCQ0MhSZLc5RAREVETCCFQWlqKuLg4aDSNt620uvCSmZmJhIQEucsgIiKiZkhPT0eHDh0a3abVhZfQ0FAAVSdvMplkroaIiIiaoqSkBAkJCZ7f8ca0uvDi7ioymUwML0RERCrTlCEfHLBLREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8EJERESqwvBCREREqsLwQkRERKrC8ELUBmUVV+KXE/nIKq68oG18dTwiosbo5C6AiFpeVnElTuWXo0v7YMSaA2u99smuNMxbdQAuAWgkYOHEfphyUUevt/HV8RqrnYgIACQhhJC7iJZUUlICs9mM4uJimEwmucsh8rvGgkJWcSUuWbQRrhqfeq0kYevcyz1BoSnbuLX08bwJOUTUunjz+81uIyIVO7crJqu40vPjDwAuATy16qDn9VP55bVCBAA4hUBqfoXncVO2cdv9R2Gj22YVV+Kb/Znn3eaXE/nYl17YaO3sdiIiN3YbEalUfa0UCRFB9QaF3amFuG5AILq0D4ZGQq1tNBLQuX2Q53GX9sGQANQ8jITa27jff+4XB+rUpZUkdG4fVKu+pmwjScC57cBOIbB2fxacLoFX1h9liwwRAWDLC5HqZBVX4ut9GXVaKeZ9cQDBBi00Ut19Hlm5F5/sSkOsORALJ/aDVGMbIYAtx/Maf9Nzjulu4Tk3l2gk4OWJfQGg0eBS3zYNdWC/uPYIFn57tN4WGbbGELVNbHkhUpHGWjNcANbuz8bCif0w74sDcNV8rfoHf3SPSIzuEVmrWUXg7Gux5kCcyi+vE0qEAFLzKzxjVOrrWgKAf982CNcNiMMvJ/Lrff3Za3thQv9YxJoDG9xGU30ujXEKgaXbTuHdn0+xNYaoDWLLC5FKnDuepT7v/HwSSTGh+PfUQXVec48zqS+c1ByD4u5aqsndzePW0DZDOod7Xj+3AUiS4AkuWcWV+GZfZp0aNRIwfUSnhk+whre3nGpwfAwRtW4ML0QqsXTrqUaDC1DVinLTkl9wNKukbnhA1biVhoKFO5zEmgNx86D4Wq/fNCiu1sygWHMgxveNaXCbNcmZdQKS+4n/bj6BEQs3YsXO9Dr1uwTw/vY/Gj/JBrjHxzDAELV+DC9EKpBVXIm3fz7VpG0FgP9sOlG36wfARw0FAwHkllg877V6b0atl7/cm1krFPx38wmsO5Bda5vVezKwL70Q+9ILsfDbo/XW9cq3R+t9raW8uPYILlm0EZ/sSvPZexCR/DjmhUgFfkstaJHjLPnpBDKKKusNNjct+QWLJjU8Y8ndrbT7j/rDiav6GPU2DlWPxv0yuW5XUUurOb6Hi9wRtU4ML0QqIEn1TCHygka4EGS3INRegYM/ncZAWwVC7JUIsVciyG5BkMOKIIcFWXs+RXSkEVeIDtiYMBg1pyVtS8nDtHdPNNp1de5LOpcDN574GVOOb0SovRJ2jRZ2ja72TauFRWtAhc6ISl0AKnVGVOqMqNAZkRXcDlviB6Lc4F0IcYcthhei1onhhUgFAvXN7OEVAjed+Bl3HlmPQKetafukAY8DGJR3HP8ZMBFWnREAsKSerqjGDMo9jvv3f4mOZbme5/QuJ/QuJwBrk48z6+AabI4fhHVdLsbxsIRagaoh565dQ0StC8MLkQqczC/3ep9AuwV/2fspRmfu9zxn0epRpg9CqSEQZfpAlOsDUa4LQIU+ABW6AFTojTDZKnBTyhaMTd+NbkWn8eKwu5ARGtXk4BJZUYhZB9fg0syqBewKjSH4X59rsSs6CXqnE3qXo9bN4HQgwGlDoMOKIIcVgXYLApw2BNktGJx3HF1KsjEubSfGpe3E7+Z4fNtlBDZ1GARLdaiqj3vtGk6dJmqdeG0jIhXYl16IG5f80uTtO5Zk45md7yOhLA92SYt3+16PdV0uhkPTtH+v9Ms/gbm7PkKEtRQVOiNeGzQZP8cPbHQfvdOOSSmbMeX4BgQ47XBKGqxJvAQfJV2NCn0zu2+EQK+CPzAhdTtGZ+yDweUAAFTojNiYMBhrO49Aqjmu3l0bun4SESmTN7/fDC9EKpBVXIkRCzc2adsxp/fiz3s/Q6DThvwAM14aNh1HIzp7/Z7hlhLM3fUR+p85CQD4KvFSvNv3unoD0EXZh3H/ga8QV34GALC/XSLe7H8zUs2xXr9vQ0Jt5bjqj124JvVXdCjP9zx/OKITvukyEpsShtTZ5+N7L8aIru1arAYi8h1vfr/ZbUSkAqea2G3UoTQXT/y2AhoI7I3sjleGTkOxMaRZ71kYYMK8S+7D9KPf4bbjG3Hjya3oWZiGly+ajrygqsXoYsrP4L4DX+Hi7MMAgPwAE97tez02xw9s0tgUb5QagrGq+2VY3W00BuSfwIRT2zEi6yB6F/yBMn1gnfBS3/WYiKh1YHghUoEu7YObtN3p0Ch8lHQ1DC4HPuw1Di7pwpZycmm0eL/3BByJ6Iy/7v4YSYVp+M+mf+G1Qbeia3EGJv++CQaXAw5Jg9XdRuPjHmNRqQ+4oPc8HyFpkBzZHcmR3RFuKcHVf+zCsYh6xra0bHYiIgVhtxGRCmQVV2Lkwo1ezfZpadHlZ/DUrg/Ro+h0ref3RHbHm/1vxunQKJkqaxi7jYjUw5vfb66wS6QC9V2PyN9ygtvhr6MewjddRlQ9DgzDC8PuwtMjZ3kdXCQA8yYk1XsFbACYd00StNXdThoJmDqsY70NKY01rpx7PSYiaj3YbUSkAu4LIZ7v2ka+ZtfqsGTAJKzqNgb5AWbYtfpmHef24Qm4YUAcLu4SUWcWlQTghoFxuGFgHFLzK9C5fRBO5Zdjxc66S/439r/jiWt6cqYRUSvFlhciFYg1B+LJa5LkLsMjK7h9s4OLBGDFjnRcsmgjvtmfVed1AWB3aiFO5Zejc/sgxJoDcSCjuM525/vy6h8f1qz6iEj52PJCpBL94s1yl3DBJMlzmSO4BPDe1roXm5QAPLJyL1yiqsvoyWuS8Eo911Ia0zMSm47l1fs+7DIiat3Y8kKkEu6uI7WScDa4uLlE7XErUo3n3f995duj9XaX/XS8/uAiAXh5Yl92GRG1YgwvRCoRaw7Ewon9VDsDuKHxKTWfl6S62zU0zqeheZKSBIzuEelldUSkJuw2IlIBlxDIsdkxom805tud+Nuawy12bL0AQlwSNAC0oupfNFpI0FTfr3qM6sdSjfvVrwmpxv2a29d9XgMgVyuwI8BR1RJT5zxR53l3WGvqWGWXAK8oTdTKMbwQKYDN6cLx4gocyCvF8YIKFAuBUh1wRuNCpsOBTIsdtuqmBg0A1+hoSJVO6CxWBNgqEWQvQ4ijBGZnIcJQiFBdOYL0lQjWVyCjLAab0kc3+N7d7FpcV2Hw05kCBuHEDqBuSkHVuT1wWVe88dPZK1h7O8GK412IWj+GFyI/KLM6kFVUicxiC7KKKrEpsxCH8spQVGZDRbkdzkoHJGf9P9NCrwGMGhgCtJACNHAadZCMWgijBo6QAJQag1FqiEJO9booWuFABM4gCjl4Ei/gUF5So+HFAQErBJwS4ALghICr+r4L8DzvqrHN2efFOdsATgAuSdTZ333cYk3Vebq7fWpmGBeANzefaPb/Z60kcbwLURvA8ELkA/llVry09ggOZ5Ygs7gSpRZHo9u7u0Y0Bg2MgVrA4YC10gWXS4Jkd0Gyu4CyqmPU96Ft17sc1vgwFEoRcEo65CEapc5QrD11FTLLGr844u8GF343WJpxli2nVoBp5lo2z17bCxP6xzK4ELUBDC9ELSy3xIKp7+5ASm4ZACAUFRgm/YHBxnT00KbDJTlg1+phDgqqvgUiPDgIsO5CpeUIXJILQgPYtBqcjAhHkcWMYls4yl3dUO7qghJ7NHIrTNj1hw1OoQUg4czhYOCwHTrkQGfUQATqIHTAjoJRyHYp+zIZLbHunlaSGFyI2hCGF6KWIgRyMlPxrw+/wLjSY3gqMA0XB2UgqDz97DbO6v86AFgBFDZ8OFtIGNqNeQ4hIUkIDu4BrdZY6/VPdqXhqVUH4awx7UYCAKsLIdZSXGY4gXb6cqy3JSFXhLbQSfpGfYN3m7oPu4qI2h6GF6ILIQSQvAI4+AWcmfsQXZmPRQCgR9Uva3nVZmeCYrEzsCuOhHTFlbFxGBCkA5x2OGyFyMv+Bg7rGWihQ4R5OIy6CEhOGwzBUYiLm9zgW0+5qCNG94jEnj8KMXvFXs/z8ZoijNKfRIDkhFVooZdcLdO84WPeXv7gP1MHISLY6FmFl4jaDoYXouayW4C1jwHJywFUTQ92CgnpmnhE9bgIQR0HwxLdDw8XmPB1uRYGScJbfTphQGQYAKC8/CSS982ARW+FwdAZAwf8DwGhfbwqIdYciGv7B6LM6sC8L/ZjgC4DA3RVS+7nu4Lwk70byoTxPEeRn0BVDry2XwzWHsg+7/ZaScLgTuEMLURtFMMLUXOUZAKf3AFk7IaQNFiqmYSvK/qiMqIXls4ajSBzIEocTkzffxI7yssRqNFgWb8uGBNR1X1TXJyMffvvgd1eiMDAzhg0cCkCAzs2u5wJSeHI65KN/Kyq4HLEEYVdjgS4VLYO5bcHGw4u7pYZdhMREcMLkbfSd1YFl7IcOI1mPOL4M9aWJ6FbVAhW3DMcUaYA5NscuH3fCRwoq4RJp8Hy/l1xkTkYAJCfvwkHDj4Ml6sSptD+GDDgXRgM7ZpdTmpqKj7//HOUlZVB0uqwubIjTrqafzw5uQTQP96E/RkldV77922D0C6E3URExPBC5J09HwBr5wBOG6wRPXFr8SPYVx6OpJhQfHTPcLQPMSK1vBTTkg/iVEUl2usk/F+3eLRHLlIKncjN/x4V6a9BghMREaPQr+8S6HTBzS7n119/xXfffQchBCIjI3HrrbfiJosONy75pQVP2r8OZJTUGcCrlSQM6cxuIiKqIgnR0BVC1KmkpARmsxnFxcUwmZQ9RZRUxGkH1s8Fdr0LAEjtchVuzb4EBa5iREdUYHgPIK8yC2llGSiorP+CgW6XhtjxUK9r0StpETQafbPKEUJgw4YN2Lp1KwCgf//+uO6662AwVK2U+8muNMz94oBPxumOTIzA9pMFzZ4d1BSzRnfBez+nwimEp5toykXN71YjIuXz5veb4YXofMryID67E8ezduGnoEBsiEzEEXshIDX+0dFKWug0OmglLbQaLUptpQCAG+N64YWxn0CSmneJRZfLhbVr12L37t0AgCuvvBKXXnppnePtSy+8oBaYhsKGBsA7dw3BPe/vrvf1gR3MSD5d3Oz31UoSts69HEDVNYrYTUTUNnjz+81uI6JGZJ34Ee/++Bds1jmRE1+9Uq2jAJAAvSsSg+K6opMpAfEh8YgPjUeHkA7QGSIRYQxHlPHs9YKOFx7HpDWToJE0eGDEv5odXBwOB1atWoXDh6suzHj99ddjyJAh9W47ICEcr0zqV2ctmKYSqD/AuAAEGfS4Z1QXvPPzqTr77fMiuEgA5k5IwqvfHqvVyuIOKwwtRFQfhheihtjKsfjHh/F9gA6ADgEaAy6OH4kxHcZgYLsRcKbloCInAwOHXwtJ0/isng8PfwgAGNtxLOJD4ptVjtVqxSeffIKTJ09Co9Fg0qRJ6NOn8anV7rVgUvMrsP90EV5df8yrIHPjwDh8mZxZ5/kggwZ/urT+8CIATB2egBU70ms9r5UkPDG+J1759ihcqGrBWTipH6Zc1BE3DIhjKwsRNRnDC1FDDMEY0uMmfJ/2DYJ1Qfjulu9hNpoBAJbyMixZdB8AoPPAIQiPiWvwMPmV+Vh7ci0A4M4+dzarlIqKCixfvhwZGRnQ6/W47bbb0LVr1ybtG2sORKw5ECO6tsMNA8+GhDX7MquChKi/hUUrSRjbO7re8FJhcyHWHIh5E5KwcN3ROvs9fEV3dGoX7Dl+zXErNWuo2cLC0EJETcXwQtSIW0YvwNJVvyG7IhtfpXzlCR9bV37o2SY0on2jx1h5dCXsLjsGRA7AgMgBXtdgsViwbNky5ObmIjAwENOmTUOHDh28Pg5QOyTcN7prrRaPNcmZnlYRd9gY0im8zsq3WklC5/ZBnmNAoM5+sebAOsdnUCGiluLTFawKCgowbdo0mEwmhIWF4e6770ZZWVmj+1x22WWQJKnW7f777/dlmUQNMmgNuH9A1d+/9w6+hwp7BZwOB/Z9v9azjc5gaGh3WBwWfHLsEwDAnb29b3VxuVxYtWoVcnNzERISgpkzZzY7uNTH3SITaw7EfWO6Ytu8K/DxvRdj69zLMeWijog1B2LhxH7QVo/RqW+BuPr2q+/4REQtxactL9OmTUNWVhZ++OEH2O12zJw5E7NmzcKKFSsa3e/ee+/F888/73kcFBTkyzKJGnVDtxvwv4P/Q1ppGpYfWY5LS7p7Xut35bhG9/365NcoshYhPiQeV3S8wuv33rx5M44fPw6tVovbb78dUVFRXh/DG/W1itQcN9PQmBS2phCRP/ksvBw5cgTr16/Hrl27MHToUADA66+/jgkTJmDx4sWIi2t4jEBQUBBiYmJ8VRqRV/QaPR4Y+ADm/TwPSw8uhWHfYM9rHZLODpjNT/8D5sho6AMCAAAu4fIM1J3Waxp0Gu8+bkePHsXmzZsBVM0qio9v3kDflsBwQkRK4rNuo+3btyMsLMwTXABg7Nix0Gg02LFjR6P7Ll++HO3bt0ffvn0xb948VFRUNLit1WpFSUlJrRtRS7um8zXoFtYNwdk25Kemep6P6dYTAGC3WrD6leexdM4DyPujagbOtoxtOFV8CsH6YNzc7Wav3i8vLw+rVq0CAAwbNgwDBw5skfMgImoNfBZesrOz6zRx63Q6REREIDu74YuvTZ06FR999BE2bdqEefPm4cMPP8Qdd9zR4PYLFy6E2Wz23BISElrsHIjctBotZg+cjd6nzi6cZAwORnhM1dovO1Z/hpK8HAghYI6uajX84PAHAIBJ3SchxBDS5PeyWCxYuXIlbDYbOnXqhHHjGu+aIiJqa7zuNpo7dy5eeeWVRrc5cuRIswuaNWuW536/fv0QGxuLK6+8EidOnKh3aui8efPw2GOPeR6XlJQwwJBPjIkZheTcs+OvYrr2gKTRoCDzNHat+QIAcMVds2AICMSxgmP4NetXaCQNpvWa1uT3cA/QPXPmDEwmEyZPngytVtvi50JEpGZeh5c5c+ZgxowZjW6TmJiImJgY5Obm1nre4XCgoKDAq/Esw4cPBwCkpKTUG16MRiOMRmOTj0fUXNaKctRcF9fUKb7qGkPvvQnhdKHLwCHoNmwEgLOL0l3V6SrEhTQ8vutcNQfoTpkyBSEhTW+xISJqK7wOL5GRkYiMjDzvdiNGjEBRURF2797tWb5848aNcLlcnkDSFMnJyQCA2NhYb0slalHW8trT/H+TfkfMinXoXzwS3WL7YtP+j/Hh3D8Dei0qCw/gcm0kBuUF4Idj/4E+wAi9MQA6YwD0xqr7eqMR+oAA6I0BqLTasO/gQRw5dhySRoNrJlyDOP6dJyKql89mG/Xq1Qvjx4/Hvffei7feegt2ux0PPfQQbrvtNs9Mo4yMDFx55ZX44IMPMGzYMJw4cQIrVqzAhAkT0K5dO+zfvx+PPvooRo8ejf79+/uqVKImsZSX13q8vmQzorYVYHyHP0EvGSBcLuSlngQAxKFqxlF2TjIaHuFVl7udZfM/9mEzAJ3BWCvk6AxGTxDy3NzByHB2u5r7uMOSzh2aqp/X6nTNvsYSEZGcfLrOy/Lly/HQQw/hyiuv9FyL5d///rfndbvdjmPHjnlmExkMBvz444947bXXUF5ejoSEBEyaNAnPPPOML8skapKaLS+2EA2u/iUSpfYCuIQLBm0ApjyxEOVSGZ7d9BSs1krcnjgZ3YK7wG61wm6zwm6xwG61wG61oqy4CGdyc1FRVgoIFySXCzpJgiQEnHab530cNiscNisqS1t+Fp2k0ZwNM9UhSRdQIxS5A9A5oUhXIwDpzw1TAe5tjNBoOFaHiHzDp+ElIiKi0QXpOnfuDFHjInEJCQmedS2IlKZmeAl06OG0ueCCE5VSGYJhQvvQDvhF/IAj7fMRHxKPyTf/BdpzfsDT09OxZcsW/F5UCUQmAJFVrZSjRo3ytEgKlwsOm6066FSFHbvVArvFWuO5qscOm/u1GttW33fYrPXu43I6PO9jq6yArbLhpQguhE5vONvac96WoNoBqLEWJr0xAFq9nq1GRG0Yr21E1ESWGuHFabF67mdFl6Fbjgm27DJ8eKZqoO4dve7wBBchBFJTU7FlyxacOlW1BowkSejbty9GjRpVZ0kBSaOp+kGvXuyupTkdjqpw4w5F1pqtQlWPPa9Zzg1QDQWj6mPYrED1P0gcdhscdhssZaUtfg6SpKkOP42EonMCz9ltzt/CpOEMLyJFY3ghaiJLed3rcv1wUQ4czmR0QxxOnzqFVKQiRB+Cm7vfDCEEUlJSsGXLFqSnpwMANBoNBgwYgEsvvRTt2rXz9ykAALQ6HbS6ECC45WcyCSHgsNtgt1gaDkC1nrdWBymLJ/zUDlPVr1c/djqqW42EC3ZLJeyWSqC4xU8DWp2uKgg11D1WHXh054SimmOKGmph0hmMbDUiukAML0RNdCY9rdbjgJBQ9BnaE6kHMgAAJafzgQ7AxO4TkZaShi1btiArKwsAoNVqMXjwYFxyySUICwvzd+l+I0lS1Q+9wTfLF7iczhoh6NwAVPP52t1lVa813JrkbkkSwgWgqnXK6SgD6gmsF8z9/8jdPXZO61GlRo98J3DJ6NEYMvLSln9/olaA4YWoiVL37an1eNaby5BekYFHfn8AABBRHgqN0MC5y4lPcquuJK3X6zF06FCMHDkSoaGhfq+5tdFotTAGBcHog4u1CiHgtNvrBKBaoaieLraqbrYGutiqW5IcFgsc7oHYQnj2r09lXCIc5gh89+nHqEw7iYsn3tbolcuJ2iKGF6ImslacnSo99p4HoTcYkWhIxIAeQ2A/4UCQKwB9i5NgKbTAaDRi2LBhuPjiixEcHCxj1dRUkiRBZzBAZzAgMNR0/h285HI5z7YANdCVVlZSinU7dwNCQFeQix2rP8XxX7fhqlkPIaF3vxaviUitGF6ImqAkv/Zq0QOumuC5P67DeJw2ZqKLNR69bd1w6eWXY9iwYQgM5FWY6SyNRgtDYBAMgQ23Gm3btg1CCMTFxeHyCVdjw//eQmFWBj5dMA/9rhyH0dNmIsAHY5WI1MZnF2Ykai2EEHhn9p88j2f+67+1Xh/WaRgyjXkAgHsHzsCYMWMYXMhrQgjs3r0bADBkyBB0HzYSM/7xBvqPHQ8AOLDhOyyb8yB+3/GLnGUSKQLDC9F5bP7wPc/9EbdMRURcfK3XA3QBuGb4jQAAXaHLr7VR65GamoqCggIYDAb07dsXABAQHIKr7n0IU+YvQnhsPMoLC7Dmny/jq8UvoazgjMwVE8mH4YWoESX5udi99kvP45GTp9a7nT66qivAnuObBd+o9XO3uvTr16/OxWY79O6LO199HcNvngKNVouUXduxbM6D2P/jeggXAzO1PQwvRA04t7vo/v9+2OC2uurw4sitgHCJBrcjqk95eTmOHDkCAJ4L2Z5LZzDg0tum446FryGma3dYK8rxwzv/wafPP4WCzNP+LJdIdgwvRA04t7soOCy8wW117QIBnQRhd8FZWP8UWKKG7N+/H06nE7GxsZ7LRDQkslMX3P7iYlx2573QGY04feQgPnjiYexY/alnET+i1o7hhageTe0ucpM0EvSR7Doi79UcqDt48OAm7aPRaDHk2hsxY/Eb6DxgMJx2O7au/AAfzfsLslOO+7JcIkVgeCE6hzfdRTVx3As1R1paGvLz86HX69Gvn3druZijojFx3gJc89AcBISakJ+WihXP/BU/ffAO7Ba2AFLrxfBCdI7NH77ruT9y8rRGu4tq0kVXLUZnzyk/z5ZEZ7lbXfr27YuAZlyMU5Ik9B51OWb+8030GnU5hHBh99qvsOyvs5GavLulyyVSBIYXohqquou+8jweccvtTd7X3fLiYMsLNVFFRQUOHToEoOGBuk0VZDJjwkNzMHHeApgio1CSl4MvFs7Ht//5BypKfHD1SiIZMbwQ1VCzu+iBtz/yal9Pt1FeBYSTM47o/NwDdaOjoxEfH3/+HZqgy8AhuGvxEgyecCMgSTj88yYse+wBHNn6E4Tg30tqHRheiKptev8dz/1Lbr0DQeYwr/bXhgdA0msAh4CjoLKFq6PW5twVdSVJarFjGwICcfld92LqC4vRPqETKktLsO71xVi96G8oycs9/wGIFI7hhQhASV4u9qw721108aTbvD6GpJGgi2LXETVNeno68vLyoNPp0L9/f5+8R2z3nrhj0Wu4ZMp0aHU6nErejWVzHsSedV/B5XL65D2J/IHhhQjAOw81v7uoJs44oqa60IG6TaXV6XHxxCmY/urriE/qA7vVgk3vv4OPn30ceWmpPntfIl9ieKE2b9Oytz33L5ky3evuoprOhhfOOKKGVVZWtthA3aZqF5+AKfMXYuw9s2EIDEJ2ynF8NPfP2PbJh3DYbH6pgailMLxQm1aSl4s9367xPL544pQLOt7Z6dJseaGG7d+/Hw6HA1FRUejQoYPf3lfSaDDgqmsw459voOvQi+FyOvHrqk/wwZOP4PSRg36rg+hCMbxQm9ZS3UVununS+ZUQTl4wj+ry5UDdpgqNaI8b//o0rn9sHoLDwlGYeRqf/G0ufnx3CawVbDUk5WN4oTZr47L/eu5fetudF9Rd5KYNM0IyaAGngCOfM46ortOnTyM3N9enA3WbQpIk9Bh+CWb84030u3IcAGDfD99i2WMPIGXXr7LVRdQUDC/UJpXk5WLvt197Hg+/+dYWOa4kSRy0S41yt7r06dMHgYGBMlcDBISE4OpZD+PW515GeGwcygoL8NXiF/H1PxeivKhQ7vKI6sXwQm1Sre6id5a36LF1DC/UAIvF4veBuk2V0Kc/pr/6OobdNBmSRoPjO7Zh6WP348DG77m4HSkOwwu1OXW6i0zmFj3+2csEcOwA1XbgwAHY7XZERkYiISFB7nLq0BuMGHX7Xbhj4WuITuwGa3k5vv/vv/HZC0+jMCtD7vKIPBheqE3xVXdRTXrOOKJ6CCHw22+/AZBvoG5TRXVOxNQX/4Ex0++GzmBE+qH9+ODxh7Hzq8/hdDjkLo+I4YXaFl92F7l5Wl7OVEI4OOOIqmRmZiInJwdarVbWgbpNpdFqMfS6m3HX4iXo1H8QHHYbfl6xDMuffgw5J1PkLo/aOIYXajM2Lq3RXXT7XS3eXeSmMRkgBWgBF2DP44wjquIeqNu7d28EBQXJXE3ThUXHYNJTz2P8g48iICQUeaknsfypx7D5o//BbrXIXR61UQwv1CYU5+Zg7/oa3UU3TfbZe1XNOKrqOuK4FwKqBuoeOHAAgPIG6jaFJEnoM+ZKzPznm0i6ZAyEcOG3r1fh/b/ORur+vXKXR20Qwwu1Ce8+fLfnvq+6i2ridGmq6eDBg7Db7WjXrh06deokdznNFmQOw7WPPI6bn5yP0HaRKM7NwRcvPYv1b/wLlaUlcpdHbQjDC7V6G/73luf+qKkzfNZdVBOnS1NNcq+o29ISB1+EGf9YgkHjrwckCYc2b8CyOQ/i6LbNnFZNfsHwQq1acW4Okr/7xvN42I23+OV9OV2a3DIzM5GVlQWtVosBAwbIXU6LMQQG4YqZ9+H25/+Odh06oqK4CGv//Xd8+erzKMnPlbs8auUYXqhV83d3kZtnzEuBBcLu9Nv7kvK4W1169eqF4OBgmatpeXE9kjD9lf/DyFunQavT4eSeXVg2Zzb2rv8aLhf/7pNvMLxQqyVHd5GbJkQPTZAOEIA9lzOO2iqr1arqgbpNpdXpMWLS7Zj+yuuI69kbdkslNi79L1bOfxL56X/IXR61Qgwv1CoV52bL0l3kJklSjXEv7Dpqqw4ePAibzYaIiAh07txZ7nJ8rl2HBNz2t0W48u4HYQgMRNbxo/jwyT/jl8+Ww2G3y10etSIML9QqvfvwPZ77D767QpYazk6X5qDdtqq1DdRtCkmjwcCrJ2DGP95E4pBhcDkd2P75x/jwyUeQceyI3OVRK8HwQq3Oj++96bk/etpMBIaaZKmD06XbtqysLGRmZkKj0bSqgbpNFdquPW56/Flc95e5CDKHoSAjHSvnP4EN/3sT1gp+JujCMLxQq1Kcm41936/1PL7ohkmy1aJnt1GbVnOgbkhIiMzVyEOSJPQccSlm/PNN9L38KkAIJH+3Fsv++iBO7N4pd3mkYgwv1KooobvITVfdbeQstMJl5ayLtsRms7WJgbpNFRgSinH3/xm3PPMiwqJjUXYmH1+++jy+ee0VlBcVyl0eqRDDC7UaP777huf+6Dv+JFt3kZs2WA9NiB4A4MhlM3lbcujQIVitVoSHh7eJgbpN1anfQNz599dx0Q2TIGk0OLb9Zyx77AEc3PQDF7cjrzC8UKtQnJuNfT+s8zy+6PqJMlZzFruO2qaaA3U1Gn7N1qQ3BmD0tJmY9vK/ENWlKyzlZfjurf/D5y8+g6LsLLnLI5Xgp4pahVrdRe99LGMltblnHHHQbtuRnZ2N06dPQ6PRYODAgXKXo1jRXbpi2kv/xOg7/gSdwYi0g/vw/uMPYdeaL+ByspuVGsfwQqr347tLPPfH3PEnBIaEylhNbbzGUduzZ88eAEDPnj3b7EDdptJotbjo+om46+//Qce+A+CwWbFl+VIsf/ox5Jw6IXd5pGAML6RqRTnZ2PfDt57HQxXSXeR29hpHDC9tgc1mw759+wBwoK43wmJiccszL2LcA39BQHAIck+dwPKnHsWW5Utht1rkLo8UiOGFVO29R5TZXeSmj6oKL85iK1wWh8zVkK8dPnwYVqsVYWFhSExMlLscVZEkCX0vG4sZ/3wTPUeMgnC5sGvNF/jg8YeRdnCf3OWRwjC8kGopubvITROkh8ZkAMCuo7bAPVB38ODBHKjbTMFh4bjuL0/ipieeRUi79ijKycJnLz6D9MMH5C6NFISfLlIlpXcX1cSuo7YhNzcX6enpkCQJgwYNkrsc1es6ZDhmLH4D3YePBITAhvfehNPB1kuqwvBCqqT07qKa3F1HnC7durlbXXr27InQUOW1AqqRMSgIV896BIEmM86cTsOedV/JXRIpBMMLqc4Pb//Hc3/M9LsV2V1Ukz6G06VbO7vdzoG6PhIQEoLR02YCALZ//jFKz+TLXBEpAcMLqUpRTjb2b1jveTz0uptlrKZpOF269Tt8+DAsFgvMZjO6du0qdzmtTp/RVyCuZ2/YrRb89P47cpdDCsDwQqpSs7to9nsrZayk6dzdRq5SG1wVdpmrIV/gQF3fkjQajL37AUgaDY7v2IbUfXvkLolkxk8Zqcb3b7/uuX/ZnfcgQCULgGkCdNCGGQGw9aU1ysvLQ1paGgfq+lhkpy4YNP56AMDGpW/BYec/BNoyhhdShaLsLBzY8J3n8ZBrb5KvmGbQs+uo1XKvqNujRw+YTPJeDLS1Gzl5GoLDI1CYlYnf1nwhdzkkI4YXUoXfvlnlua+W7qKadLxAY6tkt9uRnJwMgAN1/cEYFIQx0+8GAOxY/SmKc7NlrojkwvBCqtDnsrFIHHwRJs79m2q6i2rSR1XNOOJaL63L0aNHUVlZCZPJhG7dusldTpuQNHI0Evr0h8Nuw8Zlb8tdDsmE4YVUIbZbT9z85Hx0GTRU7lKaRR/DbqPWiAN1/U+SJFz5p/uh0WpxcvdOnNi9Q+6SSAb8tBH5gc4946jcDmeZTeZqqCXk5+cjNTWVA3Vl0K5DR8+4t03L3obdZpW3IPI7hhciP9AYtNBGBABg60tr4R6o261bN5jNZpmraXsunnQbQtq1R3FuDnZ++Znc5ZCfMbwQ+QmvcdR6OBwODtSVmSEgEJffdS8AYNdXn6MwK0PmisifGF6I/ETPGUetxtGjR1FRUYHQ0FB0795d7nLarO7DRqLzgMFwOhzYuPS/EELIXRL5CcMLkZ/oonmNo9bCPVB30KBB0Gq1MlfTdkmShCtm3getTofUfXvw+85f5C6J/IThhchP3JcJcORW8F+IKnbmzBmcOnUKQNUsI5JXeGw8LrrxFgDApvffgc1SKXNF5A8ML0R+oo8KBCTAVeGAq5RLm6tVzYG6YWFh8hZDAIBhN02GKTIaZWfy8esX6lvEkrzH8ELkJ5JeC127QAAc96JWHKirTHqDEVfMnAUA2L32S5w5nSZzReRrDC9EfuRe74XjXtTp2LFjKC8vR0hICHr06CF3OVRD1yHDkThkGFxOJzb87y12zbZyDC9EfsTp0urGgbrKdsWMWdDpDUg/tB9Hf9kidznkQwwvRH509jIB7DZSm4KCApw8eRIAB+oqlTkqBsNvvhUAsPnD92Ct4D8SWiuGFyI/0teYLs1mbXXZu3cvAKBr164IDw+XuRpqyNAbJiE8Ng7lhQX45bPlcpdDPuKz8PLSSy9h5MiRCAoKavKIfCEEnnvuOcTGxiIwMBBjx47F77//7qsSifxO1z4Q0ADC6oSzhNc4Ugun0+kJLxyoq2w6vR5XzLwfALB3/dfI++OUzBWRL/gsvNhsNkyePBkPPPBAk/d59dVX8e9//xtvvfUWduzYgeDgYIwbNw4Wi8VXZRL5laTTVAUYAI5sdh2pxfHjx1FWVobg4GD07NlT7nLoPDoPGIwewy+BcLnw43tvQrhccpdELcxn4WXBggV49NFH0a9fvyZtL4TAa6+9hmeeeQY33ngj+vfvjw8++ACZmZn48ssvfVUmkd/pudKu6nCgrvpcdte90BsDkHnsMA5t2Sh3OdTCFDPm5dSpU8jOzsbYsWM9z5nNZgwfPhzbt29vcD+r1YqSkpJaNyIl43RpdSksLERKSgoADtRVk9B27THiltsBAFuWL4WlrEzmiqglKSa8ZGdnAwCio6NrPR8dHe15rT4LFy6E2Wz23BISEnxaJ9GF4gUa1cU91qVLly6IiIiQuRryxuAJNyAiPgGVJcXYuvIDucuhFuRVeJk7dy4kSWr0dvToUV/VWq958+ahuLjYc0tPT/fr+xN5Sx9T1W3kyK2AcHHGkZJxoK66aXV6jL27atzlvh+/Rc7JFJkropai82bjOXPmYMaMGY1uk5iY2KxCYmJiAAA5OTmIjY31PJ+Tk4OBAwc2uJ/RaITRaGzWexLJQdcuANBKEDYXnEVW6CIC5C6JGvD777+jtLQUQUFBSEpKkrscaoaEPv2RdMkYHN22GT++9wamvrAYkkYxnQ7UTF6Fl8jISERGRvqkkC5duiAmJgYbNmzwhJWSkhLs2LHDqxlLREonaatmHDlyKmDPrWB4UTD3QN2BAwdCp/Pq65IUZMz0u3Fyzy5kpxzHgY3fo//Y8XKXRBfIZ/EzLS0NycnJSEtLg9PpRHJyMpKTk1FWY9BUUlISVq9eDQCQJAl/+ctf8OKLL2LNmjU4cOAA7rzzTsTFxeGmm27yVZlEsnB3Hdk5XVqxioqKOFC3lQgJj8Alt04DAGxc9l9UlBTLXBFdKJ/9U+K5557D+++/73k8aNAgAMCmTZtw2WWXAai6yFlx8dm/RE888QTKy8sxa9YsFBUV4dJLL8X69esREMB/mVLroo8KQiV4jSO5CCEghB0ul7X6ZoPTaanx2Iq9e3ciPCINZtNwtG/fXu6S6QJ16F21bIfTbseb907DIx9+Ab2BQw7UShKtbI3ykpISmM1mFBcXw2QyyV0OUb0qD+bjzEdHoI8PQfTDg+QuRxZCiFphoebN6bLA5TwbLGo9X98+Tvfr575mqdq/1utVxwCa9tUXEf46Bg2a4Nv/GeQzZzLSsf2zFTj261agxs9dh959cdPjz8EYFCRjdVSTN7/f7MQlkoEu+uxaL8IlIGkkWeoQwnk2GDjPDQa22oHBeW7AqC9EWM553VYjRJy7vXIuj6DRGOvc8vOK4HBo0LNH9PkPQIpTmJ2JXz//GEe2boYQ1SvsSpInwJw+fBCfvfA0Js77G4JMZhkrpeZgeCGSga5dIKCTIBxO2PNLoYmQageGOkGiZquD7ZwwYaknSNhqBYmGwoYQDrn/V1SToNEEQKsNqA4PhlpBQqsJOCdcGKDRGqGpfl5bT/ioet14zuvnHqfqWJJUNzwuWrQIFosFo0Z1kuH/BzVXcW42fl31CQ5t3uC5LEDXoRdj6HU34dMFT0FA4MbHn8X3b/0fck7+jk/mP4lbnnkRoe3YNagmDC9EMpA0EjKH/Aelpt9w/KAyem4lSV8rOJwNEnVv9YeFgLP71notoFaYqG9fSdLVGyDk5Kr+4VNaXVS/kvw87Fj9CQ5u+gEupxMA0GXQUIycPA0xXbsjdd8eCOGCKTIa3YYOR/iCV/D5S8+iIPM0Pn7ucUx+5kWEx8bLfBbUVAwvRDKR9FpAqh1czhsWtOe+FlD9muGcEHGeoFF9LK0nXBggSbxmT03u4YAML8pWVnAGO778DAc2rIfTUdWS2Kn/IIycPA1xPc6uzZNx9BAAoENSbwBAu/gE3L7gVXz+0jMozMrEyvlPYtJTzyOqc/PWKiP/YnghkklC8UOw7LwNYdf0ROiwTg12X5A83OFFwwXNFKmipBg7v/wU+77/Fg571fiphN79MPLWaejQq2+d7TOOHgYAxCf18TxniozCbQtexecvP4e81JP4dME83PzkfMRXBxxSLn4qiWSiRxh0tjDoRAi0WiODi8Kw20jZKoqLsHvdGjjsNsT16IXJz76EW+cvrDe4OB12ZKUcB1A7vABAkDkMU+YvRHxSb1gryvH5S8/iVPJuv5wDNR/DC5FcqmcY8fpGysRuI2Vrn9AJIydPxaR5C3Db86+iY98BDW6bc/IEHDYrAkJNiIjvUOd1Y1AwJj31PLoMHAKHzYovX30BR3/Z4svy6QIxvBDJxDM9muFFkdhtpHwjJt2OzgOHnDdgZhyr7jLq2bvBbfXGANz4+DPoOXI0XE4H1v7779j/4/oWr5laBj+VRHJhy4ti1Vy7ky0v6ucerGurrMDhnzchK+UYLOVldbbT6vSY8PAcDLjqGkAI/PDOf7Dzq8/9XS41AQfsEsnE0/LSuha5bhXc410AhpfWwFJ9Tb30Q/uRfmi/5/lAkxnhsfEIj41DeGw8Iqrvj7nzHgSEhGLH6k/x84plsJSVYtTUGfy7oCAML0Rycbd7OhlelIYtL63LtY88jqO/bEFRViYKszJQmJWBssICVJYUo7KkGJnV3UoekgRT+0hodTo4HQ7sWvMFrOXluPKeB6DRcEkBJWB4IZILu40Uq2Z44ZgX9Qtt1x4XXT+x1nO2ygoUZmd5wkyhO9hkZsBaUY6SvNxa2+/fsB4JffsjaeRof5ZODWB4IZLJ2QG78tZBdbHbqPUzBAYhuktXRHfpWut5IQQqS0tQmJlRK9jYbVZEJ3aTqVo6F8MLkVzcLS8c86I47DZquyRJQpDJjCCTmYvVKRjbQ4nk4m554ZgXxWG3EZGy8VNJJBOu86JcbHkhUjaGFyK5sNtIsTjmhUjZGF6IZOL5TWTLi+Kw5YVI2RheiOSirW554ZgXxeGlAYiUjZ9MIpmcXWFX3jqoLl5RmkjZGF6I5MIBu4rFK0oTKRvDC5FcJK6wq1QML0TKxvBCJBNJy5YXpeKYFyJl4yeTSC68tpFiccwLkbIxvBDJxPPDyPCiOOw2IlI2hhciuXDArmKx24hI2fjJJJKLlt1GSsVuIyJlY3ghkglX2FUudhsRKRvDC5FcOGBXsRheiJSN4YVIJmevKi1vHVQXx7wQKRs/mURy4YBdxeKYFyJlY3ghkgu7jRSL3UZEysbwQiQTiS0visVuIyJl4yeTSC5seVEsdhsRKRvDC5FMJPenj+FFcdhtRKRsDC9EcnF3GzG7KA7DC5GyMbwQyYXdRorFMS9EysZPJpFMPAN2nQwvSsMxL0TKxvBCJBe2vCgWu42IlI3hhUgmnpYXwfCiNOw2IlI2fjKJ5MJ1XhSL3UZEysbwQiQXd7cRx7woDruNiJSN4YVIJuw2Ui6GFyJlY3ghkgsH7CoWx7wQKRs/mUQy4Qq7ysUxL0TKxvBCJBfPgN2z/9InZWC3EZGyMbwQycQz5gXgJQIUht1GRMrGTyaRXGqGF3YdKQq7jYiUjeGFSC41wgsH7SoLu42IlI3hhUgmElteFIvhhUjZGF6I5CIxvCgVx7wQKRs/mURyqfHpY7eRsnDMC5GyMbwQyUSSpLOfQJespdA52G1EpGwML0Ry4iq7isTwQqRsDC9EMvL8ODK8KIq724hjXoiUiZ9MIjlpGF6UiC0vRMrG8EIkI0nLbiMlYnghUjaGFyI5sdtIkThVmkjZ+MkkkhMH7CoSp0oTKRvDC5GMJI55USR2GxEpG8MLkZyqP4FseVEWhhciZWN4IZKRp+WF2UVROFWaSNn4ySSSkzu8OJlelIQtL0TKxvBCJCcO2FUkhhciZWN4IZIRB+wqE6dKEykbP5lEcnK3vAiGFyXhVGkiZWN4IZKRxDEvisRuIyJlY3ghkhO7jRSJ4YVI2XwWXl566SWMHDkSQUFBCAsLa9I+M2bMgCRJtW7jx4/3VYlE8nOv88JuI0XhVGkiZdP56sA2mw2TJ0/GiBEj8N577zV5v/Hjx2Pp0qWex0aj0RflESkCB+wqE1teiJTNZ+FlwYIFAIBly5Z5tZ/RaERMTIwPKiJSIM9UaZnroFoYXoiUTXFtoj/99BOioqLQs2dPPPDAAzhz5kyj21utVpSUlNS6EakFW16UiVOliZRNUZ/M8ePH44MPPsCGDRvwyiuvYPPmzbjmmmvgdDob3GfhwoUwm82eW0JCgh8rJrpADC+KxKnSRMrmVXiZO3dunQG1596OHj3a7GJuu+023HDDDejXrx9uuukmfPPNN9i1axd++umnBveZN28eiouLPbf09PRmvz+R30lcYVeJ2G1EpGxejXmZM2cOZsyY0eg2iYmJF1JPnWO1b98eKSkpuPLKK+vdxmg0clAvqZakZcuLEjG8ECmbV+ElMjISkZGRvqqljtOnT+PMmTOIjY3123sS+RWvbaRInCpNpGw++2SmpaUhOTkZaWlpcDqdSE5ORnJyMsrKyjzbJCUlYfXq1QCAsrIyPP744/j111+RmpqKDRs24MYbb0S3bt0wbtw4X5VJJCsO2FUmtrwQKZvPpko/99xzeP/99z2PBw0aBADYtGkTLrvsMgDAsWPHUFxcDADQarXYv38/3n//fRQVFSEuLg5XX301XnjhBXYLUevH7KIoDC9Eyuaz8LJs2bLzrvFSc1XRwMBAfPfdd74qh0iRhLN6VouWP5JKYrfbAQA6nc++IonoArBDl0hGwlEd4HX8KCqJe70ok8kkcyVEVB9+YxLJydPywo+iUgghPN3ZZrNZ5mqIqD78xiSSkXBWj63QsdtIKSoqKuBwOACw5YVIqRheiGQkHNUXNeKYF8UoKioCAISEhHDMC5FCMbwQycnd8sJuI8VglxGR8vEbk0hGntlGHLCrGAwvRMrHb0wiGbnHvLDbSDnc4SUsLEzeQoioQQwvRHJycJ0XpWHLC5HyMbwQyUhwzIviMLwQKR+/MYlk5B7zwkXqlIPhhUj5+I1JJCP3CrvsNlIGu92O8vJyAAwvRErG8EIkJ17bSFHclwXQ6/UIDAyUuRoiagjDC5GMzq6wy4+iEtTsMuIVpYmUi9+YRDIRQngWqeNUaWVwr67LLiMiZWN4IZKLO7iALS9KwcG6ROrAb0wimXhmGoFjXpSCC9QRqQPDC5FM3DONAABc50UR2PJCpA78xiSSi7vlRQNIGra8KAHDC5E6MLwQyeTsGi/8GCqBy+VieCFSCX5rEsnEM+aF410UoaKiAk6nEwBgMplkroaIGsPwQiQXrvGiKO5Wl9DQUGi1WpmrIaLG8FuTSCaCV5RWFHYZEakHwwuRTIRngTp+DJWA4YVIPfitSSQXXtdIUbi6LpF6MLwQyYTXNVIWLlBHpB781iSSiXvMC2cbKQO7jYjUg+GFSC5OrvOiJAwvROrBb00imQiOeVEMm82GiooKAAwvRGrA8EIkE8+1jTjmRXYlJSUAAIPBgICAAJmrIaLz4bcmkUzY8qIcNbuMJIl/HkRKx/BCJBfONlIMjnchUhd+axLJ5OyFGfkvfbkxvBCpC8MLkUzOXpiRH0O5cYE6InXhtyaRXNzXNtKx5UVuXKCOSF0YXohkIrjOi2Kw24hIXfitSSQTT7cRW15k5XK5PFOlGV6I1IHhhUguDra8KEF5eTmcTickSUJoaKjc5RBRE/Bbk0gmXOdFGdxdRqGhodBqtTJXQ0RNwfBCJBP3mBfONpIXx7sQqQ+/NYnkwtlGisDwQqQ+DC9EMuFsI2VgeCFSH35rEsnkbLcRW17kxPBCpD4ML0Ry8QzY5cdQTu7VdblAHZF68FuTSCaCY14UgS0vROrD8EIkE842kp/NZkNlZSUAhhciNeG3JpFMzg7YZcuLXNytLkajEQEBATJXQ0RNxfBCJBdPtxE/hnJhlxGROvFbk0gmnmsbseVFNgwvROrE8EIkE0+3EVteZMPwQqRO/NYkkouDY17kxvBCpE4ML0QyOdttxI+hXNzhhWu8EKkLvzWJZCLcLS9c50U2bHkhUieGFyK5cIVdWblcLoYXIpXityaRTIQnvLDlRQ5lZWVwuVyQJAkhISFyl0NEXmB4IZKBcAmgesgLONtIFu5WF5PJBK1WK3M1ROQNfmsSycF9aQCw5UUu7DIiUi+GFyIZOMvtnvsML/JgeCFSL4YXIhnYUos99ysPnZGxkraL4YVIvRheiGTgqtHyUrTmBJylNhmraZt27twJAHA6nTJXQkTeYnghkoElpchz31XhQOGq3yGEaHgH8hmr1Sp3CUTkJYYXIj8TTgHryaoui/BbegBaCZYjBajYmytzZW1HzcDSoUMHGSshouZgeCHyM9vpUgirE5ogHYIGR8E0tiOA6u6jYrYC+ENmZqbnflBQkIyVEFFzMLwQ+Zn190IAgLFrGCSNhNDRCdB3CIGwONl95CcZGRme+xzzQqQ+DC9EfuYe72LsFgagaqp0xK09AZ0Ey7FCVPyWI19xbcTp06c9910uVyNbEpESMbwQ+ZHL6oQtrRQAEFAdXgBAHxUE89WdAQBF35yEo9AiQ3VtR82WF4YXIvVheCHyI0eBBXBVdQtV7M+v1UUUcmk8DJ1MEFYnCr9g95GvFBcXo7S01POY3UZE6sPwQuRH+pgghIyMAwCUfJeKws+OQziqL9CokRA+uQckvQbWlCKU78iSs9RWq2arC8CWFyI1Yngh8iNJkhB2Q1eE3dgV0AAVe3KR984BOMuqFqnTtw+EaXxnAEDxulNwnKmUsdrWqeZ4F4DhhUiNGF6IZBAyIg7tZ/aFFKCF7Y8S5L6xD/accs9rhi5mCJsLBZ//XnUFamoxbHkhUj+fhZfU1FTcfffd6NKlCwIDA9G1a1fMnz8fNlvjy6BbLBbMnj0b7dq1Q0hICCZNmoScHM6+oNYnoHs4oh4cCG27ADgLLMh9Yx8sxwogaSRE3NIdkkED26lilP2Sef6DUZM4nc5aa7wAQExMjEzVEFFz+Sy8HD16FC6XC//9739x6NAh/Otf/8Jbb72Fp556qtH9Hn30UXz99df47LPPsHnzZmRmZmLixIm+KpNIVvqoIEQ9OBCGLlUDdfOXHULZtgxoIwJgnpAIoGpsjD2vQuZKW4e8vDzY7WevKxUZGYlOnTrJWBERNYck/Dil4e9//zvefPNNnDx5st7Xi4uLERkZiRUrVuCWW24BUBWCevXqhe3bt+Piiy8+73uUlJTAbDajuLgYJpOpResn8hXhcKFwdQoqdle1MgZfHIuw6xKRv+wQrClFMHQMReT9AyBpJJkrVbfffvsN33zzjefxhAkTMGzYMBkrIiI3b36//Trmpbi4GBEREQ2+vnv3btjtdowdO9bzXFJSEjp27Ijt27fXu4/VakVJSUmtG5HaSDoNwm/pDvM1XQAJKP81C/nLDsF8bSIkoxa2tFKUbc04/4GoUTXHu+j1evTv31/GaoioufwWXlJSUvD666/jvvvua3Cb7OxsGAwGhIWF1Xo+Ojoa2dnZ9e6zcOFCmM1mzy0hIaElyybyG0mSEDqmA9rd0dszXbpgxRGEjKiaWl38fapnUC81T82ZRgMGDEBAQICM1RBRc3kdXubOnQtJkhq9HT16tNY+GRkZGD9+PCZPnox77723xYoHgHnz5qG4uNhzS09Pb9HjE/lbYJ92iHxgALRmAxx5lSjbkQUpQAs4BAo+Ow7h5Oyj5rBYLMjLy/M8Hjp0qIzVENGF0Hm7w5w5czBjxoxGt0lMTPTcz8zMxOWXX46RI0fi7bffbnS/mJgY2Gw2FBUV1Wp9ycnJaXBGgNFohNFobHL9RGpgiAtB1OxByP/gEOynyzzP20+XoXRLOkyXd5SxOnWqOcsoISGBs4yIVMzr8BIZGYnIyMgmbZuRkYHLL78cQ4YMwdKlS6HRNN7QM2TIEOj1emzYsAGTJk0CABw7dgxpaWkYMWKEt6USqZrWZEDUff1R8NlxVO7P9zxf8mMaApLawRAbLGN16lNzvMtFF10kYyVEdKF8NuYlIyMDl112GTp27IjFixcjLy8P2dnZtcauZGRkICkpCTt37gQAmM1m3H333XjsscewadMm7N69GzNnzsSIESOaNNOIqLWR9FpE3JaE0CtrtLQ4BQo/PQbh5OJq3nCPdwkKCkLv3r1lroaILoTXLS9N9cMPPyAlJQUpKSno0KFDrdfcs7PtdjuOHTuGioqza1j861//gkajwaRJk2C1WjFu3Di88cYbviqTSPEkjQTzVZ2gjwxEwefHAYeAPascJRvTYb6Ka5Q0hRDC0/IyePBg6HQ+++ojIj/w6zov/sB1Xqg1s6aV4MwHh+EqswMaCVEPD2L3URNYLBYsWrQIAPDnP/8Z4eHhMldEROdS7DovRHRhjB1NiJo9EPqYIMAlYPujWO6SVMFoNGLUqFG49tprGVyIWgG2vBCpkMvmhC21BMZEMyQd/w1CROrnze83O36JVEhj0CKgB1sQiKht4j/ZiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVGF6IiIhIVRheiIiISFUYXoiIiEhVdHIX0NKEEACAkpISmSshIiKipnL/brt/xxvT6sJLaWkpACAhIUHmSoiIiMhbpaWlMJvNjW4jiaZEHBVxuVzIzMxEaGgoJEmqd5uSkhIkJCQgPT0dJpPJzxX6T1s4z7ZwjkDbOM+2cI5A2zjPtnCOQNs4T3+eoxACpaWliIuLg0bT+KiWVtfyotFo0KFDhyZtazKZWu1fuJrawnm2hXME2sZ5toVzBNrGebaFcwTaxnn66xzP1+LixgG7REREpCoML0RERKQqbTK8GI1GzJ8/H0ajUe5SfKotnGdbOEegbZxnWzhHoG2cZ1s4R6BtnKdSz7HVDdglIiKi1q1NtrwQERGRejG8EBERkaowvBAREZGqMLwQERGRqrSZ8FJQUIBp06bBZDIhLCwMd999N8rKyhrd/uGHH0bPnj0RGBiIjh074pFHHkFxcbEfqz6/JUuWoHPnzggICMDw4cOxc+fORrf/7LPPkJSUhICAAPTr1w/r1q3zU6XN5805vvPOOxg1ahTCw8MRHh6OsWPHnvf/iVJ4+2fptnLlSkiShJtuusm3BbYAb8+xqKgIs2fPRmxsLIxGI3r06NHq/s4CwGuvveb5rklISMCjjz4Ki8Xip2q9t2XLFlx//fWIi4uDJEn48ssvz7vPTz/9hMGDB8NoNKJbt25YtmyZz+u8EN6e46pVq3DVVVchMjISJpMJI0aMwHfffeefYi9Ac/4s3bZt2wadToeBAwf6rL4GiTZi/PjxYsCAAeLXX38VP//8s+jWrZu4/fbbG9z+wIEDYuLEiWLNmjUiJSVFbNiwQXTv3l1MmjTJj1U3buXKlcJgMIj//e9/4tChQ+Lee+8VYWFhIicnp97tt23bJrRarXj11VfF4cOHxTPPPCP0er04cOCAnytvOm/PcerUqWLJkiVi79694siRI2LGjBnCbDaL06dP+7ly73h7nm6nTp0S8fHxYtSoUeLGG2/0T7HN5O05Wq1WMXToUDFhwgSxdetWcerUKfHTTz+J5ORkP1fuHW/Pc/ny5cJoNIrly5eLU6dOie+++07ExsaKRx991M+VN926devE008/LVatWiUAiNWrVze6/cmTJ0VQUJB47LHHxOHDh8Xrr78utFqtWL9+vX8KbgZvz/HPf/6zeOWVV8TOnTvF8ePHxbx584Rerxd79uzxT8HN5O15uhUWForExERx9dVXiwEDBvi0xvq0ifBy+PBhAUDs2rXL89y3334rJEkSGRkZTT7Op59+KgwGg7Db7b4o02vDhg0Ts2fP9jx2Op0iLi5OLFy4sN7tb731VnHttdfWem748OHivvvu82mdF8LbczyXw+EQoaGh4v333/dViS2iOefpcDjEyJEjxbvvvivuuusuxYcXb8/xzTffFImJicJms/mrxBbh7XnOnj1bXHHFFbWee+yxx8Qll1zi0zpbSlN+8J544gnRp0+fWs9NmTJFjBs3zoeVtRxvftRr6t27t1iwYEHLF+Qj3pznlClTxDPPPCPmz58vS3hpE91G27dvR1hYGIYOHep5buzYsdBoNNixY0eTj1NcXAyTyQSdTv5LQtlsNuzevRtjx471PKfRaDB27Fhs37693n22b99ea3sAGDduXIPby60553iuiooK2O12RERE+KrMC9bc83z++ecRFRWFu+++2x9lXpDmnOOaNWswYsQIzJ49G9HR0ejbty9efvllOJ1Of5Xtteac58iRI7F7925P19LJkyexbt06TJgwwS81+4PavntagsvlQmlpqaK/e5pr6dKlOHnyJObPny9bDfL/CvtBdnY2oqKiaj2n0+kQERGB7OzsJh0jPz8fL7zwAmbNmuWLEr2Wn58Pp9OJ6OjoWs9HR0fj6NGj9e6TnZ1d7/ZN/X/gb805x3M9+eSTiIuLq/PFqSTNOc+tW7fivffeQ3Jysh8qvHDNOceTJ09i48aNmDZtGtatW4eUlBQ8+OCDsNvtsn5pNqY55zl16lTk5+fj0ksvhRACDocD999/P5566il/lOwXDX33lJSUoLKyEoGBgTJV5juLFy9GWVkZbr31VrlLaVG///475s6di59//lnWf8iruuVl7ty5kCSp0VtTf+QaU1JSgmuvvRa9e/fG3/72twsvnPxi0aJFWLlyJVavXo2AgAC5y2kxpaWlmD59Ot555x20b99e7nJ8xuVyISoqCm+//TaGDBmCKVOm4Omnn8Zbb70ld2kt6qeffsLLL7+MN954A3v27MGqVauwdu1avPDCC3KXRs20YsUKLFiwAJ9++mmdfzirmdPpxNSpU7FgwQL06NFD1lpU3fIyZ84czJgxo9FtEhMTERMTg9zc3FrPOxwOFBQUICYmptH9S0tLMX78eISGhmL16tXQ6/UXWnaLaN++PbRaLXJycmo9n5OT0+A5xcTEeLW93Jpzjm6LFy/GokWL8OOPP6J///6+LPOCeXueJ06cQGpqKq6//nrPcy6XC0BVi+KxY8fQtWtX3xbtpeb8WcbGxkKv10Or1Xqe69WrF7Kzs2Gz2WAwGHxac3M05zyfffZZTJ8+Hffccw8AoF+/figvL8esWbPw9NNPQ6NR9b8xATT83WMymVpdq8vKlStxzz334LPPPlN0i29zlJaW4rfffsPevXvx0EMPAaj67hFCQKfT4fvvv8cVV1zhl1pU/amIjIxEUlJSozeDwYARI0agqKgIu3fv9uy7ceNGuFwuDB8+vMHjl5SU4Oqrr4bBYMCaNWsU9a93g8GAIUOGYMOGDZ7nXC4XNmzYgBEjRtS7z4gRI2ptDwA//PBDg9vLrTnnCACvvvoqXnjhBaxfv77WOCel8vY8k5KScODAASQnJ3tuN9xwAy6//HIkJycjISHBn+U3SXP+LC+55BKkpKR4ghkAHD9+HLGxsYoMLkDzzrOioqJOQHEHNtFKLj2ntu+e5vr4448xc+ZMfPzxx7j22mvlLqfFmUymOt89999/P3r27Ink5ORGf09bnN+HCMtk/PjxYtCgQWLHjh1i69atonv37rWmSp8+fVr07NlT7NixQwghRHFxsRg+fLjo16+fSElJEVlZWZ6bw+GQ6zRqWblypTAajWLZsmXi8OHDYtasWSIsLExkZ2cLIYSYPn26mDt3rmf7bdu2CZ1OJxYvXiyOHDki5s+fr4qp0t6c46JFi4TBYBCff/55rT+z0tJSuU6hSbw9z3OpYbaRt+eYlpYmQkNDxUMPPSSOHTsmvvnmGxEVFSVefPFFuU6hSbw9z/nz54vQ0FDx8ccfi5MnT4rvv/9edO3aVdx6661yncJ5lZaWir1794q9e/cKAOKf//yn2Lt3r/jjjz+EEELMnTtXTJ8+3bO9e6r0448/Lo4cOSKWLFmi+KnS3p7j8uXLhU6nE0uWLKn13VNUVCTXKTSJt+d5LrlmG7WZ8HLmzBlx++23i5CQEGEymcTMmTNr/aCdOnVKABCbNm0SQgixadMmAaDe26lTp+Q5iXq8/vrromPHjsJgMIhhw4aJX3/91fPamDFjxF133VVr+08//VT06NFDGAwG0adPH7F27Vo/V+w9b86xU6dO9f6ZzZ8/3/+Fe8nbP8ua1BBehPD+HH/55RcxfPhwYTQaRWJionjppZcU84+Hxnhznna7Xfztb38TXbt2FQEBASIhIUE8+OCDorCw0P+FN1FD34/u87rrrrvEmDFj6uwzcOBAYTAYRGJioli6dKnf6/aGt+c4ZsyYRrdXqub8WdYkV3iRhGgl7ZJERETUJqh6zAsRERG1PQwvREREpCoML0RERKQqDC9ERESkKgwvREREpCoML0RERKQqDC9ERESkKgwvREREpCoML0RERKQqDC9ERESkKgwvREREpCoML0RERKQq/w+N6bApt4ZhygAAAABJRU5ErkJggg==\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## **Creating the TensorFlow Dataset**\n", "\n"], "metadata": {"id": "htp_3DxsNll4"}}, {"cell_type": "markdown", "source": ["### Why Use TensorFlow Datasets?\n", "\n", "1. **Handling Large Datasets**: TFDS can manage datasets that are too large to fit into memory. It does this by loading data in chunks (batches), enabling the model to train on large datasets without memory overflow issues.\n", "\n", "2. **Performance Optimization**: TFDS includes features like prefetching, parallel data extraction, and caching, which significantly speed up data loading and preprocessing. This means less waiting time for data to be ready for each training step, leading to faster model training.\n", "\n", "3. **Flexibility and Scalability**: TensorFlow datasets provide a flexible pipeline for transforming data, allowing for complex data augmentation and preprocessing operations to be efficiently integrated into the training pipeline.\n", "\n", "4. **Integration with TensorFlow Ecosystem**: TFDS seamlessly integrates with TensorFlow's training loops and model APIs, making it easier to use alongside TensorFlow models, optimizers, and other utilities.\n", "\n", "\n", "> [Video Tutorial](https://www.youtube.com/watch?v=VFEOskzhhbc)\n", "\n"], "metadata": {"id": "eKDnYO5sQcQ7"}}, {"cell_type": "markdown", "source": ["### Overview\n", "\n", "In this section, we begin the process of constructing a TensorFlow dataset suitable for training a model on the sign language recognition data. This involves a series of steps designed to format the data properly, including loading, preprocessing, batching, and combining features with labels. We will go over the following:\n", "\n", "- **Importing Libraries**: Load necessary libraries for data handling and manipulation.\n", "\n", "- **Load Sign Label Mappings**: Read the JSON file that maps sign gestures to prediction indices, essential for label processing.\n", "\n", "- **Defining Constants**: Define constants such as data columns (x, y, z coordinates), number of rows per frame, and batch size.\n", "\n", "- **Data Loading and Preprocessing Functions**: Implement functions to load data from parquet files, focusing on required columns, and reshape it to match model input specifications.\n", "\n", "- **Generating the Dataset**:\n", "  - Initialize a dataset.\n", "  - Use helper functions to turn our data into the format our model can learn from.\n", "  - Group the data into batches, getting it ready for the model.\n", "\n", "- **Getting Labels Ready**:\n", "  - Turn the sign names into numbers the model understands.\n", "  - Group these numbers into batches, just like we did with the data.\n", "\n", "- **Putting It All Together**:\n", "  - Join the data and labels into one dataset that we can use to train the model."], "metadata": {"id": "4wBPZsLUQi3e"}}, {"cell_type": "markdown", "source": ["### Import libraries\n", "\n", "Let's import the necessary libraries for creating our TensorFlow dataset. Here's a brief overview of each and its relevance to our project:\n", "\n", "- **TensorFlow (`tf`)**: A powerful library for numerical computation and machine learning. In this context, TensorFlow is used to construct and manipulate datasets, and it's the backbone of building and training our model.\n", "\n", "- **JSON**: We use the JSON library to load mapping files that associate sign language gestures with numerical indices, a step required for label encoding.\n", "\n"], "metadata": {"id": "fDfThzHfWdg2"}}, {"cell_type": "code", "source": ["# import libraries\n", "import tensorflow as tf\n", "import json\n", "import pandas as pd\n", "import numpy as np"], "metadata": {"id": "FFn-mL9-KMBE"}, "execution_count": 8, "outputs": []}, {"cell_type": "markdown", "source": ["### Load Sign Label Mappings\n", "\n", "In this step, we load the mappings between sign language gestures and their corresponding numerical indices from a JSON file. This mapping is essential for transforming textual labels into numerical form, which is a format our machine learning model can work with more effectively.\n", "\n", "```\n", "with open('/content/asl-signs/sign_to_prediction_index_map.json') as f:\n", "    sign_index_mapping = json.load(f)\n", "print(sign_index_mapping)\n", "```\n", "\n", "By loading this mapping, we can easily convert the sign language labels in our dataset into indices that our TensorFlow model will predict. This is required for both training our model and interpreting its predictions."], "metadata": {"id": "eEhOruUgX6OF"}}, {"cell_type": "code", "source": ["with open('/content/asl-signs/sign_to_prediction_index_map.json') as f:\n", "    sign_index_mapping = json.load(f)\n", "\n", "print(sign_index_mapping)"], "metadata": {"id": "7UXASeTvXWZN", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b6bf05a7-7feb-40b6-8e64-24b59e903a93"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'bird': 0, 'blow': 1, 'cloud': 2, 'owie': 3, 'wait': 4}\n"]}]}, {"cell_type": "markdown", "source": ["### Defining Constants\n", "\n", "Next, we focus on defining constants that will guide the handling and processing of our dataset for the TensorFlow model. Understanding the significance of each constant will help in grasping their impact on the data preparation and training process:\n", "\n", "- **`COLUMNS_OF_INTEREST`**: These are the columns we extract from our dataset, specifically the 'x', 'y', and 'z' coordinates. These coordinates represent the spatial positioning of sign language gestures and are features for our model to learn the nuances of sign language.\n", "\n", "- **`TOTAL_LANDMARKS` = 543**: This constant signifies the total number of landmark points captured for each frame in our video sequences. The value 543 is derived from the structure of our dataset, where each frame includes a comprehensive set of landmarks across the face, hands, and pose. These landmarks provide a detailed snapshot of a gesture at a given time, crucial for our model to understand and learn from the temporal progression of signs.\n", "\n", "- **`SHARDS_NUMBER` = 2**: Sharding refers to splitting the dataset into smaller, more manageable segments. Here, we divide our dataset into two parts. This technique can optimize data processing and loading times, making it easier to handle large volumes of data.\n", "\n", "- **`SAVE_LOCATION` = '/content/ASLDataset'**: This is the directory where our processed data and any output from our model will be saved.\n", "\n", "- **`BATCHING_SIZE` = 256**: Batch size determines how many examples our model processes before updating its internal parameters. A batch size of 256 strikes a balance between training speed and model performance, allowing for efficient computation without sacrificing the ability to generalize from the training data.\n", "\n", "This process ensure that our dataset is treated consistently throughout our workflow, from how we extract and handle features to how we structure our training batches."], "metadata": {"id": "WdvtlBMKYy5U"}}, {"cell_type": "code", "source": ["COLUMNS_OF_INTEREST    = ['x', 'y', 'z']\n", "TOTAL_LANDMARKS  = 543\n", "SHARDS_NUMBER      = 2\n", "SAVE_LOCATION       = '/content/ASLDataset'\n", "BATCHING_SIZE      = 256"], "metadata": {"id": "GJikFKO9Ygb9"}, "execution_count": 10, "outputs": []}, {"cell_type": "markdown", "source": ["### Data Loading and Preprocessing Function\n", "\n", "In this section, we're setting up functions to load and process our data, making it ready for the model. Here’s a breakdown:\n", "\n", "- **Loading Data with `load_data_from_file`**:\n", "   - This function takes a path to a parquet file.\n", "   - It reads the file, selecting only the 'x', 'y', and 'z' columns.\n", "   - It calculates the number of frames in the video by dividing the total rows by rows per frame.\n", "   - The data is converted to `float32`, a format TensorFlow likes.\n", "   - Finally, it reshapes the data so each piece matches what the model expects: a sequence of frames, each with its landmarks.\n", "\n", "- **Fetching Features with `transform_tensor_to_data`**:\n", "   - This function wraps around the first one but is designed to work within TensorFlow’s way of handling data.\n", "   - It takes a tensor (basically, TensorF<PERSON>’s version of an array), grabs the actual file path from it, and feeds that to our data loading function.\n", "   - This ensures TensorFlow can use our data loading function without any issues.\n", "\n", "- **Setting Data Shape with `ensure_data_shape`**:\n", "   - TensorFlow works best when it knows the shape of the data it’s dealing with.\n", "   - This function tells TensorFlow the shape of our data: a batch of frames, where each frame has a set number of landmarks, and each landmark has three coordinates (x, y, z).\n", "   - The ‘None’ in the shape means it can handle any number of frames, making it flexible.\n", "\n", "These steps are essential for turning our raw data into something structured and ready for the model to learn from."], "metadata": {"id": "Fmg4ib61beLB"}}, {"cell_type": "code", "source": ["def load_data_from_file(pq_path):\n", "    data = pd.read_parquet('/content/asl-signs/'+pq_path, columns=COLUMNS_OF_INTEREST)\n", "    n_frames = int(len(data) / TOTAL_LANDMARKS)\n", "    data = data.values.astype(np.float32)\n", "    return data.reshape(n_frames, TOTAL_LANDMARKS, len(COLUMNS_OF_INTEREST))"], "metadata": {"id": "MT5WEA63aLqE"}, "execution_count": 11, "outputs": []}, {"cell_type": "markdown", "source": ["#### Understanding the shape\n", "\n", "When we used the `load_data_from_file` function on a sample parquet file, it processed the file and reshaped the data. Here's what happened:\n", "\n", "  - The first number, **105**, represents the total number of frames in this particular video sequence. Each frame is a snapshot in time of the sign being performed.\n", "  - The second number, **543**, is the number of landmarks (or points) captured in each frame. This consistent number aligns with the constant `TOTAL_LANDMARKS` we defined earlier, indicating each frame has a detailed set of landmarks.\n", "  - The third number, **3**, corresponds to the coordinates of each landmark: 'x', 'y', and 'z'.\n", "\n", "This shape (105, 543, 3) tells us that we have a sequence of 105 frames, and in each frame, there are 543 landmarks, with each landmark defined by 3 coordinates. This structured format is exactly what we need for training our model, allowing it to learn the dynamic movements of sign language from spatial landmark data over time."], "metadata": {"id": "OIPODINqdv0m"}}, {"cell_type": "code", "source": ["# Lets view one sample parquet file and use our helper function\n", "test_np = load_data_from_file('train_landmark_files/16069/100015657.parquet')\n", "test_np.shape"], "metadata": {"id": "L8g8VclTcdGS", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "26b27fa0-4a7d-44d2-9a00-7f8bdeae31eb"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(105, 543, 3)"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "markdown", "source": ["#### Fetching Features with transform_tensor_to_data\n", "\n", "In the `transform_tensor_to_data` function, we work on integrating our preprocessing routine within TensorFlow's data pipeline. Here's a breakdown:\n", "\n", "- **Bridge to Custom Processing**: This function acts as a connector, allowing TensorFlow to utilize our custom `load_data_from_file` function directly within its data processing pipeline. It's designed to work with TensorFlow's tensors, enabling full compatibility.\n", "\n", "- **Use of `tf.py_function`**: The core of this integration is `tf.py_function`, which wraps our `feat_wrapper` function. This TensorFlow operation allows us to run Python functions as part of the TensorFlow graph. It's particularly useful for incorporating non-TensorFlow Python code (like our data loading function) into TensorFlow's execution environment.\n", "\n", "- **Data Conversion and Decoding**:\n", "  - Inside `feat_wrapper`, the tensor `ftensor` is first converted to a numpy array with `.numpy()` and then decoded from bytes to a string with `.decode('utf-8')`. This conversion is necessary because the file paths in our dataset are stored as strings in TensorFlow tensors, but they need to be in a format that our file loading function can use.\n", "  - This process bridges the gap between TensorFlow's tensor operations and the Python-based file handling required to load our dataset.\n", "\n", "- **Ensuring Data Type Compatibility**: The `Tout=tf.float32` argument in `tf.py_function` specifies the output type of the function, ensuring that the data returned by our loading function is in `float32` format. This consistency is crucial for TensorFlow models, which typically expect inputs to be in floating-point format for numerical stability and efficiency during training.\n", "\n", "This is the integration of custom data with TensorFlow's optimized data pipelines."], "metadata": {"id": "WJOCJwzgetWs"}}, {"cell_type": "code", "source": ["def transform_tensor_to_data(ftensor):\n", "    def feat_wrapper(ftensor):\n", "        return load_data_from_file(ftensor.numpy().decode('utf-8'))\n", "    return tf.py_function(\n", "        feat_wrapper,\n", "        [ftensor],\n", "        Tout=tf.float32\n", "    )"], "metadata": {"id": "LWGhk96xby9j"}, "execution_count": 13, "outputs": []}, {"cell_type": "markdown", "source": ["The `ensure_data_shape` function ensures our data adheres to the expected shape before it's fed into the model for training. Here's what it does and why it's important:\n", "\n", "- **Setting Expectations**: This function uses `tf.ensure_shape` to specify the shape that our data should have. By doing this, it helps TensorFlow understand how to handle the incoming data during the model training process.\n", "\n", "- **Flexible Batching**: The first dimension is set to `None`, which allows for flexibility in the batch size. This means the function can handle any number of examples in a batch, making our data pipeline more adaptable.\n", "\n", "- **Consistent Structure**: The second and third dimensions are explicitly set to match the `TOTAL_LANDMARKS` and the length of `COLUMNS_OF_INTEREST`, respectively. This ensures that every piece of data fed into the model has a consistent and expected structure, specifically, the number of landmarks per frame and the coordinates (x, y, z) for each landmark.\n", "\n", "- **Ensuring Compatibility**: By explicitly setting the shape, we help prevent errors that might arise from data shape mismatches. It's a way of telling the model, \"This is exactly what you should expect,\" which is crucial for smooth training.\n", "\n", "This function ensures consistency in our data and structures it for training a machine learning model."], "metadata": {"id": "MCC2q3jnfx5h"}}, {"cell_type": "code", "source": ["def ensure_data_shape(x):\n", "    return tf.ensure_shape(x, (None, TOTAL_LANDMARKS, len(COLUMNS_OF_INTEREST)))"], "metadata": {"id": "_tJ6KASLcIkS"}, "execution_count": 14, "outputs": []}, {"cell_type": "markdown", "source": ["### Generating the Dataset\n", "\n", "This part of the process turns the parquet file paths from our train.csv into a TensorFlow dataset that's formatted and batched, ready for model training. The steps are as follows:\n", "\n", "1. **Initialize Dataset with File Paths**: We start by creating a dataset from the file paths listed in our CSV. These paths point to the parquet files, each containing a sequence of sign language data.\n", "\n", "2. **Load and Preprocess the Data**: For every file path in our dataset, we use the helper functions we created to load the corresponding data and preprocess it, focusing on the necessary 'x', 'y', and 'z' coordinates.\n", "\n", "3. **Batch the Data**: After processing, the data is grouped into batches. This step is crucial for training, as it dictates the amount of data the model processes in one iteration.\n", "\n", "```python\n", "dataframe = pd.read_csv('/content/asl-signs/train.csv')\n", "\n", "features_dataset = tf.data.Dataset.from_tensor_slices(\n", "    dataframe.path.values  # Dataset from parquet paths\n", ").map(\n", "    transform_tensor_to_data,  # Load and preprocess data\n", "    num_parallel_calls=tf.data.AUTOTUNE  # Optimize preprocessing\n", ").map(\n", "    ensure_data_shape,  # Ensure correct data shape\n", "    num_parallel_calls=tf.data.AUTOTUNE  # Optimize shaping\n", ").ragged_batch(\n", "    BATCHING_SIZE  # Group into batches\n", ")\n", "```\n", "\n", "- `tf.data.Dataset.from_tensor_slices` creates a dataset of the file paths.\n", "- `.map(transform_tensor_to_data, num_parallel_calls=tf.data.AUTOTUNE)` applies the custom function to load and preprocess the data efficiently, with `AUTOTUNE` dynamically adjusting parallelism for optimal performance.\n", "- `.map(ensure_data_shape, num_parallel_calls=tf.data.AUTOTUNE)` ensures each item has the desired shape, again using `AUTOTUNE` for performance optimization.\n", "- `ragged_batch` groups the data into batches, preparing it for the model. This approach is efficient for handling data where each sequence might have a different number of frames, fitting the dynamic nature of sign language videos.\n"], "metadata": {"id": "kLIyyC_Mhtar"}}, {"cell_type": "code", "source": ["dataframe = pd.read_csv('/content/asl-signs/train.csv')\n", "\n", "features_dataset = tf.data.Dataset.from_tensor_slices(\n", "    dataframe.path.values  # Dataset from parquet paths\n", ").map(\n", "    transform_tensor_to_data,  # Load and preprocess data\n", "    num_parallel_calls=tf.data.AUTOTUNE  # Optimize preprocessing\n", ").map(\n", "    ensure_data_shape,  # Ensure correct data shape\n", "    num_parallel_calls=tf.data.AUTOTUNE  # Optimize shaping\n", ").ragged_batch(\n", "    BATCHING_SIZE  # Group into batches\n", ")"], "metadata": {"id": "1nl-8hxbiklh"}, "execution_count": 15, "outputs": []}, {"cell_type": "markdown", "source": ["### Getting Labels Ready\n", "\n", "In this step, we prepare the labels for our model. This involves converting the sign language names into numerical indices and then batching these indices to align with our data batches. Here’s how it’s done:\n", "\n", "1. **Convert Sign Names to Indices**: The model cannot understand textual labels, so we map each sign name to a unique numerical index. This mapping ensures the model can process and learn from the labels.\n", "\n", "2. **Batch the Labels**: After conversion, we group the labels into batches. This batching aligns the labels with the corresponding data batches, ensuring each data batch has its matching set of labels during training.\n", "\n", "Here's the code for preparing and batching the labels:\n", "\n", "```python\n", "# load and batch the labels\n", "labels_dataset = tf.data.Dataset.from_tensor_slices(\n", "    dataframe.sign.map(sign_index_mapping).values.reshape(-1,1)  # Convert sign names to indices\n", ").batch(\n", "    BATCHING_SIZE  # Group into batches\n", ")\n", "```\n", "\n", "- We start by creating a dataset of labels from `dataframe.sign`, utilizing the `sign_index_mapping` dictionary to convert each sign name into its corresponding index.\n", "- The `.map(sign_index_mapping)` function applies this conversion, effectively translating all textual labels into numerical form.\n", "- Following the conversion, `.batch(BATCHING_SIZE)` is used to group these numerical labels into batches, mirroring the batch size of our data.\n", "\n", "This process ensures our labels are in the right format and batched correctly, ready to be paired with the data batches for model training."], "metadata": {"id": "37ONdZboknzO"}}, {"cell_type": "code", "source": ["# load and batch the labels\n", "labels_dataset = tf.data.Dataset.from_tensor_slices(\n", "    dataframe.sign.map(sign_index_mapping).values.reshape(-1,1)  # Convert sign names to indices\n", ").batch(\n", "    BATCHING_SIZE  # Group into batches\n", ")"], "metadata": {"id": "VVlPgz5zh4lM"}, "execution_count": 16, "outputs": []}, {"cell_type": "markdown", "source": ["### Putting It All Together and Saving\n", "\n", "After preparing both the feature and label datasets, the final step is to combine them into a single dataset. This combined dataset is what we'll use for training our model. It ensures that each input data batch is paired with its corresponding label batch, aligning perfectly for the learning process.\n", "\n", "Here’s how we put everything together and save it:\n", "\n", "1. **Combine Features and Labels**: We use the `zip` function to pair each feature batch with its corresponding label batch. This creates a dataset where each element is a tuple containing a batch of features and a batch of labels.\n", "\n", "2. **Prefetching for Performance**: Before saving, we apply `prefetch(tf.data.AUTOTUNE)` to the dataset. This step hints at TensorFlow to preload the next batch while the current one is being processed, improving training speed.\n", "\n", "3. **Saving the Dataset**: We save the dataset using the `.save` method.\n", "\n", "4. **Sharding the Dataset**: The `distribute_across_shards` defines how the dataset is split into shards or separate files. We use a simple uniform distribution to decide how data is assigned to shards.\n", "\n", "Here’s the code snippet for these steps:\n", "\n", "```python\n", "# Combine the features and labels into one dataset\n", "complete_dataset = tf.data.Dataset.zip((features_dataset, labels_dataset))\n", "\n", "def distribute_across_shards(*_):\n", "    return tf.random.uniform(shape=[], maxval=SHARDS_NUMBER, dtype=tf.int64)\n", "\n", "# Prefetch and save the dataset\n", "complete_dataset.prefetch(tf.data.AUTOTUNE).save(SAVE_LOCATION, shard_func=distribute_across_shards)\n", "```\n", "\n", "- `tf.data.Dataset.zip((features_dataset, labels_dataset))` combines the features and labels.\n", "- `.prefetch(tf.data.AUTOTUNE)` ensures that the dataset is loaded efficiently, enhancing performance.\n", "- `.save(SAVE_LOCATION, shard_func=distribute_across_shards)` stores the dataset, with `distribute_across_shards` determining how it’s divided into shards.\n"], "metadata": {"id": "WaaO345clCyM"}}, {"cell_type": "code", "source": ["# Combine the features and labels into one dataset\n", "complete_dataset = tf.data.Dataset.zip((features_dataset, labels_dataset))\n", "\n", "def distribute_across_shards(*_):\n", "    return tf.random.uniform(shape=[], maxval=SHARDS_NUMBER, dtype=tf.int64)\n", "\n", "# Prefetch and save the dataset\n", "complete_dataset.prefetch(tf.data.AUTOTUNE).save(SAVE_LOCATION, shard_func=distribute_across_shards)"], "metadata": {"id": "g2_j2jZUkyYj"}, "execution_count": 17, "outputs": []}, {"cell_type": "markdown", "source": ["# **SOLUTION**\n", "\n"], "metadata": {"id": "piApgdFspjrN"}}, {"cell_type": "code", "source": ["# Necessary libraries\n", "import tensorflow as tf\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "\n", "# Load sign to index mapping\n", "with open('/content/asl-signs/sign_to_prediction_index_map.json') as file:\n", "    sign_index_mapping = json.load(file)\n", "print(sign_index_mapping)\n", "\n", "# Constants setup\n", "COLUMNS_OF_INTEREST = ['x', 'y', 'z']\n", "TOTAL_LANDMARKS = 543\n", "SHARDS_NUMBER = 2\n", "SAVE_LOCATION = '/content/ASLDataset'\n", "BATCHING_SIZE = 256\n", "\n", "# Function to load and reshape data from a file\n", "def load_data_from_file(file_path):\n", "    data = pd.read_parquet('/content/asl-signs/' + file_path, columns=COLUMNS_OF_INTEREST)\n", "    frames_count = int(len(data) / TOTAL_LANDMARKS)\n", "    data_as_floats = data.values.astype(np.float32)\n", "    return data_as_floats.reshape(frames_count, TOTAL_LANDMARKS, len(COLUMNS_OF_INTEREST))\n", "\n", "# Function to transform tensor to data\n", "def transform_tensor_to_data(tensor):\n", "    def wrapper_function(tensor):\n", "        return load_data_from_file(tensor.numpy().decode('utf-8'))\n", "    return tf.py_function(wrapper_function, [tensor], Tout=tf.float32)\n", "\n", "# Function to ensure data shape\n", "def ensure_data_shape(data):\n", "    return tf.ensure_shape(data, (None, TOTAL_LANDMARKS, len(COLUMNS_OF_INTEREST)))\n", "\n", "# Load dataset from CSV\n", "dataframe = pd.read_csv('/content/asl-signs/train.csv')\n", "\n", "# Create TensorFlow datasets for features\n", "features_dataset = tf.data.Dataset.from_tensor_slices(dataframe.path.values).map(\n", "    transform_tensor_to_data, num_parallel_calls=tf.data.AUTOTUNE).map(\n", "    ensure_data_shape, num_parallel_calls=tf.data.AUTOTUNE).ragged_batch(BATCHING_SIZE)\n", "\n", "# Create TensorFlow datasets for labels\n", "labels_dataset = tf.data.Dataset.from_tensor_slices(\n", "    dataframe.sign.map(sign_index_mapping).values.reshape(-1,1)).batch(BATCHING_SIZE)\n", "\n", "# Combine features and labels into one dataset\n", "complete_dataset = tf.data.Dataset.zip((features_dataset, labels_dataset))\n", "\n", "# Function to decide on shard distribution\n", "def distribute_across_shards(*_):\n", "    return tf.random.uniform(shape=[], maxval=SHARDS_NUMBER, dtype=tf.int64)\n", "\n", "# Final step: prefetch and save the dataset for efficient training\n", "complete_dataset.prefetch(tf.data.AUTOTUNE).save(SAVE_LOCATION, shard_func=distribute_across_shards)"], "metadata": {"id": "FWl5rpSxsFSK", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8b902ab5-8667-42da-f574-478bf7114628"}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'bird': 0, 'blow': 1, 'cloud': 2, 'owie': 3, 'wait': 4}\n"]}]}, {"cell_type": "code", "source": ["# Determine the number of batches in the complete dataset\n", "dataset_cardinality = complete_dataset.cardinality().numpy()\n", "print(f\"Dataset cardinality (number of batches): {dataset_cardinality}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "beMYtziXuh6r", "outputId": "b5d0ccaa-3e16-4bfc-d5cf-e2850d14b9f9"}, "execution_count": 19, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset cardinality (number of batches): 8\n"]}]}, {"cell_type": "code", "source": ["# Download our TF Dataset\n", "!zip -r ASLDataset.zip ASLDataset/"], "metadata": {"id": "WGnV_n8NfsBR", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "5602b18b-55a8-4558-de98-e22161bb1296"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  adding: ASLDataset/ (stored 0%)\n", "  adding: ASLDataset/9599301937257639596/ (stored 0%)\n", "  adding: ASLDataset/9599301937257639596/00000001.shard/ (stored 0%)\n", "  adding: ASLDataset/9599301937257639596/00000001.shard/00000000.snapshot (deflated 14%)\n", "  adding: ASLDataset/9599301937257639596/00000000.shard/ (stored 0%)\n", "  adding: ASLDataset/9599301937257639596/00000000.shard/00000000.snapshot (deflated 14%)\n", "  adding: ASLDataset/snapshot.metadata (stored 0%)\n", "  adding: ASLDataset/dataset_spec.pb (deflated 26%)\n", "  adding: ASLDataset/10506842217049778974/ (stored 0%)\n", "  adding: ASLDataset/10506842217049778974/00000001.shard/ (stored 0%)\n", "  adding: ASLDataset/10506842217049778974/00000001.shard/00000000.snapshot (deflated 14%)\n", "  adding: ASLDataset/10506842217049778974/00000000.shard/ (stored 0%)\n", "  adding: ASLDataset/10506842217049778974/00000000.shard/00000000.snapshot (deflated 14%)\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "Q3h66NBFmF0n"}, "execution_count": 20, "outputs": []}]}