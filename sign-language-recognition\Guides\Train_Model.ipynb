{"cells": [{"cell_type": "markdown", "metadata": {"id": "IH8BxN50U5JZ"}, "source": ["# Training our model"]}, {"cell_type": "markdown", "metadata": {"id": "FXu7Imn3eF0h"}, "source": ["## Download Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vBMxM1V-Y_0L"}, "outputs": [], "source": ["# Download TF Dataset into environment\n", "!wget -O ASLDataset.zip \"https://www.dropbox.com/scl/fi/1799mm7ovirghzpx3cnb1/ASLDataset.zip?rlkey=s8sjhss4ofs2r7k742ucxg8mf&dl=1\"\n", "!unzip -q ASLDataset.zip"]}, {"cell_type": "markdown", "metadata": {"id": "AKoLXH06eUFM"}, "source": ["## Defining Constants\n", "\n", "Before starting with the tasks, let's get familiar with the constants and their significance:\n", "\n", "- **`IMPORTANT_LANDMARKS`**: This list contains specific landmarks (points on the hands and face) that are crucial for recognizing sign language gestures. The numbers (e.g., 0, 9, 11) indicate the indices of these landmarks in the dataset.\n", "- **`DATASET_PATH`**: The location of the dataset. This path is where our dataset is stored and from where it will be loaded.\n", "- **`NUMBER_OF_SIGNS`**: The total number of unique sign language gestures included in the dataset. With our simplified dataset, this number is set to 5."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "emj5sfC5qXBA"}, "outputs": [], "source": ["# Constants for data and model configuration\n", "\n", "# Key landmarks for gesture recognition\n", "IMPORTANT_LANDMARKS = [0, 9, 11, 13, 14, 17, 117, 118, 119, 199, 346, 347, 348] + list(range(468, 543))\n", "\n", "# Dataset storage location\n", "DATASET_PATH = \"/content/ASLDataset\"\n", "\n", "# Number of unique gestures\n", "NUMBER_OF_SIGNS = 5"]}, {"cell_type": "markdown", "metadata": {"id": "aqCTEqI7bDnx"}, "source": ["### Task 1: Load and Preprocess Dataset\n", "\n", "In this task, our goal is to prepare the dataset for our sign language recognition model. This involves loading the dataset, selecting specific data points (landmarks), handling missing values, and finally, splitting the dataset into training and testing sets.\n", "\n", "#### **Code Explanation**:\n", "\n", "1. **Defining the Preprocessing Function**:\n", "\n", "   - **`def preprocess_data(data, labels):`**: This function prepares each batch of data for our model. The `data` parameter represents a batch of video frames, each containing several landmarks. The `labels` are the correct answers (signs) for each batch.\n", "\n", "2. **Selecting Important Landmarks**:\n", "\n", "   - **`processed_data = tf.gather(data, IMPORTANT_LANDMARKS, axis=2)`**: We extract only the landmarks defined in `IMPORTANT_LANDMARKS`. This focuses our model on the most relevant features. The `axis=2` specifies that landmarks are selected across the depth of our data, ensuring we get the right points from each frame.\n", "\n", "3. **Cleaning Missing Values**:\n", "\n", "   - **`processed_data = tf.where(tf.math.is_nan(processed_data), tf.zeros_like(processed_data), processed_data)`**: If any landmarks are missing (represented by NaN values), we replace them with zeros. This step is necessary for consistency in our data and avoid feeding invalid data to the model.\n", "\n", "4. **Reshaping Landmark Data**:\n", "\n", "   - The final step in preprocessing is to reshape the landmark data so it can be efficiently processed by our model. This is done by concatenating the `x`, `y`, and `z` coordinates of each landmark, forming a unified input structure.\n", "\n", "#### **Loading and Preprocessing the Dataset**:\n", "\n", "- **`dataset = tf.data.Dataset.load(DATASET_PATH)`**: Here, we load our dataset from a specified path.\n", "\n", "- **`dataset = dataset.map(preprocess_data)`**: We apply our `preprocess_data` function to each element of our dataset. This ensures all data is preprocessed uniformly, making it suitable for training and evaluation.\n", "\n", "#### **Splitting into Training and Validation Sets**:\n", "\n", "- **Training and Validation Datasets**: We split our dataset into two parts: one for training the model and another for validation. This is crucial for evaluating our model's performance on unseen data.\n", "\n", "  - **`val_ds = dataset.take(1).cache().prefetch(tf.data.AUTOTUNE)`**: Creates a validation dataset from the first part of our dataset. The `.cache()` and `.prefetch(tf.data.AUTOTUNE)` methods are used to optimize data loading, making it more efficient.\n", "\n", "  - **`train_ds = dataset.skip(1).cache().shuffle(20).prefetch(tf.data.AUTOTUNE)`**: Forms the training dataset by excluding the part used for validation. Shuffling the data helps in preventing the model from learning the order of the data, instead of learning the actual patterns.\n", "\n", "#### **Summary**:\n", "\n", "- Task 1 focuses on the initial steps of loading and preprocessing our dataset. It ensures that the data fed into our model is clean, relevant, and suitably formatted."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rAxvStXYbDSc"}, "outputs": [], "source": ["# Preprocess the data: select landmarks, replace missing values, and prepare for model\n", "def preprocess_data(data, labels):\n", "    # Select important landmarks\n", "    processed_data = tf.gather(data, IMPORTANT_LANDMARKS, axis=2)\n", "    # Replace missing values with 0\n", "    processed_data = tf.where(tf.math.is_nan(processed_data), tf.zeros_like(processed_data), processed_data)\n", "    # Reshape and return data\n", "    return tf.concat([processed_data[..., i] for i in range(3)], -1), labels\n", "\n", "# Load the dataset and apply preprocessing\n", "dataset = tf.data.Dataset.load(DATASET_PATH)\n", "dataset = dataset.map(preprocess_data)\n", "\n", "# Split the dataset into training and testing sets\n", "val_ds = dataset.take(1).cache().prefetch(tf.data.AUTOTUNE)\n", "train_ds = dataset.skip(1).cache().shuffle(20).prefetch(tf.data.AUTOTUNE)"]}, {"cell_type": "markdown", "metadata": {"id": "8vIC3Au9cafO"}, "source": ["### Task 2: Setup Training Callbacks\n", "\n", "In this task, we introduce an aspect of training neural networks efficiently: callbacks. Callbacks are tools in TensorFlow and Keras that allow us to monitor our model's performance during training and take specific actions based on those observations. They help in preventing overfitting, optimizing training time, and adjusting learning rates to achieve better performance.\n", "\n", "#### **Code Explanation**:\n", "\n", "1. **EarlyStopping Callback**:\n", "\n", "   - **`tf.keras.callbacks.EarlyStopping(monitor=\"val_accuracy\", patience=10, restore_best_weights=True)`**: This callback monitors the model's validation accuracy. If the validation accuracy stops improving for 10 consecutive epochs (`patience=10`), the training process will be halted. By setting `restore_best_weights=True`, the model will revert to the weights from the epoch with the highest validation accuracy. This mechanism helps in preventing overfitting—where the model performs well on the training data but poorly on unseen data.\n", "\n", "2. **ReduceLROnPlateau Callback**:\n", "\n", "   - **`tf.keras.callbacks.ReduceLROnPlateau(monitor=\"val_accuracy\", factor=0.5, patience=3)`**: This callback also monitors the model's validation accuracy. If the accuracy does not improve for three consecutive epochs (`patience=3`), the learning rate will be reduced by a factor of 0.5. Adjusting the learning rate is a strategy to escape local minima in the loss landscape and potentially improve model performance after a plateau has been reached.\n", "\n", "#### **Summary**:\n", "\n", "- Task 2 focuses on setting up callbacks to enhance the training process of our sign language recognition model. By integrating these callbacks, we can make the training process more efficient and effective, avoiding unnecessary computations and improving the model's generalization ability on unseen data."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_kBVG2kra6ta"}, "outputs": [], "source": ["# Callbacks\n", "callbacks = [\n", "    # Stops training when the validation accuracy stops improving\n", "    tf.keras.callbacks.EarlyStopping(monitor=\"val_accuracy\", patience=10, restore_best_weights=True),\n", "    # Reduces the learning rate when the validation accuracy plateaus\n", "    tf.keras.callbacks.ReduceLROnPlateau(monitor=\"val_accuracy\", factor=0.5, patience=3)\n", "]"]}, {"cell_type": "markdown", "metadata": {"id": "xREbSxjMd2Wo"}, "source": ["### Task 3: Define the Model Architecture\n", "\n", "In this task, we're going to build the neural network architecture for our sign language recognition model. This involves setting up the input, processing layers, and the output layer to classify the different signs.\n", "\n", "#### **Code Explanation**:\n", "\n", "1. **Input Layer**:\n", "\n", "   - **`input_layer = tf.keras.Input(shape=(None,3*len(IMPORTANT_LANDMARKS)), ragged=True, name=\"input_layer\")`**: The input layer is designed to accept a ragged tensor with a dynamic number of frames per video. Each frame contains a flattened list of the `x`, `y`, and `z` coordinates for the selected landmarks. This flexibility allows the model to handle videos of varying lengths.\n", "\n", "2. **<PERSON><PERSON> Layers with Normalization and Dropout**:\n", "\n", "   - **For Loop Over Units**: We iterate over a list of units (512 and 256) to create dense layers. Each dense layer is followed by layer normalization, a ReLU activation function, and dropout. This sequence of operations helps the model learn complex patterns while avoiding overfitting through regularization.\n", "   \n", "     - **Layer Normalization**: Normalizes the output of each dense layer, ensuring that the activation distribution remains consistent across different inputs.\n", "   \n", "     - **ReLU Activation**: Introduces non-linearity to the model, allowing it to learn more complex relationships between the input and output.\n", "   \n", "     - **Dropout**: Randomly sets input units to 0 with a frequency of 10% at each step during training, which helps prevent overfitting by making the network's neurons less sensitive to the weights of other neurons.\n", "\n", "3. **LSTM Layer for Sequential Data Processing**:\n", "\n", "   - **`sequence = layers.LSTM(250, name=\"lstm_layer\")(sequence)`**: Adds an LSTM (Long Short-Term Memory) layer with 250 units. LSTM layers are particularly suited for modeling sequences (like video frames) because they can maintain information in memory for long periods, making them ideal for understanding the temporal dynamics of sign language.\n", "\n", "4. **Output Layer for Classification**:\n", "\n", "   - **`output_layer = layers.Dense(NUMBER_OF_SIGNS, activation=\"softmax\", name=\"output_layer\")(sequence)`**: The final layer is a dense layer with a number of units equal to the number of signs we want to recognize (`NUMBER_OF_SIGNS`). It uses the softmax activation function to output a probability distribution over the sign classes, helping the model to classify which sign is being performed.\n", "\n", "#### **Model Definition**:\n", "\n", "- **`model = models.Model(inputs=input_layer, outputs=output_layer, name=\"sign_language_model\")`**: This line brings together the input and output layers to define the model.\n", "\n", "- **`model.summary()`**: Displays a summary of the model, including the layers and their shapes, which is helpful for understanding the architecture and ensuring everything is set up correctly.\n", "\n", "#### **Summary**:\n", "\n", "- Task 3 constructs the architecture of the sign language recognition model, layer by layer, from input to output. It strategically combines dense, normalization, dropout, and LSTM layers to process and learn from the sequential landmark data effectively. This structure is designed to capture both the spatial relationships between landmarks in individual frames and the temporal relationships across frames, which is required for recognizing sign language gestures."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HPXIg5JRd2wi"}, "outputs": [], "source": ["# Model architecture\n", "input_layer = tf.keras.Input(shape=(None,3*len(IMPORTANT_LANDMARKS)), ragged=True, name=\"input_layer\")\n", "\n", "# Applying Dense layers with normalization and dropout for regularization\n", "sequence = input_layer\n", "for units in [512, 256]:\n", "    sequence = layers.Dense(units)(sequence)\n", "    sequence = layers.LayerNormalization()(sequence)\n", "    sequence = layers.Activation(\"relu\")(sequence)\n", "    sequence = layers.Dropout(0.1)(sequence)\n", "\n", "# Adding LSTM layer for sequential data processing\n", "sequence = layers.LSTM(250, name=\"lstm_layer\")(sequence)\n", "\n", "# Output layer for classification\n", "output_layer = layers.Dense(NUMBER_OF_SIGNS, activation=\"softmax\", name=\"output_layer\")(sequence)\n", "\n", "# Define the model\n", "model = models.Model(inputs=input_layer, outputs=output_layer, name=\"sign_language_model\")\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {"id": "SzTxx_z8prAp"}, "source": []}, {"cell_type": "markdown", "metadata": {"id": "waPusHLBpuu2"}, "source": ["### Task 4: Compile the Model\n", "\n", "After defining the architecture of our sign language recognition model, the next step is to prepare it for training. This involves specifying the optimizer, loss function, and metrics to evaluate the model's performance.\n", "\n", "#### **Code Explanation**:\n", "\n", "1. **Learning Rate Schedule**:\n", "\n", "   - **`learning_rate_schedule = optimizers.schedules.PiecewiseConstantDecay([10, 15], [1e-3, 1e-4, 1e-5])`**: Before compiling the model, we define a learning rate schedule. This schedule adjusts the learning rate at specified epochs to optimize the training process. After 10 epochs, the learning rate drops from `1e-3` to `1e-4`, and after 15 epochs, it further reduces to `1e-5`. This strategy helps in fine-tuning the model as it converges, improving performance by taking smaller steps towards the minimum loss as training progresses.\n", "\n", "2. **Compiling the Model**:\n", "\n", "   - **`model.compile()`**: The compile method prepares the model for training. We specify the following parameters:\n", "     - **Optimizer**: `optimizers.<PERSON>(learning_rate=learning_rate_schedule)` uses the Adam optimizer with our predefined learning rate schedule. Adam is a popular choice for deep learning models due to its efficient computation and low memory requirement. It adapts the learning rate for each parameter, helping to find optimal weights faster.\n", "     - **Loss Function**: `loss=\"sparse_categorical_crossentropy\"` is used for models predicting multiple classes. It measures the difference between the distribution of the predicted probabilities and the true distribution, with the goal of minimizing this difference.\n", "     - **Metrics**: `metrics=[\"accuracy\", \"sparse_top_k_categorical_accuracy\"]` provides two metrics:\n", "       - **Accuracy**: The percentage of correctly predicted labels.\n", "       - **Sparse Top K Categorical Accuracy**: This metric checks if the true label is one of the top `k` predicted labels. It's useful for cases where the model's confidence is split among several classes, providing insight into whether the correct class was close to being predicted.\n", "\n", "#### **Summary**:\n", "\n", "- Task 4 focuses on compiling the model, a crucial step that sets up the model for efficient training. By defining a learning rate schedule, we ensure the model adapts its learning pace as it learns, optimizing the training process. The selection of the Adam optimizer and appropriate loss function, along with meaningful metrics, prepares our model to learn from the training data accurately and effectively. This setup aims to minimize loss and maximize accuracy, guiding the model toward better understanding and classifying sign language gestures."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Pnhqd5-TprY9"}, "outputs": [], "source": ["# Compile model and train\n", "learning_rate_schedule = optimizers.schedules.PiecewiseConstantDecay([10, 15], [1e-3, 1e-4, 1e-5])\n", "model.compile(optimizer=optimizers.<PERSON>(learning_rate=learning_rate_schedule),\n", "              loss=\"sparse_categorical_crossentropy\",\n", "              metrics=[\"accuracy\", \"sparse_top_k_categorical_accuracy\"])"]}, {"cell_type": "markdown", "metadata": {"id": "22QCVPn_p12R"}, "source": ["### Task 5: Train the Model\n", "\n", "The final step in developing our sign language recognition model is to train it with our prepared dataset. This process involves feeding the training data into the model, allowing it to learn and adjust its weights to minimize the loss function and improve its predictions.\n", "\n", "#### **Code Explanation**:\n", "\n", "1. **Model Training**:\n", "\n", "   - **`model.fit(train_ds, validation_data=val_ds, callbacks=callbacks, epochs=100)`**: This line is where the actual training happens. Let's break down the parameters:\n", "     - **`train_ds`**: The training dataset, which includes the features (video frames with landmarks) and labels (signs). The model learns from this data.\n", "     - **`validation_data=val_ds`**: The validation dataset is used to evaluate the model's performance on data it hasn't seen during training. This helps monitor for overfitting and ensure that the model generalizes well.\n", "     - **`callbacks=callbacks`**: Includes the callbacks we defined earlier (`EarlyStopping` and `ReduceLROnPlateau`). These are used to optimize the training process by stopping early if the model isn't improving and adjusting the learning rate based on the model's performance on the validation set.\n", "     - **`epochs=100`**: The number of times the entire training dataset is passed through the model. However, due to our early stopping callback, training may stop before reaching 100 epochs if no improvement is observed in the validation accuracy.\n", "\n", "#### **Summary**:\n", "\n", "- Task 5 is all about training the model to recognize sign language from video data. The training process is carefully monitored using validation data and optimized with callbacks to prevent overfitting and ensure efficient learning. After training, the model can be evaluated on new, unseen data to test its generalization capabilities and fine-tuned as necessary for improved performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kkKYYyY-p2UA"}, "outputs": [], "source": ["model.fit(train_ds, validation_data=val_ds, callbacks=callbacks, epochs=100)"]}, {"cell_type": "markdown", "metadata": {"id": "bxn4d1oXp_kj"}, "source": ["# **SOLUTION**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SCkKoqljp-7h"}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow.keras import layers, models, optimizers\n", "\n", "#Constants\n", "IMPORTANT_LANDMARKS = [0, 9, 11, 13, 14, 17, 117, 118, 119, 199, 346, 347, 348] + list(range(468, 543))\n", "DATASET_PATH = \"/content/ASLDataset\"\n", "NUMBER_OF_SIGNS = 250\n", "\n", "# Preprocess the data: select landmarks, replace missing values, and prepare for model\n", "def preprocess_data(data, labels):\n", "    # Select important landmarks\n", "    processed_data = tf.gather(data, IMPORTANT_LANDMARKS, axis=2)\n", "    # Replace missing values with 0\n", "    processed_data = tf.where(tf.math.is_nan(processed_data), tf.zeros_like(processed_data), processed_data)\n", "    # Reshape and return data\n", "    return tf.concat([processed_data[..., i] for i in range(3)], -1), labels\n", "\n", "# Load the dataset and apply preprocessing\n", "dataset = tf.data.Dataset.load(DATASET_PATH)\n", "dataset = dataset.map(preprocess_data)\n", "\n", "# Split the dataset into training and testing sets\n", "val_ds = dataset.take(1).cache().prefetch(tf.data.AUTOTUNE)\n", "train_ds = dataset.skip(1).cache().shuffle(20).prefetch(tf.data.AUTOTUNE)\n", "\n", "# Callbacks\n", "callbacks = [\n", "    # Stops training when the validation accuracy stops improving\n", "    tf.keras.callbacks.EarlyStopping(monitor=\"val_accuracy\", patience=10, restore_best_weights=True),\n", "    # Reduces the learning rate when the validation accuracy plateaus\n", "    tf.keras.callbacks.ReduceLROnPlateau(monitor=\"val_accuracy\", factor=0.5, patience=3)\n", "]\n", "\n", "# Model architecture\n", "input_layer = tf.keras.Input(shape=(None,3*len(IMPORTANT_LANDMARKS)), ragged=True, name=\"input_layer\")\n", "\n", "# Applying Dense layers with normalization and dropout for regularization\n", "sequence = input_layer\n", "for units in [512, 256]:\n", "    sequence = layers.Dense(units)(sequence)\n", "    sequence = layers.LayerNormalization()(sequence)\n", "    sequence = layers.Activation(\"relu\")(sequence)\n", "    sequence = layers.Dropout(0.1)(sequence)\n", "\n", "# Adding LSTM layer for sequential data processing\n", "sequence = layers.LSTM(250, name=\"lstm_layer\")(sequence)\n", "\n", "# Output layer for classification\n", "output_layer = layers.Dense(NUMBER_OF_SIGNS, activation=\"softmax\", name=\"output_layer\")(sequence)\n", "\n", "# Define the model\n", "model = models.Model(inputs=input_layer, outputs=output_layer, name=\"sign_language_model\")\n", "model.summary()\n", "\n", "# Compile model and train\n", "learning_rate_schedule = optimizers.schedules.PiecewiseConstantDecay([10, 15], [1e-3, 1e-4, 1e-5])\n", "model.compile(optimizer=optimizers.<PERSON>(learning_rate=learning_rate_schedule),\n", "              loss=\"sparse_categorical_crossentropy\",\n", "              metrics=[\"accuracy\", \"sparse_top_k_categorical_accuracy\"])\n", "\n", "model.fit(train_ds, validation_data=val_ds, callbacks=callbacks, epochs=100)"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}